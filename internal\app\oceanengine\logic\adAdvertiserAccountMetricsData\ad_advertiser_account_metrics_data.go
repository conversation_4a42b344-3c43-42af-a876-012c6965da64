// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-11-15 16:27:19
// 生成路径: internal/app/oceanengine/logic/ad_advertiser_account_metrics_data.go
// 生成人：cyao
// desc:广告账户的指标数据
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/gogf/gf/v2/os/gtime"
	channelDao "github.com/tiger1103/gfast/v3/internal/app/channel/dao"
	channelModel "github.com/tiger1103/gfast/v3/internal/app/channel/model"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model/entity"
	orderDao "github.com/tiger1103/gfast/v3/internal/app/order/dao"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	theaterModel "github.com/tiger1103/gfast/v3/internal/app/theater/model"
	"github.com/tiger1103/gfast/v3/library/advertiser"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/api"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"strings"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/dao"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdAdvertiserAccountMetricsData(New())
}

func New() service.IAdAdvertiserAccountMetricsData {
	return &sAdAdvertiserAccountMetricsData{}
}

type sAdAdvertiserAccountMetricsData struct{}

func (c *sAdAdvertiserAccountMetricsData) AdAdvertiserAccountReportDataSearch(ctx context.Context, req *model.AdAdvertiserAccountReportDataSearch) (listRes *model.AdAdvertiserAccountReportDataSearchRes, err error) {
	listRes = new(model.AdAdvertiserAccountReportDataSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdAdvertiserAccount.Ctx(ctx).As("b").
			//LeftJoin("ad_project_metrics_data", "a", "a.advertiser_id = b.advertiser_id").
			//LeftJoin("ad_advertiser_account_transactions", "c", "a.advertiser_id = c.advertiser_id and a.create_date = c.create_date").
			LeftJoin("sys_user", "u", "b.user_id = u.id")
		metricsDataJoinStr := "a.advertiser_id = b.advertiser_id "
		if req.StartTime != "" {
			//m = m.Where("a.create_date >= ?", req.StartTime)
			metricsDataJoinStr += "and a.create_date >= '" + req.StartTime + "'"
		}
		if req.EndTime != "" {
			metricsDataJoinStr += "and a.create_date <= '" + req.EndTime + "'"
			//m = m.Where("a.create_date <= ?", req.EndTime)
		}
		m = m.LeftJoin("ad_advertiser_account_metrics_data", "a", metricsDataJoinStr).
			LeftJoin("ad_advertiser_account_transactions", "c", "a.advertiser_id = c.advertiser_id and a.create_date = c.create_date")

		if req.DeptId > 0 {
			m = m.Where("u.dept_id = ?", req.DeptId)
		}
		if req.UserId > 0 {
			m = m.Where("u.id = ?", req.UserId)
		} else {
			userInfo := sysService.Context().GetLoginUser(ctx)
			userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
				LoginUserRes: &systemModel.LoginUserRes{
					Id:     userInfo.Id,
					DeptId: userInfo.DeptId,
				},
			})
			if !admin {
				m = m.WhereIn("u.id", userIds)
			}
		}
		if req.AccountId != "" {
			m = m.Where("b."+dao.AdAdvertiserAccount.Columns().AdvertiserId, req.AccountId)
		}
		if req.Company != "" {
			m = m.Where("b."+dao.AdAdvertiserAccount.Columns().AdvertiserCompany, req.Company)
		}
		if req.AccountName != "" {
			m = m.Where("b."+dao.AdAdvertiserAccount.Columns().AdvertiserNick, req.AccountName)
		}
		if req.AccountRemark != "" {
			m = m.Where("b."+dao.AdAdvertiserAccount.Columns().Remark, req.AccountRemark)
		}
		if len(req.AccountIds) > 0 {
			m = m.WhereIn("b."+dao.AdAdvertiserAccount.Columns().AdvertiserId, req.AccountIds)
		}
		if len(req.AccountNames) > 0 {
			m = m.WhereIn("b."+dao.AdAdvertiserAccount.Columns().AdvertiserNick, req.AccountNames)
		}
		if req.Keyword != "" {
			m = m.Where("b.advertiser_nick like ? or b.advertiser_id like ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
		}
		filedStr := make([]string, 0)
		filedSumStr := make([]string, 0)
		filedSumStr = append(filedSumStr, "any_value(b.balance) as balance")         // 账户余额
		filedSumStr = append(filedSumStr, "Sum(a.total_ad_up) as totalAdUp")         // 总IAA收入
		filedSumStr = append(filedSumStr, "Sum(a.total_amount) as totalAmount")      // 总充值金额
		filedSumStr = append(filedSumStr, "Sum(a.stat_cost) as statCost")            // 总消耗
		filedSumStr = append(filedSumStr, "Sum(a.stat_pay_amount) as statPayAmount") // 当日付费金额
		filedSumStr = append(filedSumStr, "Sum(a.show_cnt) as showCnt")              // 总展示数
		filedSumStr = append(filedSumStr, "Sum(a.active) as active")                 // 总激活数
		filedSumStr = append(filedSumStr, "Sum(a.register) as register")             // 总注册数

		//filedStr = append(filedStr, "a.create_date as createDate")
		filedStr = append(filedStr, "any_value(b.advertiser_id) as advertiserId")
		filedStr = append(filedStr, "any_value(b.advertiser_nick) as advertiserNick")
		filedStr = append(filedStr, "any_value(b.advertiser_company) as advertiserCompany")
		filedStr = append(filedStr, "any_value(b.auth_status) as authStatus")
		filedStr = append(filedStr, "any_value(u.user_name) as userName")
		filedStr = append(filedStr, "any_value(b.balance) as balance")
		filedStr = append(filedStr, "any_value(b.budget) as budget")           // 预算
		filedStr = append(filedStr, "any_value(b.budget_mode) as budgetMode")  // 预算mode
		filedStr = append(filedStr, "Sum(a.total_ad_up) as totalAdUp")         // IAA收入
		filedStr = append(filedStr, "Sum(a.total_amount) as totalAmount")      // 充值金额
		filedStr = append(filedStr, "Sum(a.stat_cost) as statCost")            // 消耗
		filedStr = append(filedStr, "Sum(a.stat_pay_amount) as statPayAmount") // 当日付费金额
		//filedStr = append(filedStr, "a.attribution_game_in_app_roi_1day as attributionGameInAppRoi1Day")      // 当日付费ROI
		filedStr = append(filedStr, "Sum(a.show_cnt) as showCnt")                             // 展示数
		filedStr = append(filedStr, "Sum(a.active) as active")                                // 激活
		filedStr = append(filedStr, "Sum(a.stat_cost)/Sum(a.active) as activeCost")           // 激活成本  总花费 / 激活
		filedStr = append(filedStr, "Sum(a.register) as register")                            // 注册数
		filedStr = append(filedStr, "Sum(a.stat_cost)/Sum(a.register) as activeRegisterCost") //注册成本  总花费 / 注册数
		// convertCnt 转化数
		filedStr = append(filedStr, "Sum(a.convert_cnt) as convertCnt") // 转化数
		//filedStr = append(filedStr, "a.active_register_cost as activeRegisterCost") //注册成本  总花费 / 注册数
		m2 := m
		m = m.Group("b.advertiser_id")
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "statCost desc"
		if req.OrderBy != "" {
			order = req.OrderBy + " " + req.OrderType
		}
		//var res []*model.AdAdvertiserAccountReportDataRes
		listRes.Summary = new(model.AdAdvertiserAccountReportDataSummary)
		err = m2.Fields(filedSumStr).Scan(&listRes.Summary)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		err = m.Page(req.PageNum, req.PageSize).Fields(filedStr).Order(order).Scan(&listRes.List)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		for i, item := range listRes.List {
			listRes.List[i].Roi = libUtils.DivideAndRound(item.TotalAmount+item.TotalAdUp, item.StatCost, 2, libUtils.RoundHalfEven)
			listRes.List[i].PayAmountRoi = libUtils.DivideAndRound(item.StatPayAmount*100, item.StatCost, 2, libUtils.RoundHalfEven)
			listRes.List[i].ActiveCost = libUtils.ToRound(item.ActiveCost, 2, libUtils.RoundHalfEven)
			listRes.List[i].ActiveRegisterCost = libUtils.ToRound(item.ActiveRegisterCost, 2, libUtils.RoundHalfEven)
			listRes.List[i].StatPayAmount = libUtils.ToRound(item.StatPayAmount, 2, libUtils.RoundHalfEven)
			listRes.List[i].StatCost = libUtils.ToRound(listRes.List[i].StatCost, 2, libUtils.RoundHalfEven)
		}
		// 计算数据
		listRes.Summary.StatCost = libUtils.ToRound(listRes.Summary.StatCost, 2, libUtils.RoundHalfEven)
		listRes.Summary.Roi = libUtils.DivideAndRound(listRes.Summary.TotalAmount+listRes.Summary.TotalAdUp, listRes.Summary.StatCost, 2, libUtils.RoundHalfEven)
		listRes.Summary.ActiveRegisterCost = libUtils.DivideAndRound(listRes.Summary.StatCost, float64(listRes.Summary.Register), 2, libUtils.RoundHalfEven)
		listRes.Summary.ActiveCost = libUtils.DivideAndRound(listRes.Summary.StatCost, float64(listRes.Summary.Active), 2, libUtils.RoundHalfEven)
		listRes.Summary.PayAmountRoi = libUtils.DivideAndRound(listRes.Summary.StatPayAmount*100, listRes.Summary.StatCost, 2, libUtils.RoundHalfEven)
		listRes.Summary.StatPayAmount = libUtils.ToRound(listRes.Summary.StatPayAmount, 2, libUtils.RoundHalfEven)

	})
	return
}

func (s *sAdAdvertiserAccountMetricsData) List(ctx context.Context, req *model.AdAdvertiserAccountMetricsDataSearchReq) (listRes *model.AdAdvertiserAccountMetricsDataSearchRes, err error) {
	listRes = new(model.AdAdvertiserAccountMetricsDataSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdAdvertiserAccountMetricsData.Ctx(ctx).WithAll()
		if req.AdvertiserId != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AdvertiserId+" = ?", req.AdvertiserId)
		}
		if req.StatCost != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().StatCost+" = ?", req.StatCost)
		}
		if req.ShowCnt != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().ShowCnt+" = ?", req.ShowCnt)
		}
		if req.CpmPlatform != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().CpmPlatform+" = ?", req.CpmPlatform)
		}
		if req.ClickCnt != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().ClickCnt+" = ?", req.ClickCnt)
		}
		if req.Ctr != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().Ctr+" = ?", req.Ctr)
		}
		if req.CpcPlatform != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().CpcPlatform+" = ?", req.CpcPlatform)
		}
		if req.ConvertCnt != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().ConvertCnt+" = ?", req.ConvertCnt)
		}
		if req.ConversionCost != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().ConversionCost+" = ?", req.ConversionCost)
		}
		if req.ConversionRate != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().ConversionRate+" = ?", req.ConversionRate)
		}
		if req.DeepConvertCnt != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().DeepConvertCnt+" = ?", req.DeepConvertCnt)
		}
		if req.DeepConvertCost != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().DeepConvertCost+" = ?", req.DeepConvertCost)
		}
		if req.DeepConvertRate != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().DeepConvertRate+" = ?", req.DeepConvertRate)
		}
		if req.AttributionConvertCnt != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionConvertCnt+" = ?", req.AttributionConvertCnt)
		}
		if req.AttributionConvertCost != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionConvertCost+" = ?", req.AttributionConvertCost)
		}
		if req.AttributionDeepConvertCnt != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionDeepConvertCnt+" = ?", req.AttributionDeepConvertCnt)
		}
		if req.AttributionDeepConvertCost != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionDeepConvertCost+" = ?", req.AttributionDeepConvertCost)
		}
		if req.PreConvertCount != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().PreConvertCount+" = ?", req.PreConvertCount)
		}
		if req.PreConvertCost != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().PreConvertCost+" = ?", req.PreConvertCost)
		}
		if req.PreConvertRate != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().PreConvertRate+" = ?", req.PreConvertRate)
		}
		if req.ClickStartCnt != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().ClickStartCnt+" = ?", req.ClickStartCnt)
		}
		if req.ClickStartCost != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().ClickStartCost+" = ?", req.ClickStartCost)
		}
		if req.ClickStartRate != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().ClickStartRate+" = ?", req.ClickStartRate)
		}
		if req.DownloadFinishCnt != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().DownloadFinishCnt+" = ?", req.DownloadFinishCnt)
		}
		if req.DownloadFinishCost != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().DownloadFinishCost+" = ?", req.DownloadFinishCost)
		}
		if req.DownloadFinishRate != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().DownloadFinishRate+" = ?", req.DownloadFinishRate)
		}
		if req.InstallFinishCnt != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().InstallFinishCnt+" = ?", req.InstallFinishCnt)
		}
		if req.InstallFinishCost != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().InstallFinishCost+" = ?", req.InstallFinishCost)
		}
		if req.InstallFinishRate != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().InstallFinishRate+" = ?", req.InstallFinishRate)
		}
		if req.Active != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().Active+" = ?", req.Active)
		}
		if req.ActiveCost != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().ActiveCost+" = ?", req.ActiveCost)
		}
		if req.ActiveRate != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().ActiveRate+" = ?", req.ActiveRate)
		}
		if req.ActiveRegisterCost != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().ActiveRegisterCost+" = ?", req.ActiveRegisterCost)
		}
		if req.ActiveRegisterRate != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().ActiveRegisterRate+" = ?", req.ActiveRegisterRate)
		}
		if req.GameAddiction != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().GameAddiction+" = ?", req.GameAddiction)
		}
		if req.GameAddictionCost != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().GameAddictionCost+" = ?", req.GameAddictionCost)
		}
		if req.GameAddictionRate != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().GameAddictionRate+" = ?", req.GameAddictionRate)
		}
		if req.AttributionNextDayOpenCnt != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionNextDayOpenCnt+" = ?", req.AttributionNextDayOpenCnt)
		}
		if req.AttributionNextDayOpenCost != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionNextDayOpenCost+" = ?", req.AttributionNextDayOpenCost)
		}
		if req.AttributionNextDayOpenRate != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionNextDayOpenRate+" = ?", req.AttributionNextDayOpenRate)
		}
		if req.NextDayOpen != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().NextDayOpen+" = ?", req.NextDayOpen)
		}
		if req.ActivePay != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().ActivePay+" = ?", req.ActivePay)
		}
		if req.ActivePayCost != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().ActivePayCost+" = ?", req.ActivePayCost)
		}
		if req.ActivePayRate != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().ActivePayRate+" = ?", req.ActivePayRate)
		}
		if req.GamePayCount != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().GamePayCount+" = ?", req.GamePayCount)
		}
		if req.GamePayCost != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().GamePayCost+" = ?", req.GamePayCost)
		}
		if req.AttributionGamePay7DCount != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionGamePay7DCount+" = ?", req.AttributionGamePay7DCount)
		}
		if req.AttributionGamePay7DCost != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionGamePay7DCost+" = ?", req.AttributionGamePay7DCost)
		}
		if req.AttributionActivePay7DPerCount != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionActivePay7DPerCount+" = ?", req.AttributionActivePay7DPerCount)
		}
		if req.InAppUv != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().InAppUv+" = ?", req.InAppUv)
		}
		if req.InAppDetailUv != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().InAppDetailUv+" = ?", req.InAppDetailUv)
		}
		if req.InAppCart != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().InAppCart+" = ?", req.InAppCart)
		}
		if req.InAppPay != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().InAppPay+" = ?", req.InAppPay)
		}
		if req.InAppOrder != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().InAppOrder+" = ?", req.InAppOrder)
		}
		if req.AttributionGameInAppLtv1Day != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionGameInAppLtv1Day+" = ?", req.AttributionGameInAppLtv1Day)
		}
		if req.AttributionGameInAppLtv2Days != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionGameInAppLtv2Days+" = ?", req.AttributionGameInAppLtv2Days)
		}
		if req.AttributionGameInAppLtv3Days != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionGameInAppLtv3Days+" = ?", req.AttributionGameInAppLtv3Days)
		}
		if req.AttributionGameInAppLtv4Days != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionGameInAppLtv4Days+" = ?", req.AttributionGameInAppLtv4Days)
		}
		if req.AttributionGameInAppLtv5Days != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionGameInAppLtv5Days+" = ?", req.AttributionGameInAppLtv5Days)
		}
		if req.AttributionGameInAppLtv6Days != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionGameInAppLtv6Days+" = ?", req.AttributionGameInAppLtv6Days)
		}
		if req.AttributionGameInAppLtv7Days != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionGameInAppLtv7Days+" = ?", req.AttributionGameInAppLtv7Days)
		}
		if req.AttributionGameInAppLtv8Days != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionGameInAppLtv8Days+" = ?", req.AttributionGameInAppLtv8Days)
		}
		if req.AttributionGameInAppRoi1Day != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionGameInAppRoi1Day+" = ?", req.AttributionGameInAppRoi1Day)
		}
		if req.AttributionGameInAppRoi2Days != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionGameInAppRoi2Days+" = ?", req.AttributionGameInAppRoi2Days)
		}
		if req.AttributionGameInAppRoi3Days != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionGameInAppRoi3Days+" = ?", req.AttributionGameInAppRoi3Days)
		}
		if req.AttributionGameInAppRoi4Days != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionGameInAppRoi4Days+" = ?", req.AttributionGameInAppRoi4Days)
		}
		if req.AttributionGameInAppRoi5Days != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionGameInAppRoi5Days+" = ?", req.AttributionGameInAppRoi5Days)
		}
		if req.AttributionGameInAppRoi6Days != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionGameInAppRoi6Days+" = ?", req.AttributionGameInAppRoi6Days)
		}
		if req.AttributionGameInAppRoi7Days != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionGameInAppRoi7Days+" = ?", req.AttributionGameInAppRoi7Days)
		}
		if req.AttributionGameInAppRoi8Days != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionGameInAppRoi8Days+" = ?", req.AttributionGameInAppRoi8Days)
		}
		if req.AttributionDayActivePayCount != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionDayActivePayCount+" = ?", req.AttributionDayActivePayCount)
		}
		if req.AttributionActivePayIntraOneDayCount != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionActivePayIntraOneDayCount+" = ?", req.AttributionActivePayIntraOneDayCount)
		}
		if req.AttributionActivePayIntraOneDayCost != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionActivePayIntraOneDayCost+" = ?", req.AttributionActivePayIntraOneDayCost)
		}
		if req.AttributionActivePayIntraOneDayRate != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionActivePayIntraOneDayRate+" = ?", req.AttributionActivePayIntraOneDayRate)
		}
		if req.AttributionActivePayIntraOneDayAmount != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionActivePayIntraOneDayAmount+" = ?", req.AttributionActivePayIntraOneDayAmount)
		}
		if req.AttributionActivePayIntraOneDayRoi != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionActivePayIntraOneDayRoi+" = ?", req.AttributionActivePayIntraOneDayRoi)
		}
		if req.AttributionRetention2DCnt != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionRetention2DCnt+" = ?", req.AttributionRetention2DCnt)
		}
		if req.AttributionRetention2DCost != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionRetention2DCost+" = ?", req.AttributionRetention2DCost)
		}
		if req.AttributionRetention2DRate != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionRetention2DRate+" = ?", req.AttributionRetention2DRate)
		}
		if req.AttributionRetention3DCnt != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionRetention3DCnt+" = ?", req.AttributionRetention3DCnt)
		}
		if req.AttributionRetention3DCost != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionRetention3DCost+" = ?", req.AttributionRetention3DCost)
		}
		if req.AttributionRetention3DRate != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionRetention3DRate+" = ?", req.AttributionRetention3DRate)
		}
		if req.AttributionRetention4DCnt != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionRetention4DCnt+" = ?", req.AttributionRetention4DCnt)
		}
		if req.AttributionRetention4DCost != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionRetention4DCost+" = ?", req.AttributionRetention4DCost)
		}
		if req.AttributionRetention4DRate != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionRetention4DRate+" = ?", req.AttributionRetention4DRate)
		}
		if req.AttributionRetention5DCnt != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionRetention5DCnt+" = ?", req.AttributionRetention5DCnt)
		}
		if req.AttributionRetention5DCost != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionRetention5DCost+" = ?", req.AttributionRetention5DCost)
		}
		if req.AttributionRetention5DRate != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionRetention5DRate+" = ?", req.AttributionRetention5DRate)
		}
		if req.AttributionRetention6DCnt != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionRetention6DCnt+" = ?", req.AttributionRetention6DCnt)
		}
		if req.AttributionRetention6DCost != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionRetention6DCost+" = ?", req.AttributionRetention6DCost)
		}
		if req.AttributionRetention6DRate != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionRetention6DRate+" = ?", req.AttributionRetention6DRate)
		}
		if req.AttributionRetention7DCnt != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionRetention7DCnt+" = ?", req.AttributionRetention7DCnt)
		}
		if req.AttributionRetention7DCost != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionRetention7DCost+" = ?", req.AttributionRetention7DCost)
		}
		if req.AttributionRetention7DRate != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionRetention7DRate+" = ?", req.AttributionRetention7DRate)
		}
		if req.AttributionRetention7DSumCnt != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionRetention7DSumCnt+" = ?", req.AttributionRetention7DSumCnt)
		}
		if req.AttributionRetention7DTotalCost != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionRetention7DTotalCost+" = ?", req.AttributionRetention7DTotalCost)
		}
		if req.TotalPlay != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().TotalPlay+" = ?", req.TotalPlay)
		}
		if req.ValidPlay != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().ValidPlay+" = ?", req.ValidPlay)
		}
		if req.ValidPlayCost != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().ValidPlayCost+" = ?", req.ValidPlayCost)
		}
		if req.ValidPlayRate != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().ValidPlayRate+" = ?", req.ValidPlayRate)
		}
		if req.Play25FeedBreak != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().Play25FeedBreak+" = ?", req.Play25FeedBreak)
		}
		if req.Play50FeedBreak != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().Play50FeedBreak+" = ?", req.Play50FeedBreak)
		}
		if req.Play75FeedBreak != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().Play75FeedBreak+" = ?", req.Play75FeedBreak)
		}
		if req.Play99FeedBreak != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().Play99FeedBreak+" = ?", req.Play99FeedBreak)
		}
		if req.AveragePlayTimePerPlay != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AveragePlayTimePerPlay+" = ?", req.AveragePlayTimePerPlay)
		}
		if req.PlayOverRate != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().PlayOverRate+" = ?", req.PlayOverRate)
		}
		if req.WifiPlayRate != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().WifiPlayRate+" = ?", req.WifiPlayRate)
		}
		if req.CardShow != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().CardShow+" = ?", req.CardShow)
		}
		if req.DyLike != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().DyLike+" = ?", req.DyLike)
		}
		if req.DyComment != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().DyComment+" = ?", req.DyComment)
		}
		if req.DyShare != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().DyShare+" = ?", req.DyShare)
		}
		if req.IesChallengeClick != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().IesChallengeClick+" = ?", req.IesChallengeClick)
		}
		if req.IesMusicClick != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().IesMusicClick+" = ?", req.IesMusicClick)
		}
		if req.LocationClick != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().LocationClick+" = ?", req.LocationClick)
		}
		if req.CustomerEffective != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().CustomerEffective+" = ?", req.CustomerEffective)
		}
		if req.Wechat != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().Wechat+" = ?", req.Wechat)
		}
		if req.AttributionMicroGame0DLtv != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionMicroGame0DLtv+" = ?", req.AttributionMicroGame0DLtv)
		}
		if req.AttributionMicroGame3DLtv != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionMicroGame3DLtv+" = ?", req.AttributionMicroGame3DLtv)
		}
		if req.AttributionMicroGame7DLtv != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionMicroGame7DLtv+" = ?", req.AttributionMicroGame7DLtv)
		}
		if req.AttributionMicroGame0DRoi != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionMicroGame0DRoi+" = ?", req.AttributionMicroGame0DRoi)
		}
		if req.AttributionMicroGame3DRoi != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionMicroGame3DRoi+" = ?", req.AttributionMicroGame3DRoi)
		}
		if req.AttributionMicroGame7DRoi != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().AttributionMicroGame7DRoi+" = ?", req.AttributionMicroGame7DRoi)
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().CreatedAt+" >=? AND "+dao.AdAdvertiserAccountMetricsData.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		if req.CreateDate != "" {
			m = m.Where(dao.AdAdvertiserAccountMetricsData.Columns().CreateDate+" = ?", req.CreateDate)
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdAdvertiserAccountMetricsDataListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdAdvertiserAccountMetricsDataListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.AdAdvertiserAccountMetricsDataListRes{
				Id:                                    v.Id,
				AdvertiserId:                          v.AdvertiserId,
				StatCost:                              v.StatCost,
				ShowCnt:                               v.ShowCnt,
				CpmPlatform:                           v.CpmPlatform,
				ClickCnt:                              v.ClickCnt,
				Ctr:                                   v.Ctr,
				CpcPlatform:                           v.CpcPlatform,
				ConvertCnt:                            v.ConvertCnt,
				ConversionCost:                        v.ConversionCost,
				ConversionRate:                        v.ConversionRate,
				DeepConvertCnt:                        v.DeepConvertCnt,
				DeepConvertCost:                       v.DeepConvertCost,
				DeepConvertRate:                       v.DeepConvertRate,
				AttributionConvertCnt:                 v.AttributionConvertCnt,
				AttributionConvertCost:                v.AttributionConvertCost,
				AttributionDeepConvertCnt:             v.AttributionDeepConvertCnt,
				AttributionDeepConvertCost:            v.AttributionDeepConvertCost,
				PreConvertCount:                       v.PreConvertCount,
				PreConvertCost:                        v.PreConvertCost,
				PreConvertRate:                        v.PreConvertRate,
				ClickStartCnt:                         v.ClickStartCnt,
				ClickStartCost:                        v.ClickStartCost,
				ClickStartRate:                        v.ClickStartRate,
				DownloadFinishCnt:                     v.DownloadFinishCnt,
				DownloadFinishCost:                    v.DownloadFinishCost,
				DownloadFinishRate:                    v.DownloadFinishRate,
				InstallFinishCnt:                      v.InstallFinishCnt,
				InstallFinishCost:                     v.InstallFinishCost,
				InstallFinishRate:                     v.InstallFinishRate,
				Active:                                v.Active,
				ActiveCost:                            v.ActiveCost,
				ActiveRate:                            v.ActiveRate,
				ActiveRegisterCost:                    v.ActiveRegisterCost,
				ActiveRegisterRate:                    v.ActiveRegisterRate,
				GameAddiction:                         v.GameAddiction,
				GameAddictionCost:                     v.GameAddictionCost,
				GameAddictionRate:                     v.GameAddictionRate,
				AttributionNextDayOpenCnt:             v.AttributionNextDayOpenCnt,
				AttributionNextDayOpenCost:            v.AttributionNextDayOpenCost,
				AttributionNextDayOpenRate:            v.AttributionNextDayOpenRate,
				NextDayOpen:                           v.NextDayOpen,
				ActivePay:                             v.ActivePay,
				ActivePayCost:                         v.ActivePayCost,
				ActivePayRate:                         v.ActivePayRate,
				GamePayCount:                          v.GamePayCount,
				GamePayCost:                           v.GamePayCost,
				AttributionGamePay7DCount:             v.AttributionGamePay7DCount,
				AttributionGamePay7DCost:              v.AttributionGamePay7DCost,
				AttributionActivePay7DPerCount:        v.AttributionActivePay7DPerCount,
				InAppUv:                               v.InAppUv,
				InAppDetailUv:                         v.InAppDetailUv,
				InAppCart:                             v.InAppCart,
				InAppPay:                              v.InAppPay,
				InAppOrder:                            v.InAppOrder,
				AttributionGameInAppLtv1Day:           v.AttributionGameInAppLtv1Day,
				AttributionGameInAppLtv2Days:          v.AttributionGameInAppLtv2Days,
				AttributionGameInAppLtv3Days:          v.AttributionGameInAppLtv3Days,
				AttributionGameInAppLtv4Days:          v.AttributionGameInAppLtv4Days,
				AttributionGameInAppLtv5Days:          v.AttributionGameInAppLtv5Days,
				AttributionGameInAppLtv6Days:          v.AttributionGameInAppLtv6Days,
				AttributionGameInAppLtv7Days:          v.AttributionGameInAppLtv7Days,
				AttributionGameInAppLtv8Days:          v.AttributionGameInAppLtv8Days,
				AttributionGameInAppRoi1Day:           v.AttributionGameInAppRoi1Day,
				AttributionGameInAppRoi2Days:          v.AttributionGameInAppRoi2Days,
				AttributionGameInAppRoi3Days:          v.AttributionGameInAppRoi3Days,
				AttributionGameInAppRoi4Days:          v.AttributionGameInAppRoi4Days,
				AttributionGameInAppRoi5Days:          v.AttributionGameInAppRoi5Days,
				AttributionGameInAppRoi6Days:          v.AttributionGameInAppRoi6Days,
				AttributionGameInAppRoi7Days:          v.AttributionGameInAppRoi7Days,
				AttributionGameInAppRoi8Days:          v.AttributionGameInAppRoi8Days,
				AttributionDayActivePayCount:          v.AttributionDayActivePayCount,
				AttributionActivePayIntraOneDayCount:  v.AttributionActivePayIntraOneDayCount,
				AttributionActivePayIntraOneDayCost:   v.AttributionActivePayIntraOneDayCost,
				AttributionActivePayIntraOneDayRate:   v.AttributionActivePayIntraOneDayRate,
				AttributionActivePayIntraOneDayAmount: v.AttributionActivePayIntraOneDayAmount,
				AttributionActivePayIntraOneDayRoi:    v.AttributionActivePayIntraOneDayRoi,
				AttributionRetention2DCnt:             v.AttributionRetention2DCnt,
				AttributionRetention2DCost:            v.AttributionRetention2DCost,
				AttributionRetention2DRate:            v.AttributionRetention2DRate,
				AttributionRetention3DCnt:             v.AttributionRetention3DCnt,
				AttributionRetention3DCost:            v.AttributionRetention3DCost,
				AttributionRetention3DRate:            v.AttributionRetention3DRate,
				AttributionRetention4DCnt:             v.AttributionRetention4DCnt,
				AttributionRetention4DCost:            v.AttributionRetention4DCost,
				AttributionRetention4DRate:            v.AttributionRetention4DRate,
				AttributionRetention5DCnt:             v.AttributionRetention5DCnt,
				AttributionRetention5DCost:            v.AttributionRetention5DCost,
				AttributionRetention5DRate:            v.AttributionRetention5DRate,
				AttributionRetention6DCnt:             v.AttributionRetention6DCnt,
				AttributionRetention6DCost:            v.AttributionRetention6DCost,
				AttributionRetention6DRate:            v.AttributionRetention6DRate,
				AttributionRetention7DCnt:             v.AttributionRetention7DCnt,
				AttributionRetention7DCost:            v.AttributionRetention7DCost,
				AttributionRetention7DRate:            v.AttributionRetention7DRate,
				AttributionRetention7DSumCnt:          v.AttributionRetention7DSumCnt,
				AttributionRetention7DTotalCost:       v.AttributionRetention7DTotalCost,
				TotalPlay:                             v.TotalPlay,
				ValidPlay:                             v.ValidPlay,
				ValidPlayCost:                         v.ValidPlayCost,
				ValidPlayRate:                         v.ValidPlayRate,
				Play25FeedBreak:                       v.Play25FeedBreak,
				Play50FeedBreak:                       v.Play50FeedBreak,
				Play75FeedBreak:                       v.Play75FeedBreak,
				Play99FeedBreak:                       v.Play99FeedBreak,
				AveragePlayTimePerPlay:                v.AveragePlayTimePerPlay,
				PlayOverRate:                          v.PlayOverRate,
				WifiPlayRate:                          v.WifiPlayRate,
				CardShow:                              v.CardShow,
				DyLike:                                v.DyLike,
				DyComment:                             v.DyComment,
				DyShare:                               v.DyShare,
				IesChallengeClick:                     v.IesChallengeClick,
				IesMusicClick:                         v.IesMusicClick,
				LocationClick:                         v.LocationClick,
				CustomerEffective:                     v.CustomerEffective,
				Wechat:                                v.Wechat,
				AttributionMicroGame0DLtv:             v.AttributionMicroGame0DLtv,
				AttributionMicroGame3DLtv:             v.AttributionMicroGame3DLtv,
				AttributionMicroGame7DLtv:             v.AttributionMicroGame7DLtv,
				AttributionMicroGame0DRoi:             v.AttributionMicroGame0DRoi,
				AttributionMicroGame3DRoi:             v.AttributionMicroGame3DRoi,
				AttributionMicroGame7DRoi:             v.AttributionMicroGame7DRoi,
				CreatedAt:                             v.CreatedAt,
				CreateDate:                            v.CreateDate,
			}
		}
	})
	return
}

// AdAdvertiserAccountReportStatTask 刷历史的报表数据
func (s *sAdAdvertiserAccountMetricsData) AdAdvertiserAccountReportStatTask(ctx context.Context, startTime string, endTime string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		innerContext, cancel := context.WithCancel(context.Background())
		defer cancel()
		for {
			if startTime > endTime {
				break
			}
			err1 := s.CreateAdAdvertiserAccountReportStat(innerContext, startTime)
			if err1 != nil {
				g.Log().Error(ctx, err1)
			}
			startTime = libUtils.PlusDays(startTime, 1)
		}
	})
	return
}

// AdAdvertiserAllMetricsReportStat 每日定时任务执行的更新数据的方法 可能比较耗时， 放在三点钟之后执行
func (s *sAdAdvertiserAccountMetricsData) AdAdvertiserAllMetricsReportStat(ctx context.Context, startTime string, endTime string) (err error) {
	libUtils.SafeGo(func() {
		s.AdAdvertiserAccountReportStatTask(ctx, startTime, endTime)
	})
	libUtils.SafeGo(func() {
		service.AdProjectMetricsData().AdAdvertiserProjectReportStatTask(ctx, startTime, endTime)
	})
	libUtils.SafeGo(func() {
		service.AdPromotionMetricsData().AdPromotionReportReportStatTask(ctx, startTime, endTime)
	})
	return err
}

// AdAdvertiserAccountReportSubTask 给订阅的用
func (s *sAdAdvertiserAccountMetricsData) AdAdvertiserAccountReportSubTask(ctx context.Context, adId string, statDate string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {

		list, innerError := s.GetAdAdvertiserAccountReportStat2(ctx, adId)
		if innerError != nil {
			return
		}
		if list == nil || len(list) == 0 {
			return
		}
		for _, item := range list {
			// 这个耗时较短在携程中进行处理
			go func() {
				innerError = s.CreateAdAdvertiserAccountTransactionsStat(ctx, item.AccessToken, item.AdvertiserId, statDate)
				if innerError != nil {
					g.Log().Errorf(ctx, "-- CreateAdAdvertiserAccountTransactionsStat error: %v -- ", innerError)
				}
			}()
			innerError = s.CreateAdAdvertiserAccountMetricsDataStat(ctx, item.AccessToken, item.AdvertiserId, statDate)
			if innerError != nil {
				g.Log().Errorf(ctx, "-- CreateAdAdvertiserAccountMetricsDataStat error: %v -- ", innerError)
			}
		}
	})
	return
}

// AdAdvertiserAccountReportSubTask2 给任务计划使用
func (s *sAdAdvertiserAccountMetricsData) AdAdvertiserAccountReportSubTask2(ctx context.Context, req []*model.AdAdvertiserAccountTaskListRes, statDate string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		for _, item := range req {
			// 这个耗时较短在携程中进行处理
			go func() {
				innerError := s.CreateAdAdvertiserAccountTransactionsStat(ctx, item.AccessToken, item.AdvertiserId, statDate)
				if innerError != nil {
					g.Log().Errorf(ctx, "-- CreateAdAdvertiserAccountTransactionsStat error: %v -- ", innerError)
				}
			}()
			innerError := s.CreateAdAdvertiserAccountMetricsDataStat(ctx, item.AccessToken, item.AdvertiserId, statDate)
			if innerError != nil {
				g.Log().Errorf(ctx, "-- CreateAdAdvertiserAccountMetricsDataStat error: %v -- ", innerError)
			}
		}
	})
	return
}

// GetAccountMetricsByPlanTask 根据执行计划获取指标相关数据
func (s *sAdAdvertiserAccountMetricsData) GetAccountMetricsByPlanTask(ctx context.Context, adIds, metricsName []string, TimeScope int) (list []*model.MetricsByPlanTaskRes) {
	startTime := libUtils.PlusDays(libUtils.GetNowDate(), -int64(TimeScope))
	endTime := libUtils.GetNowDate()

	m := dao.AdAdvertiserAccount.Ctx(ctx).As("b")
	if len(adIds) > 0 {
		m = m.WhereIn("b.advertiser_id", adIds)
	}
	metricsDataJoinStr := "a.advertiser_id = b.advertiser_id "
	if startTime != "" {
		metricsDataJoinStr += "and a.create_date >= '" + startTime + "'"
	}
	if endTime != "" {
		metricsDataJoinStr += "and a.create_date <= '" + endTime + "'"
	}
	m = m.LeftJoin("ad_advertiser_account_metrics_data", "a", metricsDataJoinStr)
	m = m.Group("b.advertiser_id")
	filedStr := make([]string, 0)
	jsonStr := make([]string, 0)
	// 动态构建Field
	filedStr = append(filedStr, " any_value(b.advertiser_id) as advertiserId")
	for _, metrics := range metricsName {
		if ok, jsonName := libUtils.CheckIsTableForField(metrics, entity.AdAdvertiserAccountMetricsData{}); ok {
			filedStr = append(filedStr, fmt.Sprintf("Sum(a.%s) as %s", metrics, jsonName))
			jsonStr = append(jsonStr, jsonName)
			// 字段较少暂时就写死
		} else if metrics == "balance" {
			filedStr = append(filedStr, fmt.Sprintf("any_value(b.%s) as %s", metrics, "balance"))
			jsonStr = append(jsonStr, "balance")
		} // todo 其他字段
	}
	var searchList []*model.AdAdvertiserAccountReportDataRes
	err := m.Fields(filedStr).Scan(&searchList)
	if err != nil {
		g.Log().Errorf(ctx, "GetAdAdvertiserAccountReportStat2 -> %+v", err)
		return
	}
	if len(searchList) > 0 {
		list = make([]*model.MetricsByPlanTaskRes, 0)
		for _, item := range searchList {
			keyValue, keyValue2, innerError := libUtils.GetFieldsByTag(item, "json", jsonStr)
			if innerError != nil {
				continue
			}
			list = append(list, &model.MetricsByPlanTaskRes{
				AdId:         item.AdvertiserId,
				ObjectId:     item.AdvertiserId,
				KeyValuePare: keyValue,
				KeyNameMap:   keyValue2,
			})
		}
	}
	return
}

func (s *sAdAdvertiserAccountMetricsData) GetAdAdvertiserAccountReportStat2(ctx context.Context, adId string) (res []*model.AdAdvertiserAccountTokenRes, err error) {
	res = make([]*model.AdAdvertiserAccountTokenRes, 0)
	err = dao.AdAdvertiserAccount.Ctx(ctx).As("a").LeftJoin("ad_majordomo_advertiser_account b", "a.parent_id = b.id").Where("a.advertiser_id = ?", adId).Order("a.advertiser_id").Fields(" a.advertiser_id , b.access_token").Scan(&res)
	return
}

func (s *sAdAdvertiserAccountMetricsData) GetAdAdvertiserAccountReportStat(ctx context.Context, pageNo int, pageSize int) (res []*model.AdAdvertiserAccountTokenRes, err error) {
	res = make([]*model.AdAdvertiserAccountTokenRes, 0)
	err = dao.AdAdvertiserAccount.Ctx(ctx).As("a").LeftJoin("ad_majordomo_advertiser_account b", "a.parent_id = b.id").Order("a.advertiser_id").Fields(" a.advertiser_id , b.access_token").Page(pageNo, pageSize).Scan(&res)
	return
}

// CreateAdAdvertiserAccountReportStat 刷历史数据 1.广告主账户报表数据 广告账户流水 和广告账户指标数据
func (s *sAdAdvertiserAccountMetricsData) CreateAdAdvertiserAccountReportStat(ctx context.Context, statDate string) (err error) {
	statDate = strings.ReplaceAll(statDate, "'", "")
	channelRechargeStatKey := commonConsts.AdAdvertiserAccountReport + statDate
	pool := goredis.NewPool(commonService.GetGoRedis())
	rs := redsync.New(pool)
	mutex := rs.NewMutex(channelRechargeStatKey, redsync.WithTries(1), redsync.WithExpiry(time.Second*20), redsync.WithRetryDelay(50*time.Millisecond))
	if err = mutex.TryLockContext(ctx); err != nil {
		g.Log().Info(ctx, "Redisson没有获取到分布式锁："+channelRechargeStatKey+", TaskName :ChannelStatTask ")
		return err
	}
	defer mutex.UnlockContext(ctx)
	err = g.Try(ctx, func(ctx context.Context) {
		var pageNum = 1
		var pageSize = 500
		for {
			// 查询 ad_advertiser_account 表格查询出有token 的数据 // ad_majordomo_advertiser_account
			list, innerError := s.GetAdAdvertiserAccountReportStat(ctx, pageNum, pageSize)
			if innerError != nil {
				return
			}
			if list == nil || len(list) == 0 {
				break
			}
			for _, item := range list {
				// 这个耗时较短在携程中进行处理
				go func() {
					innerError = s.CreateAdAdvertiserAccountTransactionsStat(ctx, item.AccessToken, item.AdvertiserId, statDate)
					if innerError != nil {
						g.Log().Errorf(ctx, "-- CreateAdAdvertiserAccountTransactionsStat error: %v -- ", innerError)
					}
				}()
				innerError = s.CreateAdAdvertiserAccountMetricsDataStat(ctx, item.AccessToken, item.AdvertiserId, statDate)
				if innerError != nil {
					g.Log().Errorf(ctx, "-- CreateAdAdvertiserAccountMetricsDataStat error: %v -- ", innerError)
				}
			}
			pageNum++
			time.Sleep(time.Millisecond * 100)
		}

	})

	return err
}

// CreateAdAdvertiserAccountMetricsDataStat  统计ad_advertiser_account_metrics_data
func (s *sAdAdvertiserAccountMetricsData) CreateAdAdvertiserAccountMetricsDataStat(ctx context.Context, token, adId string, statDate string) (err error) {
	// 根据结构体编写String[]
	fields := advertiser.GetMetrics()
	resp, err := advertiser.GetToutiaoApiClient().ReportAdvertiserGetV2ApiService.SetRequest(api.ApiOpenApi2ReportAdvertiserGetGetRequest{
		AdvertiserId: libUtils.StringToInt64(adId),
		StartDate:    statDate,
		EndDate:      statDate,
		Fields:       fields,
		Page:         1,
		PageSize:     50,
	}).AccessToken(token).Do()
	if err != nil {
		g.Log().Errorf(ctx, "------------- GetToutiaoApiClient().ReportAdvertiserGetV2ApiService.Do() ERROR err：%v,resp:%v  --------------------", err, resp)
		time.Sleep(time.Millisecond * 10)
		//多给一次机会补偿 ，有时候巨量接口抽风
		resp, err = advertiser.GetToutiaoApiClient().ReportAdvertiserGetV2ApiService.SetRequest(api.ApiOpenApi2ReportAdvertiserGetGetRequest{
			AdvertiserId: libUtils.StringToInt64(adId),
			StartDate:    statDate,
			EndDate:      statDate,
			Fields:       fields,
			Page:         1,
			PageSize:     50,
		}).AccessToken(token).Do()
		if err != nil {
			g.Log().Errorf(ctx, "-------------多给一次机会补偿 GetToutiaoApiClient().ReportAdvertiserGetV2ApiService.Do() ERROR err：%v,resp:%v  --------------------", err, resp)
		}
	}

	if resp != nil && resp.Data != nil && len(resp.Data.List) > 0 {
		for _, item := range resp.Data.List {
			if *item.Cost == 0 && *item.Active == 0 && *item.Click == 0 && *item.Register == 0 {
				resp.Data.List = nil
			}
		}
	}
	var rechargeAmountStat []theaterModel.SVideoStatisticsInfoRes
	// 统计当日总充值金额
	err = orderDao.OrderInfoAnalytic.Ctx(ctx).Where(orderDao.OrderInfoAnalytic.Columns().CreateDate, statDate).Where(orderDao.OrderInfoAnalytic.Columns().AdvertiserId, adId).Where("status = 2 and order_type = 1 ").
		Fields(" IFNULL(sum(total_money), 0.00) as rechargeAmount").Group(orderDao.OrderInfoAnalytic.Columns().CreateDate).Scan(&rechargeAmountStat)
	// 统计当日总Iaa 金额 暂时不考虑其他广告平台
	statList := make([]*channelModel.MMemberAdCallbackStatisticsInfoRes, 0)
	// 先查询广告id 下的所有项目id
	prmIdList := make([]model.AdPromotionIdInfo, 0)
	err = dao.AdPromotion.Ctx(ctx).Where(dao.AdPromotion.Columns().AdvertiserId, adId).Fields(dao.AdPromotion.Columns().PromotionId).Scan(&prmIdList)
	//fmt.Println(test)
	if err == nil {
		ids := make([]string, 0)
		for _, info := range prmIdList {
			ids = append(ids, info.PromotionId)
		}
		err = channelDao.MMemberAdCallbackAnalytic.Ctx(ctx).
			Where(channelDao.MMemberAdCallback.Columns().CreateDate, statDate).Where("promotion_id", ids).
			FieldSum("up", "up").
			FieldSum("ipu", "ipu").
			Group("create_date").
			Scan(&statList)
	} else {
		g.Log().Errorf(ctx, "查询广告id 下的所有项目id失败 erro:%v", err)
	}

	if resp != nil && resp.Data != nil && len(resp.Data.List) > 0 {
		for _, inner := range resp.Data.List {
			if *inner.AdvertiserId == libUtils.StringToInt64(adId) {
				thisModel := &model.AdAdvertiserAccountMetricsDataInfoRes{
					AdvertiserId:                          adId,
					StatCost:                              *inner.Cost,
					ShowCnt:                               *inner.Show,
					CpmPlatform:                           *inner.AvgShowCost,
					StatPayAmount:                         *inner.StatPayAmount,
					ClickCnt:                              *inner.Click,
					Ctr:                                   *inner.Ctr,
					CpcPlatform:                           *inner.AvgClickCost,
					ConvertCnt:                            *inner.Convert,
					ConversionCost:                        *inner.ConvertCost,
					ConversionRate:                        *inner.ConvertRate,
					DeepConvertCnt:                        *inner.DeepConvert,
					DeepConvertCost:                       *inner.DeepConvertCost,
					DeepConvertRate:                       *inner.DeepConvertRate,
					AttributionConvertCnt:                 *inner.AttributionConvert,
					AttributionConvertCost:                *inner.AttributionConvertCost,
					AttributionDeepConvertCnt:             *inner.AttributionDeepConvert,
					AttributionDeepConvertCost:            *inner.AttributionDeepConvertCost,
					PreConvertCount:                       *inner.PreConvertCount,
					PreConvertCost:                        float64(*inner.PreConvertCost),
					PreConvertRate:                        *inner.PreConvertRate,
					ClickStartCnt:                         *inner.DownloadStart,
					ClickStartCost:                        *inner.DownloadStartCost,
					ClickStartRate:                        *inner.DownloadStartRate,
					DownloadFinishCnt:                     *inner.DownloadFinish,
					DownloadFinishCost:                    *inner.DownloadFinishCost,
					DownloadFinishRate:                    *inner.DownloadFinishRate,
					InstallFinishCnt:                      *inner.InstallFinish,
					InstallFinishCost:                     *inner.InstallFinishCost,
					InstallFinishRate:                     *inner.InstallFinishRate,
					Active:                                *inner.Active,
					ActiveCost:                            *inner.ActiveCost,
					ActiveRate:                            *inner.ActiveRate,
					ActiveRegisterCost:                    *inner.ActiveRegisterCost,
					ActiveRegisterRate:                    *inner.ActiveRegisterRate,
					GameAddiction:                         *inner.GameAddiction,
					GameAddictionCost:                     *inner.GameAddictionCost,
					GameAddictionRate:                     *inner.GameAddictionRate,
					AttributionNextDayOpenCnt:             *inner.AttributionNextDayOpenCnt,
					AttributionNextDayOpenCost:            *inner.AttributionNextDayOpenCost,
					AttributionNextDayOpenRate:            *inner.AttributionNextDayOpenRate,
					NextDayOpen:                           *inner.NextDayOpen,
					ActivePay:                             *inner.PayCount,
					ActivePayCost:                         *inner.ActivePayCost,
					ActivePayRate:                         *inner.ActivePayRate,
					GamePayCount:                          *inner.GamePayCount,
					GamePayCost:                           float64(*inner.GamePayCost),
					AttributionGamePay7DCount:             *inner.AttributionGamePay7dCount,
					AttributionGamePay7DCost:              *inner.AttributionGamePay7dCost,
					AttributionActivePay7DPerCount:        *inner.AttributionActivePay7dPerCount,
					InAppUv:                               *inner.InAppUv,
					InAppDetailUv:                         *inner.InAppDetailUv,
					InAppCart:                             *inner.InAppCart,
					InAppPay:                              *inner.InAppPay,
					InAppOrder:                            *inner.InAppOrder,
					AttributionGameInAppLtv1Day:           *inner.AttributionGameInAppLtv1day,
					AttributionGameInAppLtv2Days:          *inner.AttributionGameInAppLtv2days,
					AttributionGameInAppLtv3Days:          *inner.AttributionGameInAppLtv3days,
					AttributionGameInAppLtv4Days:          *inner.AttributionGameInAppLtv4days,
					AttributionGameInAppLtv5Days:          *inner.AttributionGameInAppLtv5days,
					AttributionGameInAppLtv6Days:          *inner.AttributionGameInAppLtv6days,
					AttributionGameInAppLtv7Days:          *inner.AttributionGameInAppLtv7days,
					AttributionGameInAppLtv8Days:          *inner.AttributionGameInAppLtv8days,
					AttributionGameInAppRoi1Day:           *inner.AttributionGameInAppRoi1day,
					AttributionGameInAppRoi2Days:          *inner.AttributionGameInAppRoi2days,
					AttributionGameInAppRoi3Days:          *inner.AttributionGameInAppRoi3days,
					AttributionGameInAppRoi4Days:          *inner.AttributionGameInAppRoi4days,
					AttributionGameInAppRoi5Days:          *inner.AttributionGameInAppRoi5days,
					AttributionGameInAppRoi6Days:          *inner.AttributionGameInAppRoi6days,
					AttributionGameInAppRoi7Days:          *inner.AttributionGameInAppRoi7days,
					AttributionGameInAppRoi8Days:          *inner.AttributionGameInAppRoi8days,
					AttributionDayActivePayCount:          *inner.AttributionDayActivePayCount,
					AttributionActivePayIntraOneDayCount:  *inner.AttributionActivePayIntraOneDayCount,
					AttributionActivePayIntraOneDayCost:   *inner.AttributionActivePayIntraOneDayCost,
					AttributionActivePayIntraOneDayRate:   *inner.AttributionActivePayIntraOneDayRate,
					AttributionActivePayIntraOneDayAmount: *inner.AttributionActivePayIntraOneDayAmount,
					AttributionActivePayIntraOneDayRoi:    *inner.AttributionActivePayIntraOneDayRoi,
					AttributionRetention2DCnt:             *inner.AttributionRetention2dCnt,
					AttributionRetention2DCost:            *inner.AttributionRetention2dCost,
					AttributionRetention2DRate:            *inner.AttributionRetention2dRate,
					AttributionRetention3DCnt:             *inner.AttributionRetention3dCnt,
					AttributionRetention3DCost:            *inner.AttributionRetention3dCost,
					AttributionRetention3DRate:            *inner.AttributionRetention3dRate,
					AttributionRetention4DCnt:             *inner.AttributionRetention4dCnt,
					AttributionRetention4DCost:            *inner.AttributionRetention4dCost,
					AttributionRetention4DRate:            *inner.AttributionRetention4dRate,
					AttributionRetention5DCnt:             *inner.AttributionRetention5dCnt,
					AttributionRetention5DCost:            *inner.AttributionRetention5dCost,
					AttributionRetention5DRate:            *inner.AttributionRetention5dRate,
					AttributionRetention6DCnt:             *inner.AttributionRetention6dCnt,
					AttributionRetention6DCost:            *inner.AttributionRetention6dCost,
					AttributionRetention6DRate:            *inner.AttributionRetention6dRate,
					AttributionRetention7DCnt:             *inner.AttributionRetention7dCnt,
					AttributionRetention7DCost:            *inner.AttributionRetention7dCost,
					AttributionRetention7DRate:            *inner.AttributionRetention7dRate,
					AttributionRetention7DSumCnt:          *inner.AttributionRetention7dSumCnt,
					AttributionRetention7DTotalCost:       *inner.AttributionRetention7dTotalCost,
					TotalPlay:                             *inner.TotalPlay,
					ValidPlay:                             *inner.ValidPlay,
					ValidPlayCost:                         *inner.ValidPlayCost,
					ValidPlayRate:                         *inner.ValidPlayRate,
					Play25FeedBreak:                       *inner.Play25FeedBreak,
					Play50FeedBreak:                       *inner.Play50FeedBreak,
					Play75FeedBreak:                       *inner.Play75FeedBreak,
					Play99FeedBreak:                       *inner.Play100FeedBreak,
					AveragePlayTimePerPlay:                *inner.AveragePlayTimePerPlay,
					PlayOverRate:                          *inner.PlayOverRate,
					WifiPlayRate:                          *inner.WifiPlayRate,
					CardShow:                              *inner.CardShow,
					DyLike:                                *inner.Like,
					DyComment:                             *inner.Comment,
					DyShare:                               *inner.Share,
					IesChallengeClick:                     *inner.IesChallengeClick,
					IesMusicClick:                         *inner.IesMusicClick,
					LocationClick:                         *inner.LocationClick,
					CustomerEffective:                     *inner.CustomerEffective,
					Wechat:                                *inner.Wechat,
					AttributionMicroGame0DLtv:             *inner.AttributionMicroGame0dLtv,
					AttributionMicroGame3DLtv:             *inner.AttributionMicroGame3dLtv,
					AttributionMicroGame7DLtv:             *inner.AttributionMicroGame7dLtv,
					AttributionMicroGame0DRoi:             *inner.AttributionMicroGame0dRoi,
					AttributionMicroGame3DRoi:             *inner.AttributionMicroGame3dRoi,
					AttributionMicroGame7DRoi:             *inner.AttributionMicroGame7dRoi,
					CreateDate:                            statDate,
				}
				if len(rechargeAmountStat) > 0 {
					thisModel.TotalAmount = rechargeAmountStat[0].RechargeAmount
				}
				if len(statList) > 0 {
					thisModel.TotalAdUp = statList[0].Up
				}
				_, err = dao.AdAdvertiserAccountMetricsData.Ctx(ctx).Save(&thisModel)
				return
			}
		}
	}
	return err
}

// CreateAdAdvertiserAccountTransactionsStat  统计ad_advertiser_account_transactions 获取ad 每日流水数据
func (s *sAdAdvertiserAccountMetricsData) CreateAdAdvertiserAccountTransactionsStat(ctx context.Context, token, adId string, statDate string) (err error) {
	resp, err := advertiser.GetToutiaoApiClient().AdvertiserFundDailyStatV2ApiService.SetRequest(api.ApiOpenApi2AdvertiserFundDailyStatGetRequest{
		AdvertiserId: libUtils.StringToInt64(adId),
		StartDate:    statDate,
		EndDate:      statDate,
		Page:         1,
		PageSize:     50,
	}).AccessToken(token).Do()
	if err != nil {
		if strings.Contains(err.Error(), "Too many requests") {
			time.Sleep(1000 * time.Millisecond)
			// 重试一次
			resp, err = advertiser.GetToutiaoApiClient().AdvertiserFundDailyStatV2ApiService.SetRequest(api.ApiOpenApi2AdvertiserFundDailyStatGetRequest{
				AdvertiserId: libUtils.StringToInt64(adId),
				StartDate:    statDate,
				EndDate:      statDate,
				Page:         1,
				PageSize:     50,
			}).AccessToken(token).Do()
			if err != nil {
				g.Log().Errorf(ctx, "------------- GetToutiaoApiClient().AdvertiserFundDailyStatV2ApiService.Do() ERROR err：%v,resp:%v  --------------------", err, resp)
			}
		}

	}
	if resp != nil && resp.Data != nil && len(resp.Data.List) > 0 {
		for _, inner := range resp.Data.List {
			if *inner.AdvertiserId == libUtils.StringToInt64(adId) {
				thisModel := &model.AdAdvertiserAccountTransactionsInfoRes{
					AdvertiserId:      adId,
					CreateDate:        statDate,
					Balance:           *inner.Balance,
					CashCost:          *inner.CashCost,
					CompanyWalletCost: *inner.CompanyWalletCost,
					Cost:              *inner.Cost,
					Frozen:            *inner.Frozen,
					GrantBalance:      *inner.GrantBalance,
					Income:            *inner.Income,
					NonGrantBalance:   *inner.NonGrantBalance,
					RewardCost:        *inner.RewardCost,
					SharedWalletCost:  *inner.SharedWalletCost,
					TransferIn:        *inner.TransferIn,
					TransferOut:       *inner.TransferOut,
					UpdatedAt:         gtime.Now(),
				}
				_, err = dao.AdAdvertiserAccountTransactions.Ctx(ctx).Save(&thisModel)
				return
			}
		}
	}

	return err
}

func (s *sAdAdvertiserAccountMetricsData) GetById(ctx context.Context, id int) (res *model.AdAdvertiserAccountMetricsDataInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdAdvertiserAccountMetricsData.Ctx(ctx).WithAll().Where(dao.AdAdvertiserAccountMetricsData.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdAdvertiserAccountMetricsData) Add(ctx context.Context, req *model.AdAdvertiserAccountMetricsDataAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdAdvertiserAccountMetricsData.Ctx(ctx).Insert(do.AdAdvertiserAccountMetricsData{
			AdvertiserId:                          req.AdvertiserId,
			StatCost:                              req.StatCost,
			ShowCnt:                               req.ShowCnt,
			CpmPlatform:                           req.CpmPlatform,
			ClickCnt:                              req.ClickCnt,
			Ctr:                                   req.Ctr,
			CpcPlatform:                           req.CpcPlatform,
			ConvertCnt:                            req.ConvertCnt,
			ConversionCost:                        req.ConversionCost,
			ConversionRate:                        req.ConversionRate,
			DeepConvertCnt:                        req.DeepConvertCnt,
			DeepConvertCost:                       req.DeepConvertCost,
			DeepConvertRate:                       req.DeepConvertRate,
			AttributionConvertCnt:                 req.AttributionConvertCnt,
			AttributionConvertCost:                req.AttributionConvertCost,
			AttributionDeepConvertCnt:             req.AttributionDeepConvertCnt,
			AttributionDeepConvertCost:            req.AttributionDeepConvertCost,
			PreConvertCount:                       req.PreConvertCount,
			PreConvertCost:                        req.PreConvertCost,
			PreConvertRate:                        req.PreConvertRate,
			ClickStartCnt:                         req.ClickStartCnt,
			ClickStartCost:                        req.ClickStartCost,
			ClickStartRate:                        req.ClickStartRate,
			DownloadFinishCnt:                     req.DownloadFinishCnt,
			DownloadFinishCost:                    req.DownloadFinishCost,
			DownloadFinishRate:                    req.DownloadFinishRate,
			InstallFinishCnt:                      req.InstallFinishCnt,
			InstallFinishCost:                     req.InstallFinishCost,
			InstallFinishRate:                     req.InstallFinishRate,
			Active:                                req.Active,
			ActiveCost:                            req.ActiveCost,
			ActiveRate:                            req.ActiveRate,
			ActiveRegisterCost:                    req.ActiveRegisterCost,
			ActiveRegisterRate:                    req.ActiveRegisterRate,
			GameAddiction:                         req.GameAddiction,
			GameAddictionCost:                     req.GameAddictionCost,
			GameAddictionRate:                     req.GameAddictionRate,
			AttributionNextDayOpenCnt:             req.AttributionNextDayOpenCnt,
			AttributionNextDayOpenCost:            req.AttributionNextDayOpenCost,
			AttributionNextDayOpenRate:            req.AttributionNextDayOpenRate,
			NextDayOpen:                           req.NextDayOpen,
			ActivePay:                             req.ActivePay,
			ActivePayCost:                         req.ActivePayCost,
			ActivePayRate:                         req.ActivePayRate,
			GamePayCount:                          req.GamePayCount,
			GamePayCost:                           req.GamePayCost,
			AttributionGamePay7DCount:             req.AttributionGamePay7DCount,
			AttributionGamePay7DCost:              req.AttributionGamePay7DCost,
			AttributionActivePay7DPerCount:        req.AttributionActivePay7DPerCount,
			InAppUv:                               req.InAppUv,
			InAppDetailUv:                         req.InAppDetailUv,
			InAppCart:                             req.InAppCart,
			InAppPay:                              req.InAppPay,
			InAppOrder:                            req.InAppOrder,
			AttributionGameInAppLtv1Day:           req.AttributionGameInAppLtv1Day,
			AttributionGameInAppLtv2Days:          req.AttributionGameInAppLtv2Days,
			AttributionGameInAppLtv3Days:          req.AttributionGameInAppLtv3Days,
			AttributionGameInAppLtv4Days:          req.AttributionGameInAppLtv4Days,
			AttributionGameInAppLtv5Days:          req.AttributionGameInAppLtv5Days,
			AttributionGameInAppLtv6Days:          req.AttributionGameInAppLtv6Days,
			AttributionGameInAppLtv7Days:          req.AttributionGameInAppLtv7Days,
			AttributionGameInAppLtv8Days:          req.AttributionGameInAppLtv8Days,
			AttributionGameInAppRoi1Day:           req.AttributionGameInAppRoi1Day,
			AttributionGameInAppRoi2Days:          req.AttributionGameInAppRoi2Days,
			AttributionGameInAppRoi3Days:          req.AttributionGameInAppRoi3Days,
			AttributionGameInAppRoi4Days:          req.AttributionGameInAppRoi4Days,
			AttributionGameInAppRoi5Days:          req.AttributionGameInAppRoi5Days,
			AttributionGameInAppRoi6Days:          req.AttributionGameInAppRoi6Days,
			AttributionGameInAppRoi7Days:          req.AttributionGameInAppRoi7Days,
			AttributionGameInAppRoi8Days:          req.AttributionGameInAppRoi8Days,
			AttributionDayActivePayCount:          req.AttributionDayActivePayCount,
			AttributionActivePayIntraOneDayCount:  req.AttributionActivePayIntraOneDayCount,
			AttributionActivePayIntraOneDayCost:   req.AttributionActivePayIntraOneDayCost,
			AttributionActivePayIntraOneDayRate:   req.AttributionActivePayIntraOneDayRate,
			AttributionActivePayIntraOneDayAmount: req.AttributionActivePayIntraOneDayAmount,
			AttributionActivePayIntraOneDayRoi:    req.AttributionActivePayIntraOneDayRoi,
			AttributionRetention2DCnt:             req.AttributionRetention2DCnt,
			AttributionRetention2DCost:            req.AttributionRetention2DCost,
			AttributionRetention2DRate:            req.AttributionRetention2DRate,
			AttributionRetention3DCnt:             req.AttributionRetention3DCnt,
			AttributionRetention3DCost:            req.AttributionRetention3DCost,
			AttributionRetention3DRate:            req.AttributionRetention3DRate,
			AttributionRetention4DCnt:             req.AttributionRetention4DCnt,
			AttributionRetention4DCost:            req.AttributionRetention4DCost,
			AttributionRetention4DRate:            req.AttributionRetention4DRate,
			AttributionRetention5DCnt:             req.AttributionRetention5DCnt,
			AttributionRetention5DCost:            req.AttributionRetention5DCost,
			AttributionRetention5DRate:            req.AttributionRetention5DRate,
			AttributionRetention6DCnt:             req.AttributionRetention6DCnt,
			AttributionRetention6DCost:            req.AttributionRetention6DCost,
			AttributionRetention6DRate:            req.AttributionRetention6DRate,
			AttributionRetention7DCnt:             req.AttributionRetention7DCnt,
			AttributionRetention7DCost:            req.AttributionRetention7DCost,
			AttributionRetention7DRate:            req.AttributionRetention7DRate,
			AttributionRetention7DSumCnt:          req.AttributionRetention7DSumCnt,
			AttributionRetention7DTotalCost:       req.AttributionRetention7DTotalCost,
			TotalPlay:                             req.TotalPlay,
			ValidPlay:                             req.ValidPlay,
			ValidPlayCost:                         req.ValidPlayCost,
			ValidPlayRate:                         req.ValidPlayRate,
			Play25FeedBreak:                       req.Play25FeedBreak,
			Play50FeedBreak:                       req.Play50FeedBreak,
			Play75FeedBreak:                       req.Play75FeedBreak,
			Play99FeedBreak:                       req.Play99FeedBreak,
			AveragePlayTimePerPlay:                req.AveragePlayTimePerPlay,
			PlayOverRate:                          req.PlayOverRate,
			WifiPlayRate:                          req.WifiPlayRate,
			CardShow:                              req.CardShow,
			DyLike:                                req.DyLike,
			DyComment:                             req.DyComment,
			DyShare:                               req.DyShare,
			IesChallengeClick:                     req.IesChallengeClick,
			IesMusicClick:                         req.IesMusicClick,
			LocationClick:                         req.LocationClick,
			CustomerEffective:                     req.CustomerEffective,
			Wechat:                                req.Wechat,
			AttributionMicroGame0DLtv:             req.AttributionMicroGame0DLtv,
			AttributionMicroGame3DLtv:             req.AttributionMicroGame3DLtv,
			AttributionMicroGame7DLtv:             req.AttributionMicroGame7DLtv,
			AttributionMicroGame0DRoi:             req.AttributionMicroGame0DRoi,
			AttributionMicroGame3DRoi:             req.AttributionMicroGame3DRoi,
			AttributionMicroGame7DRoi:             req.AttributionMicroGame7DRoi,
			CreateDate:                            req.CreateDate,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdAdvertiserAccountMetricsData) Edit(ctx context.Context, req *model.AdAdvertiserAccountMetricsDataEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdAdvertiserAccountMetricsData.Ctx(ctx).WherePri(req.Id).Update(do.AdAdvertiserAccountMetricsData{
			AdvertiserId:                          req.AdvertiserId,
			StatCost:                              req.StatCost,
			ShowCnt:                               req.ShowCnt,
			CpmPlatform:                           req.CpmPlatform,
			ClickCnt:                              req.ClickCnt,
			Ctr:                                   req.Ctr,
			CpcPlatform:                           req.CpcPlatform,
			ConvertCnt:                            req.ConvertCnt,
			ConversionCost:                        req.ConversionCost,
			ConversionRate:                        req.ConversionRate,
			DeepConvertCnt:                        req.DeepConvertCnt,
			DeepConvertCost:                       req.DeepConvertCost,
			DeepConvertRate:                       req.DeepConvertRate,
			AttributionConvertCnt:                 req.AttributionConvertCnt,
			AttributionConvertCost:                req.AttributionConvertCost,
			AttributionDeepConvertCnt:             req.AttributionDeepConvertCnt,
			AttributionDeepConvertCost:            req.AttributionDeepConvertCost,
			PreConvertCount:                       req.PreConvertCount,
			PreConvertCost:                        req.PreConvertCost,
			PreConvertRate:                        req.PreConvertRate,
			ClickStartCnt:                         req.ClickStartCnt,
			ClickStartCost:                        req.ClickStartCost,
			ClickStartRate:                        req.ClickStartRate,
			DownloadFinishCnt:                     req.DownloadFinishCnt,
			DownloadFinishCost:                    req.DownloadFinishCost,
			DownloadFinishRate:                    req.DownloadFinishRate,
			InstallFinishCnt:                      req.InstallFinishCnt,
			InstallFinishCost:                     req.InstallFinishCost,
			InstallFinishRate:                     req.InstallFinishRate,
			Active:                                req.Active,
			ActiveCost:                            req.ActiveCost,
			ActiveRate:                            req.ActiveRate,
			ActiveRegisterCost:                    req.ActiveRegisterCost,
			ActiveRegisterRate:                    req.ActiveRegisterRate,
			GameAddiction:                         req.GameAddiction,
			GameAddictionCost:                     req.GameAddictionCost,
			GameAddictionRate:                     req.GameAddictionRate,
			AttributionNextDayOpenCnt:             req.AttributionNextDayOpenCnt,
			AttributionNextDayOpenCost:            req.AttributionNextDayOpenCost,
			AttributionNextDayOpenRate:            req.AttributionNextDayOpenRate,
			NextDayOpen:                           req.NextDayOpen,
			ActivePay:                             req.ActivePay,
			ActivePayCost:                         req.ActivePayCost,
			ActivePayRate:                         req.ActivePayRate,
			GamePayCount:                          req.GamePayCount,
			GamePayCost:                           req.GamePayCost,
			AttributionGamePay7DCount:             req.AttributionGamePay7DCount,
			AttributionGamePay7DCost:              req.AttributionGamePay7DCost,
			AttributionActivePay7DPerCount:        req.AttributionActivePay7DPerCount,
			InAppUv:                               req.InAppUv,
			InAppDetailUv:                         req.InAppDetailUv,
			InAppCart:                             req.InAppCart,
			InAppPay:                              req.InAppPay,
			InAppOrder:                            req.InAppOrder,
			AttributionGameInAppLtv1Day:           req.AttributionGameInAppLtv1Day,
			AttributionGameInAppLtv2Days:          req.AttributionGameInAppLtv2Days,
			AttributionGameInAppLtv3Days:          req.AttributionGameInAppLtv3Days,
			AttributionGameInAppLtv4Days:          req.AttributionGameInAppLtv4Days,
			AttributionGameInAppLtv5Days:          req.AttributionGameInAppLtv5Days,
			AttributionGameInAppLtv6Days:          req.AttributionGameInAppLtv6Days,
			AttributionGameInAppLtv7Days:          req.AttributionGameInAppLtv7Days,
			AttributionGameInAppLtv8Days:          req.AttributionGameInAppLtv8Days,
			AttributionGameInAppRoi1Day:           req.AttributionGameInAppRoi1Day,
			AttributionGameInAppRoi2Days:          req.AttributionGameInAppRoi2Days,
			AttributionGameInAppRoi3Days:          req.AttributionGameInAppRoi3Days,
			AttributionGameInAppRoi4Days:          req.AttributionGameInAppRoi4Days,
			AttributionGameInAppRoi5Days:          req.AttributionGameInAppRoi5Days,
			AttributionGameInAppRoi6Days:          req.AttributionGameInAppRoi6Days,
			AttributionGameInAppRoi7Days:          req.AttributionGameInAppRoi7Days,
			AttributionGameInAppRoi8Days:          req.AttributionGameInAppRoi8Days,
			AttributionDayActivePayCount:          req.AttributionDayActivePayCount,
			AttributionActivePayIntraOneDayCount:  req.AttributionActivePayIntraOneDayCount,
			AttributionActivePayIntraOneDayCost:   req.AttributionActivePayIntraOneDayCost,
			AttributionActivePayIntraOneDayRate:   req.AttributionActivePayIntraOneDayRate,
			AttributionActivePayIntraOneDayAmount: req.AttributionActivePayIntraOneDayAmount,
			AttributionActivePayIntraOneDayRoi:    req.AttributionActivePayIntraOneDayRoi,
			AttributionRetention2DCnt:             req.AttributionRetention2DCnt,
			AttributionRetention2DCost:            req.AttributionRetention2DCost,
			AttributionRetention2DRate:            req.AttributionRetention2DRate,
			AttributionRetention3DCnt:             req.AttributionRetention3DCnt,
			AttributionRetention3DCost:            req.AttributionRetention3DCost,
			AttributionRetention3DRate:            req.AttributionRetention3DRate,
			AttributionRetention4DCnt:             req.AttributionRetention4DCnt,
			AttributionRetention4DCost:            req.AttributionRetention4DCost,
			AttributionRetention4DRate:            req.AttributionRetention4DRate,
			AttributionRetention5DCnt:             req.AttributionRetention5DCnt,
			AttributionRetention5DCost:            req.AttributionRetention5DCost,
			AttributionRetention5DRate:            req.AttributionRetention5DRate,
			AttributionRetention6DCnt:             req.AttributionRetention6DCnt,
			AttributionRetention6DCost:            req.AttributionRetention6DCost,
			AttributionRetention6DRate:            req.AttributionRetention6DRate,
			AttributionRetention7DCnt:             req.AttributionRetention7DCnt,
			AttributionRetention7DCost:            req.AttributionRetention7DCost,
			AttributionRetention7DRate:            req.AttributionRetention7DRate,
			AttributionRetention7DSumCnt:          req.AttributionRetention7DSumCnt,
			AttributionRetention7DTotalCost:       req.AttributionRetention7DTotalCost,
			TotalPlay:                             req.TotalPlay,
			ValidPlay:                             req.ValidPlay,
			ValidPlayCost:                         req.ValidPlayCost,
			ValidPlayRate:                         req.ValidPlayRate,
			Play25FeedBreak:                       req.Play25FeedBreak,
			Play50FeedBreak:                       req.Play50FeedBreak,
			Play75FeedBreak:                       req.Play75FeedBreak,
			Play99FeedBreak:                       req.Play99FeedBreak,
			AveragePlayTimePerPlay:                req.AveragePlayTimePerPlay,
			PlayOverRate:                          req.PlayOverRate,
			WifiPlayRate:                          req.WifiPlayRate,
			CardShow:                              req.CardShow,
			DyLike:                                req.DyLike,
			DyComment:                             req.DyComment,
			DyShare:                               req.DyShare,
			IesChallengeClick:                     req.IesChallengeClick,
			IesMusicClick:                         req.IesMusicClick,
			LocationClick:                         req.LocationClick,
			CustomerEffective:                     req.CustomerEffective,
			Wechat:                                req.Wechat,
			AttributionMicroGame0DLtv:             req.AttributionMicroGame0DLtv,
			AttributionMicroGame3DLtv:             req.AttributionMicroGame3DLtv,
			AttributionMicroGame7DLtv:             req.AttributionMicroGame7DLtv,
			AttributionMicroGame0DRoi:             req.AttributionMicroGame0DRoi,
			AttributionMicroGame3DRoi:             req.AttributionMicroGame3DRoi,
			AttributionMicroGame7DRoi:             req.AttributionMicroGame7DRoi,
			CreateDate:                            req.CreateDate,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdAdvertiserAccountMetricsData) Delete(ctx context.Context, ids []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdAdvertiserAccountMetricsData.Ctx(ctx).Delete(dao.AdAdvertiserAccountMetricsData.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

// AccountSubjectDataStatistics 账户主体数据统计
func (s *sAdAdvertiserAccountMetricsData) AccountSubjectDataStatistics(ctx context.Context, req *model.AccountSubjectDataStatisticsReq) (listRes *model.AccountSubjectDataStatisticsRes, err error) {
	listRes = new(model.AccountSubjectDataStatisticsRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdAdvertiserAccountMetricsData.Ctx(ctx).As("a").
			LeftJoin("ad_advertiser_account", "b", "a.advertiser_id = b.advertiser_id").
			LeftJoin("sys_user u", "b.user_id = u.id")
		if len(req.UserIds) > 0 {
			m = m.WhereIn("b.user_id", req.UserIds)
		} else {
			userInfo := sysService.Context().GetLoginUser(ctx)
			userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
				LoginUserRes: &systemModel.LoginUserRes{
					Id:     userInfo.Id,
					DeptId: userInfo.DeptId,
				},
			})
			if !admin && len(userIds) > 0 {
				m = m.WhereIn("b.user_id", userIds)
			}
		}
		if len(req.AdvertiserCompanies) > 0 {
			m = m.WhereIn("b.advertiser_company", req.AdvertiserCompanies)
		}
		if req.StartTime != "" {
			m = m.WhereGTE("a.create_date", req.StartTime)
		}
		if req.EndTime != "" {
			m = m.WhereLTE("a.create_date", req.EndTime)
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		var groupBy string
		var orderBy string
		fields := make([]string, 0)
		if req.Merge == 1 {
			groupBy = "b.advertiser_company"
			fields = append(fields, "CONCAT(MIN(a.create_date), ' - ', MAX(a.create_date)) as createDate")
		} else {
			groupBy = "b.advertiser_company, b.user_id, a.create_date"
			fields = append(fields, "ANY_VALUE(a.create_date) as createDate")
			fields = append(fields, "ANY_VALUE(b.user_id) as userId")
			fields = append(fields, "ANY_VALUE(u.user_name) as userName")
		}
		orderBy = "statPayAmount desc"
		if req.OrderBy != "" {
			orderBy = req.OrderBy + " " + req.OrderType
		}
		listRes.Total, err = m.Group(groupBy).Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		fields = append(fields, "ANY_VALUE(b.advertiser_company) as advertiserCompany")
		fields = append(fields, "COUNT(DISTINCT b.advertiser_id) as totalAccounts")
		fields = append(fields, "COUNT(DISTINCT CASE WHEN b.ad_status = 1 THEN b.advertiser_id END) as activeAccounts")
		fields = append(fields, "ROUND(SUM(a.stat_cost),2) as statCost")
		fields = append(fields, "ROUND(SUM(a.stat_pay_amount),2) as statPayAmount")
		fields = append(fields, "ROUND(SUM(a.stat_pay_amount)/SUM(a.stat_cost)*100,2) as payAmountRoi")
		fields = append(fields, "SUM(a.active) as active")
		fields = append(fields, "ROUND(SUM(a.active)/SUM(a.click_cnt)*100,2) as activeRate")
		fields = append(fields, "SUM(a.convert_cnt) as convertCnt")
		fields = append(fields, "ROUND(SUM(a.convert_cnt)/SUM(a.click_cnt)*100,2) as conversionRate")
		fields = append(fields, "ROUND(SUM(a.attribution_game_in_app_ltv_1day),2) as attributionGameInAppLtv1Day")
		fields = append(fields, "ROUND(SUM(a.attribution_game_in_app_ltv_1day)/SUM(a.stat_cost)*100,2) as attributionGameInAppRoi1Day")
		fields = append(fields, "ROUND(SUM(a.attribution_micro_game_0d_ltv),2) as attributionMicroGame0DLtv")
		fields = append(fields, "ROUND(SUM(a.attribution_micro_game_0d_ltv)/SUM(a.stat_cost)*100,2) as attributionMicroGame0DRoi")
		err = m.Fields(fields).
			Page(req.PageNum, req.PageSize).
			Group(groupBy).
			Order(orderBy).
			Scan(&listRes.List)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		err1 := m.Fields(fields).Scan(&listRes.Summary)
		liberr.ErrIsNil(ctx, err1, "获取汇总数据失败")
	})
	return
}
