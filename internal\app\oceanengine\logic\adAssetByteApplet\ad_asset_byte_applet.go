// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-12-13 16:08:04
// 生成路径: internal/app/oceanengine/logic/ad_asset_byte_applet.go
// 生成人：cq
// desc:资产-字节小程序
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gogf/gf/v2/container/gset"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/util/gconv"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/dao"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/advertiser"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/api"
	toutiaoModels "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/liberr"
	"github.com/xuri/excelize/v2"
	"strings"
	"time"
)

func init() {
	service.RegisterAdAssetByteApplet(New())
}

func New() service.IAdAssetByteApplet {
	return &sAdAssetByteApplet{}
}

type sAdAssetByteApplet struct{}

func (s *sAdAssetByteApplet) List(ctx context.Context, req *model.AdAssetByteAppletSearchReq) (listRes *model.AdAssetByteAppletSearchRes, err error) {
	listRes = new(model.AdAssetByteAppletSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		m := dao.AdAssetByteApplet.Ctx(ctx).WithAll()
		if !admin && len(userIds) > 0 {
			m = m.WhereIn(dao.AdAssetByteApplet.Columns().UserId, userIds)
		}
		if req.InstanceId != "" {
			m = m.Where(dao.AdAssetByteApplet.Columns().InstanceId+" = ?", req.InstanceId)
		}
		if req.InstanceIds != nil && len(req.InstanceIds) > 0 {
			m = m.WhereIn(dao.AdAssetByteApplet.Columns().InstanceId, req.InstanceIds)
		}
		if req.Name != "" {
			m = m.Where(dao.AdAssetByteApplet.Columns().Name+" = ?", req.Name)
		}
		if req.Names != nil && len(req.Names) > 0 {
			m = m.WhereIn(dao.AdAssetByteApplet.Columns().Name, req.Names)
		}
		if req.AuditStatus != "" {
			m = m.Where(dao.AdAssetByteApplet.Columns().AuditStatus+" = ?", req.AuditStatus)
		}
		if req.AdvertiserId != "" {
			m = m.Where(dao.AdAssetByteApplet.Columns().AdvertiserId+" = ?", req.AdvertiserId)
		}
		if req.Remark != "" {
			m = m.Where(dao.AdAssetByteApplet.Columns().Remark+" like ?", "%"+req.Remark+"%")
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id desc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdAssetByteAppletListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdAssetByteAppletListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.AdAssetByteAppletListRes{
				Id:             v.Id,
				InstanceId:     v.InstanceId,
				Name:           v.Name,
				AppId:          v.AppId,
				AuditStatus:    v.AuditStatus,
				Reason:         v.Reason,
				Remark:         v.Remark,
				TagInfo:        v.TagInfo,
				AdvertiserId:   v.AdvertiserId,
				AdvertiserNick: v.AdvertiserNick,
				UserId:         v.UserId,
				CreatedAt:      v.CreatedAt,
			}
		}
		// 查询审核中的字节小程序，检测审核状态
		go func() {
			innerCtx := context.Background()
			advertiserIdList, _ := s.GetAdvertiserIdByStatus(innerCtx, commonConsts.Auditing)
			advertiserIds := make([]string, 0)
			for _, infoRes := range advertiserIdList {
				advertiserIds = append(advertiserIds, infoRes.AdvertiserId)
			}
			err1 := s.Sync(innerCtx, advertiserIds)
			if err1 != nil {
				g.Log().Errorf(innerCtx, "检测字节小程序审核状态失败: %v", err1)
			}
		}()
	})
	return
}

func (s *sAdAssetByteApplet) GetById(ctx context.Context, id int) (res *model.AdAssetByteAppletInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdAssetByteApplet.Ctx(ctx).WithAll().Where(dao.AdAssetByteApplet.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdAssetByteApplet) GetAdvertiserIdByStatus(ctx context.Context, auditStatus string) (res []*model.AdAssetByteAppletInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdAssetByteApplet.Ctx(ctx).WithAll().
			Where(dao.AdAssetByteApplet.Columns().AuditStatus, auditStatus).
			Group(dao.AdAssetByteApplet.Columns().AdvertiserId).
			Fields("advertiser_id as advertiserId").
			Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdAssetByteApplet) Add(ctx context.Context, file *ghttp.UploadFile, categoryId string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if file == nil {
			err = errors.New("请上传数据文件")
			return
		}
		var batchAdd = make([]*model.AdAssetByteAppletAddReq, 0)
		var batchLinkAdd = make([]*model.AdAssetByteAppletLinkAddReq, 0)
		err = g.Try(ctx, func(ctx context.Context) {
			f, err1 := file.Open()
			liberr.ErrIsNil(ctx, err1)
			defer f.Close()
			exFile, err1 := excelize.OpenReader(f)
			liberr.ErrIsNil(ctx, err1)
			defer exFile.Close()
			rows, err1 := exFile.GetRows("Sheet1")
			liberr.ErrIsNil(ctx, err1)
			if len(rows) == 2 {
				liberr.ErrIsNil(ctx, errors.New("表格内容不能为空"))
			}
			d := make([]interface{}, len(rows[1]))
			for k, v := range rows {
				if k == 0 || k == 1 {
					continue
				}
				for kv, vv := range v {
					d[kv] = vv
				}
				var advertiserId = gconv.Int64(strings.TrimSpace(gconv.String(d[0])))
				var appId = strings.TrimSpace(gconv.String(d[1]))
				var remark = strings.TrimSpace(gconv.String(d[2]))
				var link = strings.TrimSpace(gconv.String(d[3]))
				var linkRemark = strings.TrimSpace(gconv.String(d[4]))
				var startPage = strings.TrimSpace(gconv.String(d[5]))
				var startParam = strings.TrimSpace(gconv.String(d[6]))
				microAppCreateReq := toutiaoModels.ToolsMicroAppCreateV30Request{
					AdvertiserId: advertiserId,
					AppId:        appId,
					Remark:       remark,
					AppPage: toutiaoModels.ToolsMicroAppCreateV30RequestAppPage{
						Link:       link,
						LinkRemark: linkRemark,
						StartPage:  &startPage,
						StartParam: &startParam,
					},
					TagInfo: &categoryId,
				}
				batchAdd, batchLinkAdd = s.BuildBatchAddParams(ctx, microAppCreateReq, remark, batchAdd, batchLinkAdd)
			}
			if len(batchAdd) > 0 {
				err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
					err = s.BatchAdd(ctx, batchAdd)
					liberr.ErrIsNil(ctx, err, "批量添加字节小程序失败")
					err = service.AdAssetByteAppletLink().BatchAdd(ctx, batchLinkAdd)
					liberr.ErrIsNil(ctx, err, "批量添加字节小程序链接失败")
					return err
				})
				liberr.ErrIsNil(ctx, err, "导入字节小程序失败")
			}
		})
		liberr.ErrIsNil(ctx, err)
	})
	return
}

func (s *sAdAssetByteApplet) Sync(ctx context.Context, advertiserIds []string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		errAdvertiserIds := gset.NewStrSet()
		for _, advertiserId := range advertiserIds {
			tokenRes, err1 := service.AdAdvertiserAccount().GetAccessTokenByAdvertiserId(ctx, advertiserId)
			if err1 != nil {
				g.Log().Errorf(ctx, "广告主: %v, 获取access_token失败: %v", advertiserId, err1)
				continue
			}
			var advertiserIdInt64 = gconv.Int64(advertiserId)
			var page int32 = 1
			var pageSize int32 = 100
			var searchTypes = []toutiaoModels.ToolsMicroAppListV30FilteringSearchType{
				toutiaoModels.CREATE_ONLY_ToolsMicroAppListV30FilteringSearchType,
				toutiaoModels.SHARE_ONLY_ToolsMicroAppListV30FilteringSearchType,
			}
			for _, searchType := range searchTypes {
				wechatAppletListRes, err2 := advertiser.GetToutiaoApiClient().ToolsMicroAppListV3ApiService.
					AccessToken(tokenRes.AccessToken).
					ToolsMicroAppListV3Request(api.ToolsMicroAppListV3Request{
						AdvertiserId: &advertiserIdInt64,
						Filtering: &toutiaoModels.ToolsMicroAppListV30Filtering{
							SearchType: &searchType,
						},
						Page:     &page,
						PageSize: &pageSize,
					}).Do()
				if err2 != nil {
					if strings.Contains(err2.Error(), "Too many requests") {
						time.Sleep(1000 * time.Millisecond)
						wechatAppletListRes, err2 = advertiser.GetToutiaoApiClient().ToolsMicroAppListV3ApiService.
							AccessToken(tokenRes.AccessToken).
							ToolsMicroAppListV3Request(api.ToolsMicroAppListV3Request{
								AdvertiserId: &advertiserIdInt64,
								Filtering: &toutiaoModels.ToolsMicroAppListV30Filtering{
									SearchType: &searchType,
								},
								Page:     &page,
								PageSize: &pageSize,
							}).Do()
						if err != nil {
							errAdvertiserIds.Add(advertiserId)
							g.Log().Errorf(ctx, "广告主: %v, 获取字节小程序失败: %v", advertiserId, err2)
							continue
						}
					}
				}
				if wechatAppletListRes.Data == nil || wechatAppletListRes.Data.List == nil || len(wechatAppletListRes.Data.List) == 0 {
					continue
				}
				batchAdd := make([]*model.AdAssetByteAppletAddReq, 0)
				batchLinkAdd := make([]*model.AdAssetByteAppletLinkAddReq, 0)
				for _, listInner := range wechatAppletListRes.Data.List {
					byteAppletAdd := &model.AdAssetByteAppletAddReq{
						InstanceId:        gconv.String(listInner.InstanceId),
						Name:              *listInner.Name,
						AppId:             *listInner.AppId,
						AuditStatus:       gconv.String(listInner.AuditStatus),
						Reason:            *listInner.Reason,
						Remark:            *listInner.Remark,
						AdvertiserId:      advertiserId,
						AdvertiserNick:    tokenRes.AdvertiserNick,
						OwnerAdvertiserId: gconv.String(listInner.AdvertiserId),
						UserId:            tokenRes.UserId,
					}
					if listInner.TagInfo != nil {
						var tagInfo *model.TagInfo
						err2 = json.Unmarshal([]byte(*listInner.TagInfo), &tagInfo)
						if err2 == nil {
							byteAppletAdd.TagInfo = s.GetCategoryId(ctx, tagInfo)
						}
					}
					batchAdd = append(batchAdd, byteAppletAdd)
					assetLinkListRes, err3 := advertiser.GetToutiaoApiClient().ToolsAssetLinkListV3ApiService.
						AccessToken(tokenRes.AccessToken).
						ToolsAssetLinkV3Request(api.ToolsAssetLinkV3Request{
							AdvertiserId: &advertiserIdInt64,
							Filtering: &toutiaoModels.ToolsAssetLinkListV30Filtering{
								InstanceId: *listInner.InstanceId,
							},
							Page:     &page,
							PageSize: &pageSize,
						}).Do()
					if err3 != nil {
						errAdvertiserIds.Add(advertiserId)
						g.Log().Errorf(ctx, "广告主: %v, 获取字节小程序链接失败: %v", advertiserId, err3)
						continue
					}
					if assetLinkListRes.Data != nil && assetLinkListRes.Data.List != nil && len(assetLinkListRes.Data.List) > 0 {
						for _, linkListInner := range assetLinkListRes.Data.List {
							batchLinkAdd = append(batchLinkAdd, &model.AdAssetByteAppletLinkAddReq{
								InstanceId: gconv.String(linkListInner.InstanceId),
								LinkId:     gconv.String(linkListInner.LinkId),
								Link:       *linkListInner.Link,
								LinkRemark: *linkListInner.LinkRemark,
								StartPage:  *linkListInner.StartPage,
								StartParam: *linkListInner.StartParam,
							})
						}
					}
				}
				// 获取小程序名称
				if len(batchAdd) > 0 {
					err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
						err = s.BatchAdd(ctx, batchAdd)
						liberr.ErrIsNil(ctx, err, "批量添加字节小程序失败")
						err = service.AdAssetByteAppletLink().BatchAdd(ctx, batchLinkAdd)
						liberr.ErrIsNil(ctx, err, "批量添加字节小程序链接失败")
						return err
					})
					liberr.ErrIsNil(ctx, err)
				}
			}
		}
		if errAdvertiserIds.Size() > 0 {
			liberr.ErrIsNil(ctx, errors.New("部分账户同步失败"))
		}
	})
	return
}

func (s *sAdAssetByteApplet) BatchAdd(ctx context.Context, batchReq []*model.AdAssetByteAppletAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		data := make([]*do.AdAssetByteApplet, len(batchReq))
		for k, v := range batchReq {
			data[k] = &do.AdAssetByteApplet{
				InstanceId:        v.InstanceId,
				Name:              v.Name,
				AppId:             v.AppId,
				AuditStatus:       v.AuditStatus,
				Reason:            v.Reason,
				Remark:            v.Remark,
				TagInfo:           v.TagInfo,
				AdvertiserId:      v.AdvertiserId,
				AdvertiserNick:    v.AdvertiserNick,
				OwnerAdvertiserId: v.OwnerAdvertiserId,
				UserId:            v.UserId,
			}
		}
		_, err = dao.AdAssetByteApplet.Ctx(ctx).Save(data)
		liberr.ErrIsNil(ctx, err)
	})
	return
}

func (s *sAdAssetByteApplet) ChannelImport(ctx context.Context, req *model.AdAssetChannelImportReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var appIds = gset.NewStrSet()
		for _, v := range req.List {
			appIds.Add(v.AppId)
		}
		miniInfos, _ := sysService.SPlatRules().GetMiniInfoByAppIds(ctx, appIds.Slice())
		batchAdd := make([]*model.AdAssetByteAppletAddReq, 0)
		batchLinkAdd := make([]*model.AdAssetByteAppletLinkAddReq, 0)
		for _, v := range req.List {
			var name string
			var appId = v.AppId
			for _, miniInfo := range miniInfos {
				if miniInfo.AppId == v.AppId {
					name = miniInfo.AppName
					break
				}
			}
			splits := strings.Split(v.Link, "\n")
			startPage := strings.Split(splits[0], ":")[1]
			startParam := strings.Split(splits[1], ":")[1]
			microAppCreateReq := toutiaoModels.ToolsMicroAppCreateV30Request{
				AdvertiserId: gconv.Int64(v.AdvertiserId),
				AppId:        appId,
				Remark:       name,
				AppPage: toutiaoModels.ToolsMicroAppCreateV30RequestAppPage{
					StartPage:  &startPage,
					StartParam: &startParam,
				},
				TagInfo: &req.CategoryId,
			}
			batchAdd, batchLinkAdd = s.BuildBatchAddParams(ctx, microAppCreateReq, name, batchAdd, batchLinkAdd)
		}
		if len(batchAdd) > 0 {
			err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
				err = s.BatchAdd(ctx, batchAdd)
				liberr.ErrIsNil(ctx, err, "批量添加字节小程序失败")
				err = service.AdAssetByteAppletLink().BatchAdd(ctx, batchLinkAdd)
				liberr.ErrIsNil(ctx, err, "批量添加字节小程序链接失败")
				return err
			})
			liberr.ErrIsNil(ctx, err, "渠道导入字节小程序失败")
		}
	})
	return
}

func (s *sAdAssetByteApplet) BuildBatchAddParams(ctx context.Context, microAppCreateReq toutiaoModels.ToolsMicroAppCreateV30Request, name string, batchAdd []*model.AdAssetByteAppletAddReq, batchLinkAdd []*model.AdAssetByteAppletLinkAddReq) ([]*model.AdAssetByteAppletAddReq, []*model.AdAssetByteAppletLinkAddReq) {
	tokenRes, microAppCreateRes, assetLinkListRes := s.MicroAppCreate(ctx, microAppCreateReq)
	batchAdd = append(batchAdd, &model.AdAssetByteAppletAddReq{
		InstanceId:        gconv.String(microAppCreateRes.Data.InstanceId),
		Name:              name,
		AppId:             microAppCreateReq.AppId,
		AuditStatus:       commonConsts.Auditing,
		TagInfo:           *microAppCreateReq.TagInfo,
		AdvertiserId:      gconv.String(microAppCreateReq.AdvertiserId),
		AdvertiserNick:    tokenRes.AdvertiserNick,
		OwnerAdvertiserId: gconv.String(microAppCreateReq.AdvertiserId),
		UserId:            tokenRes.UserId,
	})
	if assetLinkListRes.Data != nil && assetLinkListRes.Data.List != nil && len(assetLinkListRes.Data.List) > 0 {
		for _, listInner := range assetLinkListRes.Data.List {
			batchLinkAdd = append(batchLinkAdd, &model.AdAssetByteAppletLinkAddReq{
				InstanceId: gconv.String(listInner.InstanceId),
				LinkId:     gconv.String(listInner.LinkId),
				Link:       *listInner.Link,
				LinkRemark: *listInner.LinkRemark,
				StartPage:  *listInner.StartPage,
				StartParam: *listInner.StartParam,
			})
		}
	}
	return batchAdd, batchLinkAdd
}

// MicroAppCreate 创建抖音小程序
func (s *sAdAssetByteApplet) MicroAppCreate(ctx context.Context, microAppCreateReq toutiaoModels.ToolsMicroAppCreateV30Request) (tokenRes *model.GetAccessTokenRes, microAppCreateRes *toutiaoModels.ToolsMicroAppCreateV30Response, assetLinkListRes *toutiaoModels.ToolsAssetLinkListV30Response) {
	tokenRes, err := service.AdAdvertiserAccount().GetAccessTokenByAdvertiserId(ctx, gconv.String(microAppCreateReq.AdvertiserId))
	liberr.ErrIsNil(ctx, err, "账户未授权，请前往【推广】-【账户管理】进行授权后重试")
	microAppCreateRes, err = advertiser.GetToutiaoApiClient().ToolsMicroAppCreateV3ApiService.
		AccessToken(tokenRes.AccessToken).
		ToolsMicroAppCreateV30Request(microAppCreateReq).
		Do()
	if err != nil {
		liberr.ErrIsNil(ctx, err, fmt.Sprintf("广告主账户ID：%v，小程序appid：%v，错误信息：%v", microAppCreateReq.AdvertiserId, microAppCreateReq.AppId, err.Error()))
	}
	var retryCount = 3
	for i := 0; i < retryCount; i++ {
		assetLinkListRes = service.AdAssetByteAppletLink().GetAssetLinkList(ctx, tokenRes.AccessToken, microAppCreateReq.AdvertiserId, *microAppCreateRes.Data.InstanceId)
		if assetLinkListRes.Data != nil && assetLinkListRes.Data.List != nil && len(assetLinkListRes.Data.List) > 0 {
			break
		}
		time.Sleep(time.Second * 1)
	}
	return
}

func (s *sAdAssetByteApplet) Edit(ctx context.Context, req *model.AdAssetByteAppletEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdAssetByteApplet.Ctx(ctx).WherePri(req.Id).Update(do.AdAssetByteApplet{
			InstanceId:        req.InstanceId,
			Name:              req.Name,
			AppId:             req.AppId,
			AuditStatus:       req.AuditStatus,
			Reason:            req.Reason,
			Remark:            req.Remark,
			TagInfo:           req.TagInfo,
			AdvertiserId:      req.AdvertiserId,
			AdvertiserNick:    req.AdvertiserNick,
			OwnerAdvertiserId: req.OwnerAdvertiserId,
			UserId:            req.UserId,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdAssetByteApplet) Delete(ctx context.Context, ids []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdAssetByteApplet.Ctx(ctx).Delete(dao.AdAssetByteApplet.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sAdAssetByteApplet) GetCategoryId(ctx context.Context, tagInfo *model.TagInfo) (categoryId string) {
	innerTagInfo := s.GetInnerCategory(ctx, tagInfo)
	if innerTagInfo != nil {
		return gconv.String(innerTagInfo.CategoryId)
	}
	return
}

func (s *sAdAssetByteApplet) GetInnerCategory(ctx context.Context, tagInfo *model.TagInfo) *model.TagInfo {
	if tagInfo.Categories == nil && len(tagInfo.Categories) == 0 {
		return tagInfo
	}
	return s.GetInnerCategory(ctx, tagInfo.Categories[0])
}
