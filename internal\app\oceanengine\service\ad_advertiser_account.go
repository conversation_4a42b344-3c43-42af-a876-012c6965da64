// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2024-11-13 10:02:38
// 生成路径: internal/app/oceanengine/service/ad_advertiser_account.go
// 生成人：cq
// desc:广告主账户表
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

type IAdAdvertiserAccount interface {
	List(ctx context.Context, req *model.AdAdvertiserAccountSearchReq) (res *model.AdAdvertiserAccountSearchRes, err error)
	GetTaskList(ctx context.Context, req *model.AdAdvertiserTaskSearchReq) (listRes *model.AdAdvertiserAccountTaskSearchRes, err error)
	GetById(ctx context.Context, Id int64) (res *model.AdAdvertiserAccountInfoRes, err error)
	GetAccessToken(ctx context.Context) (res *model.GetAccessTokenRes, err error)
	GetOne(ctx context.Context) (res *model.AdAdvertiserAccountInfoRes, err error)
	GetAdAccountBudget(ctx context.Context, aIds []string) (budgetMap map[string]float64, err error)
	GetAdAccountName(ctx context.Context, aIds []string) (nameMap map[string]string, err error)
	CheckAdvertiserAccountAvatar(ctx context.Context, aids []string) (noPassIds []string, list []model.CheckAdvertiserAccountRes, err error)
	UploadAvatar(ctx context.Context, aids []string, imgUrl, fileName string) (err error)
	GetByAdvertiserIds(ctx context.Context, advertiserIds []string) (res []*model.AdAdvertiserAccount, err error)
	GetByAdvertiserList(ctx context.Context, advertiserIds []string) (res []*model.AdAdvertiserAccountInfoRes, err error)
	Add(ctx context.Context, req *model.AdAdvertiserAccountAddReq) (err error)
	BatchAdd(ctx context.Context, req []*model.AdAdvertiserAccountAddReq) (err error)
	Edit(ctx context.Context, req *model.AdAdvertiserAccountEditReq) (err error)
	BatchEdit(ctx context.Context, req []*model.AdAdvertiserAccountEditReq) (err error)
	Delete(ctx context.Context, Id []int64) (err error)
	DeleteByParentIds(ctx context.Context, parentIds []int64) (err error)
	GetCompanyList(ctx context.Context, company string, pageNo int, pageSize int) (res *model.GetCompanyListRes, err error)
	GetAccessTokenByAdvertiserId(ctx context.Context, advertiserId string) (res *model.GetAccessTokenRes, err error)
	RunSyncAllAdvertiserInfoTask(ctx context.Context, req *model.SyncAllAdvertiserInfoReq) (err error)
	SyncAllAdvertiserInfoTask(ctx context.Context)
}

var localAdAdvertiserAccount IAdAdvertiserAccount

func AdAdvertiserAccount() IAdAdvertiserAccount {
	if localAdAdvertiserAccount == nil {
		panic("implement not found for interface IAdAdvertiserAccount, forgot register?")
	}
	return localAdAdvertiserAccount
}

func RegisterAdAdvertiserAccount(i IAdAdvertiserAccount) {
	localAdAdvertiserAccount = i
}
