// ==========================================================================
// GFast自动生成router操作代码。
// 生成日期：2025-07-28 14:33:14
// 生成路径: internal/app/oceanengine/router/ad_advertiser_account_hour_metrics_data.go
// 生成人：cq
// desc:广告账户小时指标数据
// company:云南奇讯科技有限公司
// ==========================================================================

package router

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/controller"
)

func (router *Router) BindAdAdvertiserAccountHourMetricsDataController(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/adAdvertiserAccountHourMetricsData", func(group *ghttp.RouterGroup) {
		group.Bind(
			controller.AdAdvertiserAccountHourMetricsData,
		)
	})
}
