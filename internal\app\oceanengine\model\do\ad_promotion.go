// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2024-11-16 10:33:41
// 生成路径: internal/app/oceanengine/model/entity/ad_promotion.go
// 生成人：cq
// desc:巨量广告表
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdPromotion is the golang structure for table ad_promotion.
type AdPromotion struct {
	gmeta.Meta                   `orm:"table:ad_promotion, do:true"`
	Id                           interface{} `orm:"id,primary" json:"id"`                                                // ID
	PromotionId                  interface{} `orm:"promotion_id" json:"promotionId"`                                     // 广告ID
	PromotionName                interface{} `orm:"promotion_name" json:"promotionName"`                                 // 广告名称
	ProjectId                    interface{} `orm:"project_id" json:"projectId"`                                         // 项目ID
	AdvertiserId                 interface{} `orm:"advertiser_id" json:"advertiserId"`                                   // 广告账户id
	PromotionCreateTime          interface{} `orm:"promotion_create_time" json:"promotionCreateTime"`                    // 广告创建时间，格式yyyy-MM-dd HH:mm:ss
	PromotionModifyTime          interface{} `orm:"promotion_modify_time" json:"promotionModifyTime"`                    // 广告更新时间，格式yyyy-MM-dd HH:mm:ss
	Status                       interface{} `orm:"status" json:"status"`                                                // 广告状态，OK 投放中 DELETED 已删除PROJECT_OFFLINE_BUDGET 项目超出预PROJECT_PREOFFLINE_BUDGET 项目接近预算TIME_NO_REACH 未到达投放时间 TIME_DONE 已完成 NO_SCHEDULE 不在投放时段 AUDIT 新建审核中 REAUDIT 修改审核中 FROZEN 已终止 AUDIT_DENY 审核不通过 OFFLINE_BUDGET 广告超出预算 OFFLINE_BALANCE 账户余额不足PREOFFLINE_BUDGET 广告接近预算 DISABLED 已暂停 PROJECT_DISABLED 已被项目暂停 LIVE_ROOM_OFF 关联直播间不可投PRODUCT_OFFLINE 关联商品不可投AWEME_ACCOUNT_DISABLED 关联抖音账号不可投AWEME_ANCHOR_DISABLED 锚点不可投DISABLE_BY_QUOTA 已暂停（配额达限）CREATE 新建 ADVERTISER_OFFLINE_BUDGET 账号超出预算ADVERTISER_PREOFFLINE_BUDGET 账号接近预算
	OptStatus                    interface{} `orm:"opt_status" json:"optStatus"`                                         // 操作状态，ENABLE 启用、DISABLE 暂停、DISABLE_BY_QUOTA暂停（投放额度不足）
	Budget                       interface{} `orm:"budget" json:"budget"`                                                // 预算
	BudgetMode                   interface{} `orm:"budget_mode" json:"budgetMode"`                                       // 预算类型
	CpaBid                       interface{} `orm:"cpa_bid" json:"cpaBid"`                                               // 广告出价：目标转化出价/预期成本
	DeepCpaBid                   interface{} `orm:"deep_cpa_bid" json:"deepCpaBid"`                                      // 深度优化出价
	LearningPhase                interface{} `orm:"learning_phase" json:"learningPhase"`                                 // 学习期状态，枚举值：DEFAULT（默认，不在学习期中）、LEARNING（学习中）、LEARNED（学习成功）、LEARN_FAILED（学习失败)
	UserId                       interface{} `orm:"user_id" json:"userId"`                                               // 所属用户
	MajordomoAdvertiserAccountId interface{} `orm:"majordomo_advertiser_account_id" json:"majordomoAdvertiserAccountId"` // 管家账户ID
	CreatedAt                    *gtime.Time `orm:"created_at" json:"createdAt"`                                         // 创建时间
	UpdatedAt                    *gtime.Time `orm:"updated_at" json:"updatedAt"`                                         // 更新时间
	DeletedAt                    *gtime.Time `orm:"deleted_at" json:"deletedAt"`                                         // 删除时间
}
