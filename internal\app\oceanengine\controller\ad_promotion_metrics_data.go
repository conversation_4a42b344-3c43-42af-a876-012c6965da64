// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-11-19 10:47:09
// 生成路径: internal/app/oceanengine/controller/ad_promotion_metrics_data.go
// 生成人：cyao
// desc:广告账户下的广告的指标数据
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/oceanengine"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type adPromotionMetricsDataController struct {
	systemController.BaseController
}

var AdPromotionMetricsData = new(adPromotionMetricsDataController)

// AdPromotionReportDataSearch 广告计划指标数据列表
func (c *adPromotionMetricsDataController) AdPromotionReportDataSearch(ctx context.Context, req *oceanengine.AdPromotionReportDataSearchReq) (res *oceanengine.AdPromotionReportDataSearchRes, err error) {
	res = new(oceanengine.AdPromotionReportDataSearchRes)
	res.AdPromotionReportDataSearchRes, err = service.AdPromotionMetricsData().AdPromotionReportDataSearch(ctx, &req.AdPromotionReportDataSearch)
	return
}

func (c *adPromotionMetricsDataController) AdPromotionReportDataSearch2(ctx context.Context, req *oceanengine.AdPromotionReportDataSearch2Req) (res *oceanengine.AdPromotionReportDataSearch2Res, err error) {
	res = new(oceanengine.AdPromotionReportDataSearch2Res)
	res.AdPromotionReportDataSearchRes2, err = service.AdPromotionMetricsData().AdPromotionReportDataSearch2(ctx, &req.AdPromotionReportDataSearch2)
	return
}

// AdPromotionAccountReportData
func (c *adPromotionMetricsDataController) AdPromotionAccountReportDataSearch(ctx context.Context, req *oceanengine.AdPromotionAccountReportDataSearchReq) (res *oceanengine.AdPromotionAccountReportDataSearchRes, err error) {
	res = new(oceanengine.AdPromotionAccountReportDataSearchRes)
	res.AdPromotionAccountReportDataSearchRes, err = service.AdPromotionMetricsData().AdPromotionAccountReportDataSearch(ctx, &req.AdPromotionAccountReportDataSearch)
	return
}

// AdPromotionMaterialStatistics
func (c *adPromotionMetricsDataController) AdPromotionMaterialStatistics(ctx context.Context, req *oceanengine.AdPromotionMaterialStatisticsReq) (res *oceanengine.AdPromotionMaterialStatisticsRes, err error) {
	res = new(oceanengine.AdPromotionMaterialStatisticsRes)
	res.AdPromotionMaterialReportDataSearchRes, err = service.AdPromotionMetricsData().AdPromotionMaterialStatistics(ctx, &req.AdPromotionMaterialStatisticsReq)
	return
}

// List 列表
func (c *adPromotionMetricsDataController) List(ctx context.Context, req *oceanengine.AdPromotionMetricsDataSearchReq) (res *oceanengine.AdPromotionMetricsDataSearchRes, err error) {
	res = new(oceanengine.AdPromotionMetricsDataSearchRes)
	res.AdPromotionMetricsDataSearchRes, err = service.AdPromotionMetricsData().List(ctx, &req.AdPromotionMetricsDataSearchReq)
	return
}

func (c *adPromotionMetricsDataController) AdProjectReportReportStatTask(ctx context.Context, req *oceanengine.AdPromotionReportReportStatTaskReq) (res *oceanengine.AdPromotionReportReportStatTaskRes, err error) {
	err = service.AdPromotionMetricsData().AdPromotionReportReportStatTask(ctx, req.StartTime, req.EndTime)
	return
}

func (c *adPromotionMetricsDataController) AdProjectReportReportSubTask(ctx context.Context, req *oceanengine.AdPromotionReportReportSubTaskReq) (res *oceanengine.AdPromotionReportReportSubTaskRes, err error) {
	err = service.AdPromotionMetricsData().UpdateMetricsByPIds(ctx, req.PIds, req.StartTime)
	return
}

// Get 获取广告账户下的广告的指标数据
func (c *adPromotionMetricsDataController) Get(ctx context.Context, req *oceanengine.AdPromotionMetricsDataGetReq) (res *oceanengine.AdPromotionMetricsDataGetRes, err error) {
	res = new(oceanengine.AdPromotionMetricsDataGetRes)
	res.AdPromotionMetricsDataInfoRes, err = service.AdPromotionMetricsData().GetById(ctx, req.Id)
	return
}

// Add 添加广告账户下的广告的指标数据
func (c *adPromotionMetricsDataController) Add(ctx context.Context, req *oceanengine.AdPromotionMetricsDataAddReq) (res *oceanengine.AdPromotionMetricsDataAddRes, err error) {
	err = service.AdPromotionMetricsData().Add(ctx, req.AdPromotionMetricsDataAddReq)
	return
}

// Edit 修改广告账户下的广告的指标数据
func (c *adPromotionMetricsDataController) Edit(ctx context.Context, req *oceanengine.AdPromotionMetricsDataEditReq) (res *oceanengine.AdPromotionMetricsDataEditRes, err error) {
	err = service.AdPromotionMetricsData().Edit(ctx, req.AdPromotionMetricsDataEditReq)
	return
}

// Delete 删除广告账户下的广告的指标数据
func (c *adPromotionMetricsDataController) Delete(ctx context.Context, req *oceanengine.AdPromotionMetricsDataDeleteReq) (res *oceanengine.AdPromotionMetricsDataDeleteRes, err error) {
	err = service.AdPromotionMetricsData().Delete(ctx, req.Ids)
	return
}

// OptimizerDataStatistics 优化师数据统计
func (c *adPromotionMetricsDataController) OptimizerDataStatistics(ctx context.Context, req *oceanengine.OptimizerDataStatisticsReq) (res *oceanengine.OptimizerDataStatisticsRes, err error) {
	res = new(oceanengine.OptimizerDataStatisticsRes)
	res.OptimizerDataStatisticsRes, err = service.AdPromotionMetricsData().OptimizerDataStatistics(ctx, &req.OptimizerDataStatisticsReq)
	return
}
