// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-07-24 10:05:27
// 生成路径: internal/app/oceanengine/model/ad_optimizer_data_stat.go
// 生成人：cq
// desc:优化师数据统计表
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdOptimizerDataStatInfoRes is the golang structure for table ad_optimizer_data_stat.
type AdOptimizerDataStatInfoRes struct {
	gmeta.Meta                  `orm:"table:ad_optimizer_data_stat"`
	CreateDate                  string  `orm:"create_date" json:"createDate" dc:"统计日期"`                                                     // 统计日期
	AdvertiserId                string  `orm:"advertiser_id" json:"advertiserId" dc:"广告账户ID"`                                               // 广告账户ID
	UserId                      int     `orm:"user_id" json:"userId" dc:"优化师ID"`                                                            // 优化师ID
	TotalAdNums                 int     `orm:"total_ad_nums" json:"totalAdNums" dc:"搭建广告数"`                                                 // 搭建广告数
	HasCostAdNums               int     `orm:"has_cost_ad_nums" json:"hasCostAdNums" dc:"有消耗广告数"`                                           // 有消耗广告数
	LearnedAdNums               int     `orm:"learned_ad_nums" json:"learnedAdNums" dc:"过学习期广告数"`                                           // 过学习期广告数
	StatCost                    float64 `orm:"stat_cost" json:"statCost" dc:"消耗"`                                                           // 消耗
	StatPayAmount               float64 `orm:"stat_pay_amount" json:"statPayAmount" dc:"付费金额（回传时间）"`                                        // 付费金额（回传时间）
	ShowCnt                     int     `orm:"show_cnt" json:"showCnt" dc:"展示数"`                                                            // 展示数
	CpmPlatform                 float64 `orm:"cpm_platform" json:"cpmPlatform" dc:"平均千次展现费用(元)"`                                            // 平均千次展现费用(元)
	ClickCnt                    int     `orm:"click_cnt" json:"clickCnt" dc:"点击数"`                                                          // 点击数
	ConvertCnt                  int     `orm:"convert_cnt" json:"convertCnt" dc:"转化数"`                                                      // 转化数
	ConversionCost              float64 `orm:"conversion_cost" json:"conversionCost" dc:"平均转化成本"`                                           // 平均转化成本
	Active                      int     `orm:"active" json:"active" dc:"激活数"`                                                               // 激活数
	ActiveCost                  float64 `orm:"active_cost" json:"activeCost" dc:"激活成本"`                                                     // 激活成本
	AttributionGameInAppLtv1Day float64 `orm:"attribution_game_in_app_ltv_1day" json:"attributionGameInAppLtv1Day" dc:"付费金额（激活用户当日付费金额）"`   // 付费金额（激活用户当日付费金额）
	AttributionMicroGame0DLtv   float64 `orm:"attribution_micro_game_0d_ltv" json:"attributionMicroGame0DLtv" dc:"小程序/小游戏当日LTV（激活用户当日LTV）"` // 小程序/小游戏当日LTV（激活用户当日LTV）
	ActivePay                   int     `orm:"active_pay" json:"activePay" dc:"首次付费数"`                                                      // 首次付费数
	GamePayCount                int     `orm:"game_pay_count" json:"gamePayCount" dc:"付费次数"`                                                // 付费次数
}

type AdOptimizerDataStatListRes struct {
	CreateDate                  string  `json:"createDate" dc:"统计日期"`
	AdvertiserId                string  `json:"advertiserId" dc:"广告账户ID"`
	UserId                      int     `json:"userId" dc:"优化师ID"`
	UserName                    string  `json:"userName" dc:"优化师"`
	SupervisorName              string  `json:"supervisorName" dc:"优化师主管"`
	TotalAdNums                 int     `json:"totalAdNums" dc:"搭建广告数"`
	HasCostAdNums               int     `json:"hasCostAdNums" dc:"有消耗广告数"`
	LearnedAdNums               int     `json:"learnedAdNums" dc:"过学习期广告数"`
	StatCost                    float64 `json:"statCost" dc:"消耗"`
	StatPayAmount               float64 `json:"statPayAmount" dc:"付费金额（回传时间）"`
	PayAmountRoi                float64 `json:"payAmountRoi" dc:"付费ROI（回传时间）"`
	ShowCnt                     int64   `json:"showCnt" dc:"展示数"`
	CpmPlatform                 float64 `json:"cpmPlatform" dc:"平均千次展现费用(元)"`
	ClickCnt                    int64   `json:"clickCnt" dc:"点击数"`
	Ctr                         float64 `json:"ctr" dc:"点击率"`
	ConvertCnt                  int64   `json:"convertCnt" dc:"转化数"`
	ConversionCost              float64 `json:"conversionCost" dc:"平均转化成本"`
	ConversionRate              float64 `json:"conversionRate" dc:"转化率"`
	Active                      int64   `json:"active" dc:"激活数"`
	ActiveCost                  float64 `json:"activeCost" dc:"激活成本"`
	ActiveRate                  float64 `json:"activeRate" dc:"激活率"`
	AttributionGameInAppLtv1Day float64 `json:"attributionGameInAppLtv1Day" dc:"付费金额（激活用户当日付费金额）"`
	AttributionGameInAppRoi1Day float64 `json:"attributionGameInAppRoi1Day" dc:"付费ROI（激活用户当日付费RIO）"`
	AttributionMicroGame0DLtv   float64 `json:"attributionMicroGame0DLtv" dc:"小程序/小游戏当日LTV（激活用户当日LTV）"`
	AttributionMicroGame0DRoi   float64 `json:"attributionMicroGame0DRoi" dc:"小程序/小游戏当日广告变现ROI（激活用户当日广告变现ROI）"`
	ActivePay                   int64   `json:"activePay" dc:"首次付费数"`
	ActivePayRate               float64 `json:"activePayRate" dc:"首次付费率"`
	GamePayCount                int64   `json:"gamePayCount" dc:"付费次数"`
}

// AdOptimizerDataStatSearchReq 分页请求参数
type AdOptimizerDataStatSearchReq struct {
	comModel.PageReq
	StartTime     string   `p:"startTime" dc:"开始时间格式 yyyy-MM-dd"`
	EndTime       string   `p:"endTime" dc:"结束时间格式 yyyy-MM-dd"`
	UserIds       []uint64 `p:"userIds"  dc:"优化师ID列表"`
	SupervisorIds []uint64 `p:"supervisorIds"  dc:"优化师主管ID列表"`
	DeptIds       []int    `p:"deptIds"  dc:"部门ID列表"`
	AdvertiserIds []string `p:"advertiserIds"  dc:"账户ID列表"`
}

// AdOptimizerDataStatSearchRes 列表返回结果
type AdOptimizerDataStatSearchRes struct {
	comModel.ListRes
	List    []*AdOptimizerDataStatListRes `json:"list"`
	Summary *AdOptimizerDataStatListRes   `json:"summary"`
}

// AdOptimizerDataStatAddReq 添加操作请求参数
type AdOptimizerDataStatAddReq struct {
	CreateDate                  string  `p:"createDate" v:"required#统计日期不能为空" dc:"统计日期"`
	AdvertiserId                string  `p:"advertiserId" dc:"广告账户ID"`
	UserId                      int     `p:"userId" v:"required#优化师ID不能为空" dc:"优化师ID"`
	TotalAdNums                 int     `p:"totalAdNums"  dc:"搭建广告数"`
	HasCostAdNums               int     `p:"hasCostAdNums"  dc:"有消耗广告数"`
	LearnedAdNums               int     `p:"learnedAdNums"  dc:"过学习期广告数"`
	StatCost                    float64 `p:"statCost"  dc:"消耗"`
	StatPayAmount               float64 `p:"statPayAmount"  dc:"付费金额（回传时间）"`
	ShowCnt                     int     `p:"showCnt"  dc:"展示数"`
	CpmPlatform                 float64 `p:"cpmPlatform"  dc:"平均千次展现费用(元)"`
	ClickCnt                    int     `p:"clickCnt"  dc:"点击数"`
	ConvertCnt                  int     `p:"convertCnt"  dc:"转化数"`
	ConversionCost              float64 `p:"conversionCost"  dc:"平均转化成本"`
	Active                      int     `p:"active"  dc:"激活数"`
	ActiveCost                  float64 `p:"activeCost"  dc:"激活成本"`
	AttributionGameInAppLtv1Day float64 `p:"attributionGameInAppLtv1Day"  dc:"付费金额（激活用户当日付费金额）"`
	AttributionMicroGame0DLtv   float64 `p:"attributionMicroGame0DLtv"  dc:"小程序/小游戏当日LTV（激活用户当日LTV）"`
	ActivePay                   int     `p:"activePay"  dc:"首次付费数"`
	GamePayCount                int     `p:"gamePayCount"  dc:"付费次数"`
}

// AdOptimizerDataStatEditReq 修改操作请求参数
type AdOptimizerDataStatEditReq struct {
	CreateDate                  string  `p:"createDate" v:"required#统计日期不能为空" dc:"统计日期"`
	AdvertiserId                string  `p:"advertiserId" dc:"广告账户ID"`
	UserId                      int     `p:"userId" v:"required#优化师ID不能为空" dc:"优化师ID"`
	TotalAdNums                 int     `p:"totalAdNums"  dc:"搭建广告数"`
	HasCostAdNums               int     `p:"hasCostAdNums"  dc:"有消耗广告数"`
	LearnedAdNums               int     `p:"learnedAdNums"  dc:"过学习期广告数"`
	StatCost                    float64 `p:"statCost"  dc:"消耗"`
	StatPayAmount               float64 `p:"statPayAmount"  dc:"付费金额（回传时间）"`
	ShowCnt                     int     `p:"showCnt"  dc:"展示数"`
	CpmPlatform                 float64 `p:"cpmPlatform"  dc:"平均千次展现费用(元)"`
	ClickCnt                    int     `p:"clickCnt"  dc:"点击数"`
	ConvertCnt                  int     `p:"convertCnt"  dc:"转化数"`
	ConversionCost              float64 `p:"conversionCost"  dc:"平均转化成本"`
	Active                      int     `p:"active"  dc:"激活数"`
	ActiveCost                  float64 `p:"activeCost"  dc:"激活成本"`
	AttributionGameInAppLtv1Day float64 `p:"attributionGameInAppLtv1Day"  dc:"付费金额（激活用户当日付费金额）"`
	AttributionMicroGame0DLtv   float64 `p:"attributionMicroGame0DLtv"  dc:"小程序/小游戏当日LTV（激活用户当日LTV）"`
	ActivePay                   int     `p:"activePay"  dc:"首次付费数"`
	GamePayCount                int     `p:"gamePayCount"  dc:"付费次数"`
}
