// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-07-23 16:32:30
// 生成路径: internal/app/oceanengine/controller/ad_account_subject_data_stat.go
// 生成人：cq
// desc:账户主体数据统计
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/oceanengine"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type adAccountSubjectDataStatController struct {
	systemController.BaseController
}

var AdAccountSubjectDataStat = new(adAccountSubjectDataStatController)

// List 列表
func (c *adAccountSubjectDataStatController) List(ctx context.Context, req *oceanengine.AdAccountSubjectDataStatSearchReq) (res *oceanengine.AdAccountSubjectDataStatSearchRes, err error) {
	res = new(oceanengine.AdAccountSubjectDataStatSearchRes)
	res.AdAccountSubjectDataStatSearchRes, err = service.AdAccountSubjectDataStat().List(ctx, &req.AdAccountSubjectDataStatSearchReq)
	return
}

// Add 添加账户主体数据统计
func (c *adAccountSubjectDataStatController) Add(ctx context.Context, req *oceanengine.AdAccountSubjectDataStatAddReq) (res *oceanengine.AdAccountSubjectDataStatAddRes, err error) {
	err = service.AdAccountSubjectDataStat().Add(ctx, req.AdAccountSubjectDataStatAddReq)
	return
}

// RunAdAccountSubjectDataStat 账户主体数据统计任务
func (c *adAccountSubjectDataStatController) RunAdAccountSubjectDataStat(ctx context.Context, req *oceanengine.AdAccountSubjectDataStatTaskReq) (res *oceanengine.AdAccountSubjectDataStatTaskRes, err error) {
	err = service.AdAccountSubjectDataStat().RunAdAccountSubjectDataStat(ctx, &req.AdAccountSubjectDataStatSearchReq)
	return
}
