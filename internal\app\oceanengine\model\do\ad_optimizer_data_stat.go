// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-07-24 10:05:27
// 生成路径: internal/app/oceanengine/model/entity/ad_optimizer_data_stat.go
// 生成人：cq
// desc:优化师数据统计表
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdOptimizerDataStat is the golang structure for table ad_optimizer_data_stat.
type AdOptimizerDataStat struct {
	gmeta.Meta                  `orm:"table:ad_optimizer_data_stat, do:true"`
	CreateDate                  interface{} `orm:"create_date" json:"createDate"`                                       // 统计日期
	AdvertiserId                interface{} `orm:"advertiser_id" json:"advertiserId"`                                   // 广告账户ID
	UserId                      interface{} `orm:"user_id" json:"userId"`                                               // 优化师ID
	TotalAdNums                 interface{} `orm:"total_ad_nums" json:"totalAdNums"`                                    // 搭建广告数
	HasCostAdNums               interface{} `orm:"has_cost_ad_nums" json:"hasCostAdNums"`                               // 有消耗广告数
	LearnedAdNums               interface{} `orm:"learned_ad_nums" json:"learnedAdNums"`                                // 过学习期广告数
	StatCost                    interface{} `orm:"stat_cost" json:"statCost"`                                           // 消耗
	StatPayAmount               interface{} `orm:"stat_pay_amount" json:"statPayAmount"`                                // 付费金额（回传时间）
	ShowCnt                     interface{} `orm:"show_cnt" json:"showCnt"`                                             // 展示数
	CpmPlatform                 interface{} `orm:"cpm_platform" json:"cpmPlatform"`                                     // 平均千次展现费用(元)
	ClickCnt                    interface{} `orm:"click_cnt" json:"clickCnt"`                                           // 点击数
	ConvertCnt                  interface{} `orm:"convert_cnt" json:"convertCnt"`                                       // 转化数
	ConversionCost              interface{} `orm:"conversion_cost" json:"conversionCost"`                               // 平均转化成本
	Active                      interface{} `orm:"active" json:"active"`                                                // 激活数
	ActiveCost                  interface{} `orm:"active_cost" json:"activeCost"`                                       // 激活成本
	AttributionGameInAppLtv1Day interface{} `orm:"attribution_game_in_app_ltv_1day" json:"attributionGameInAppLtv1Day"` // 付费金额（激活用户当日付费金额）
	AttributionMicroGame0DLtv   interface{} `orm:"attribution_micro_game_0d_ltv" json:"attributionMicroGame0DLtv"`      // 小程序/小游戏当日LTV（激活用户当日LTV）
	ActivePay                   interface{} `orm:"active_pay" json:"activePay"`                                         // 首次付费数
	GamePayCount                interface{} `orm:"game_pay_count" json:"gamePayCount"`                                  // 付费次数
}
