// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2024-11-16 10:33:41
// 生成路径: internal/app/oceanengine/service/ad_promotion.go
// 生成人：cq
// desc:巨量广告表
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

type IAdPromotion interface {
	List(ctx context.Context, req *model.AdPromotionSearchReq) (res *model.AdPromotionSearchRes, err error)
	GetTaskList(ctx context.Context, req *model.AdAdvertiserTaskSearchReq) (listRes *model.AdAdvertiserProjectTaskSearchRes, err error)
	GetExportData(ctx context.Context, req *model.AdPromotionSearchReq) (listRes []*model.AdPromotionInfoRes, err error)
	GetById(ctx context.Context, Id int64) (res *model.AdPromotionInfoRes, err error)
	GetByPromotionIds(ctx context.Context, promotionIds []string) (res []*model.AdPromotionInfoRes, err error)
	GetPromotionNumByAdvertiserId(ctx context.Context, advertiserId string) (count int, err error)
	GetPromotionNumByProjectId(ctx context.Context, projectId string, advertiserId string) (count int, err error)
	Add(ctx context.Context, req *model.AdPromotionAddReq) (err error)
	BatchAdd(ctx context.Context, batchAddReq []*model.AdPromotionAddReq) (err error)
	Edit(ctx context.Context, req *model.AdPromotionEditReq) (err error)
	Delete(ctx context.Context, Id []int64) (err error)
	RunSyncAdPromotion(ctx context.Context, req *model.SyncAdPromotionReq) (err error)
	SyncAdPromotion(ctx context.Context, statDate string, advertiserIds []string, promotionIds []int64) (tokenMap map[string]string, err error)
	GetAdPromotionCpaBid(ctx context.Context, promotionIds []int64) (cpaBidMap map[int64]float64, err error)
	GetAdPromotionBudget(ctx context.Context, promotionIds []int64) (budgetMap map[int64]float64, err error)
	GetAdPromotionName(ctx context.Context, promotionIds []string) (nameMap map[string]string, err error)
	AdPromotionStatusUpdate(ctx context.Context, req *model.AdPromotionStatusUpdateReq) (err error)
	SelectDouyinAccount(ctx context.Context, m *model.SelectDouyinAccountReq) (data *model.SelectDouyinAccountRes, err error)
	GetToutiaoCreativeComponent(ctx context.Context, m *model.GetToutiaoCreativeComponentReq) (data *model.GetToutiaoCreativeComponentRes, err error)
	GetNativeAnchor(ctx context.Context, req *model.GetNativeAnchorReq) (data *model.GetNativeAnchorRes, err error)
}

var localAdPromotion IAdPromotion

func AdPromotion() IAdPromotion {
	if localAdPromotion == nil {
		panic("implement not found for interface IAdPromotion, forgot register?")
	}
	return localAdPromotion
}

func RegisterAdPromotion(i IAdPromotion) {
	localAdPromotion = i
}
