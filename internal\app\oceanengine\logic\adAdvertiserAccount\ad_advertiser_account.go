// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-11-13 10:02:39
// 生成路径: internal/app/oceanengine/logic/ad_advertiser_account.go
// 生成人：cq
// desc:广告主账户表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/ahmetb/go-linq/v3"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/gogf/gf/v2/os/gtime"
	adService "github.com/tiger1103/gfast/v3/internal/app/ad/service"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysDo "github.com/tiger1103/gfast/v3/internal/app/system/model/do"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/advertiser"
	toutiaoModels "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"math"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/dao"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdAdvertiserAccount(New())
}

func New() service.IAdAdvertiserAccount {
	return &sAdAdvertiserAccount{}
}

type sAdAdvertiserAccount struct{}

func (s *sAdAdvertiserAccount) List(ctx context.Context, req *model.AdAdvertiserAccountSearchReq) (listRes *model.AdAdvertiserAccountSearchRes, err error) {
	listRes = new(model.AdAdvertiserAccountSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		m := dao.AdAdvertiserAccount.Ctx(ctx).As("ad").
			LeftJoin("sys_user u", "ad.user_id = u.id").
			LeftJoin("ad_majordomo_advertiser_account ama", "ama.id = ad.parent_id")
		if req.UserIds != nil && len(req.UserIds) > 0 {
			m = m.WhereIn("ad."+dao.AdAdvertiserAccount.Columns().UserId, req.UserIds)
		} else if !admin && len(userIds) > 0 {
			m = m.WhereIn("ad."+dao.AdAdvertiserAccount.Columns().UserId, userIds)
		}
		if len(req.DeptIds) > 0 {
			m = m.WhereIn("u.dept_id", req.DeptIds)
		}
		if len(req.AdvertiserIds) > 0 {
			m = m.WhereIn("ad."+dao.AdAdvertiserAccount.Columns().AdvertiserId, req.AdvertiserIds)
		}
		if len(req.Remarks) > 0 {
			m = m.WhereIn("ad."+dao.AdAdvertiserAccount.Columns().Remark, req.Remarks)
		}
		if req.Ids != nil && len(req.Ids) > 0 {
			m = m.WhereIn("ad."+dao.AdAdvertiserAccount.Columns().AdvertiserId, req.Ids)
		}
		if len(req.AdvertiserNicks) > 0 {
			m = m.WhereIn("ad."+dao.AdAdvertiserAccount.Columns().AdvertiserNick, req.AdvertiserNicks)
		}
		if req.AuthStatus != "" {
			m = m.Where("ama."+dao.AdMajordomoAdvertiserAccount.Columns().Expired+" = ?", gconv.Int(req.AuthStatus))
		}
		if req.AdStatus != "" {
			m = m.Where("ad."+dao.AdAdvertiserAccount.Columns().AdStatus+" = ?", gconv.Int(req.AdStatus))
		}
		if req.AdvertiserCompany != "" {
			m = m.Where("ad."+dao.AdAdvertiserAccount.Columns().AdvertiserCompany+" = ?", req.AdvertiserCompany)
		}
		if req.Balance != nil && *req.Balance > 0 {
			if *req.Balance == 1 {
				m = m.WhereGT("ad."+dao.AdAdvertiserAccount.Columns().Balance, 0)
			} else if *req.Balance == 2 {
				m = m.WhereLTE("ad."+dao.AdAdvertiserAccount.Columns().Balance, 0)
			}
		}
		if req.StartTime != "" && req.EndTime != "" {
			dayStartTime, dayEndTime := libUtils.GetDayStartAndEnd(req.StartTime, req.EndTime)
			m = m.WhereGTE("ad."+dao.AdMajordomoAdvertiserAccount.Columns().CreatedAt, dayStartTime)
			m = m.WhereLTE("ad."+dao.AdMajordomoAdvertiserAccount.Columns().CreatedAt, dayEndTime)
		}
		if req.Keyword != "" {
			m = m.Where("ad.advertiser_nick like ? or ad.remark like ? or ad.advertiser_id like ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
			//m = m.WhereLike("ad."+dao.AdAdvertiserAccount.Columns().AdvertiserNick, "%"+req.Keyword+"%")
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		var res []*model.AdAdvertiserAccountListRes
		err = m.Fields("ad.id as id").
			Fields("ad.advertiser_id as advertiserId").
			Fields("ad.advertiser_nick as advertiserNick").
			Fields("ad.remark as remark").
			Fields("ad.user_id as userId").
			Fields("u.user_name as userName").
			Fields("ama.expired as authStatus").
			Fields("ad.parent_id as parentId").
			Fields("ad.ad_status as adStatus").
			Fields("ad.balance as balance").
			Fields("ad.advertiser_company as advertiserCompany").
			Fields("ad.created_at as createdAt").
			Fields("ama.majordomo_type as majordomoType").
			Fields("ama.majordomo_nick as majordomoNick").
			Fields("ama.majordomo_user_name as majordomoUserName").
			Page(req.PageNum, req.PageSize).Order("id desc").Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdAdvertiserAccountListRes, len(res))
		for k, v := range res {
			adAdvertiser := &model.AdAdvertiserAccountListRes{
				Id:                v.Id,
				AdvertiserId:      v.AdvertiserId,
				AdvertiserNick:    v.AdvertiserNick,
				Remark:            v.Remark,
				UserId:            v.UserId,
				UserName:          v.UserName,
				AuthStatus:        v.AuthStatus,
				ParentId:          v.ParentId,
				AdStatus:          v.AdStatus,
				Balance:           v.Balance,
				AdvertiserCompany: v.AdvertiserCompany,
				CreatedAt:         v.CreatedAt,
				MajordomoType:     v.MajordomoType,
				MajordomoNick:     v.MajordomoNick,
				MajordomoUserName: v.MajordomoUserName,
			}
			if v.MajordomoType == commonConsts.CUSTOMER_ADMIN || v.MajordomoType == commonConsts.CUSTOMER_OPERATOR {
				adAdvertiser.MajordomoTypeName = commonConsts.Organization
			} else if v.MajordomoType == commonConsts.AGENT {
				adAdvertiser.MajordomoTypeName = commonConsts.Agent
			}
			listRes.List[k] = adAdvertiser
		}
	})
	return
}

// GetTaskList 给计划任务执行的list
func (s *sAdAdvertiserAccount) GetTaskList(ctx context.Context, req *model.AdAdvertiserTaskSearchReq) (listRes *model.AdAdvertiserAccountTaskSearchRes, err error) {
	listRes = new(model.AdAdvertiserAccountTaskSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdAdvertiserAccount.Ctx(ctx).As("ad").LeftJoin("ad_majordomo_advertiser_account ama", "ama.id = ad.parent_id")
		if req.UserId > 0 {
			m = m.Where("ad.user_id = ?", req.UserId)
		}
		if req.AdStatus != "" {
			m = m.Where("ad.ad_status = ?", gconv.Int(req.AdStatus))
		}
		if len(req.Ids) > 0 {
			m = m.WhereIn("ad.advertiser_id = ?", req.Ids)
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}

		err = m.Fields("ad.id as id").
			Fields("ad.advertiser_id as advertiserId").
			Fields("ad.user_id as userId").
			Fields("ad.auth_status as authStatus").
			Fields("ad.parent_id as parentId").
			Fields("ad.ad_status as adStatus").
			Fields("ad.balance as balance").
			Fields("ad.advertiser_company as advertiserCompany").
			Fields("ad.created_at as createdAt").
			Fields("ama.access_token as accessToken").
			Fields("ama.majordomo_id as adMajordomoAdvertiserId").
			Page(req.PageNum, req.PageSize).Order("id desc").Scan(&listRes.List)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

// GetAdAccountBudget 批量获取广告的预算Budget
func (s *sAdAdvertiserAccount) GetAdAccountBudget(ctx context.Context, aIds []string) (budgetMap map[string]float64, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		accounts := make([]*model.AdAdvertiserAccountInfoRes, 0)
		dao.AdAdvertiserAccount.Ctx(ctx).WithAll().Fields(dao.AdAdvertiserAccount.Columns().AdvertiserId, dao.AdAdvertiserAccount.Columns().Budget).WhereIn(dao.AdAdvertiserAccount.Columns().AdvertiserId, aIds).Scan(&accounts)
		// 将promotionList 转化成 capBidMap
		budgetMap = make(map[string]float64)
		for _, account := range accounts {
			budgetMap[account.AdvertiserId] = account.Budget
		}
	})
	return
}

func (s *sAdAdvertiserAccount) GetAdAccountName(ctx context.Context, aIds []string) (nameMap map[string]string, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		accounts := make([]*model.AdAdvertiserAccountInfoRes, 0)
		dao.AdAdvertiserAccount.Ctx(ctx).WithAll().Fields(dao.AdAdvertiserAccount.Columns().AdvertiserId, dao.AdAdvertiserAccount.Columns().AdvertiserNick).WhereIn(dao.AdAdvertiserAccount.Columns().AdvertiserId, aIds).Scan(&accounts)

		nameMap = make(map[string]string)
		for _, account := range accounts {
			nameMap[account.AdvertiserId] = account.AdvertiserNick
		}
	})
	return
}

func (s *sAdAdvertiserAccount) GetById(ctx context.Context, id int64) (res *model.AdAdvertiserAccountInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdAdvertiserAccount.Ctx(ctx).WithAll().Where(dao.AdAdvertiserAccount.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdAdvertiserAccount) GetAccessToken(ctx context.Context) (res *model.GetAccessTokenRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res = new(model.GetAccessTokenRes)
		err1 := dao.AdAdvertiserAccount.Ctx(ctx).As("ad").
			LeftJoin("ad_majordomo_advertiser_account", "ama", "ad.parent_id = ama.id").
			Fields("ama.access_token as accessToken").
			Fields("ad.user_id as userId").
			Fields("ad.parent_id as parentId").
			Fields("ad.advertiser_nick as advertiserNick").
			Fields("ad.advertiser_id as advertiserId").
			OrderDesc("ad.id").
			Limit(1).
			Scan(&res)
		liberr.ErrIsNil(ctx, err1, "获取access_token失败")
	})
	return
}

func (s *sAdAdvertiserAccount) GetOne(ctx context.Context) (res *model.AdAdvertiserAccountInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdAdvertiserAccount.Ctx(ctx).WithAll().
			OrderDesc(dao.AdAdvertiserAccount.Columns().Id).
			Limit(1).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdAdvertiserAccount) GetByAdvertiserIds(ctx context.Context, advertiserIds []string) (res []*model.AdAdvertiserAccount, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdAdvertiserAccount.Ctx(ctx).As("ad").
			LeftJoin("sys_user u", "ad.user_id =u.id").
			WhereIn("ad."+dao.AdAdvertiserAccount.Columns().AdvertiserId, advertiserIds).
			Fields("ad.advertiser_id as advertiserId").
			Fields("ad.advertiser_nick as advertiserName").
			Fields("ad.remark as advertiserRemark").
			Fields("ad.user_id as userId").
			Fields("ad.advertiser_company as company").
			Fields("u.user_name as userName").
			Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdAdvertiserAccount) GetByAdvertiserList(ctx context.Context, advertiserIds []string) (res []*model.AdAdvertiserAccountInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdAdvertiserAccount.Ctx(ctx).WithAll().
			WhereIn(dao.AdAdvertiserAccount.Columns().AdvertiserId, advertiserIds).
			Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

// CheckAdvertiserAccountAvatar 检测头像
func (s *sAdAdvertiserAccount) CheckAdvertiserAccountAvatar(ctx context.Context, aids []string) (noPassIds []string, list []model.CheckAdvertiserAccountRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		noPassIds = make([]string, 0)
		list = make([]model.CheckAdvertiserAccountRes, 0)
		for _, aid := range aids {
			tokenRes, err1 := service.AdAdvertiserAccount().GetAccessTokenByAdvertiserId(ctx, aid)
			liberr.ErrIsNil(ctx, err1, "获取access_token失败")

			interestKeywordRes, err2 := advertiser.GetToutiaoApiClient().AdvertiserAvatarGetV2ApiService.
				AccessToken(tokenRes.AccessToken).
				SetRequest(gconv.Int64(aid)).Do()
			if err2 != nil {
				noPassIds = append(noPassIds, aid)
				continue
			}
			if *interestKeywordRes.Data.AvatarStatus != "AUDIT_PASS" {
				noPassIds = append(noPassIds, aid)
			} else {
				list = append(list, model.CheckAdvertiserAccountRes{
					AdvertiserId: aid,
					AvatarStatus: *interestKeywordRes.Data.AvatarStatus,
					Avatar:       *interestKeywordRes.Data.AvatarInfo.WebUrl,
				})
			}
		}
	})
	return
}

// 上传头像
func (s *sAdAdvertiserAccount) UploadAvatar(ctx context.Context, aids []string, imgUrl, fileName string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		for _, aid := range aids {
			tokenRes, err1 := service.AdAdvertiserAccount().GetAccessTokenByAdvertiserId(ctx, aid)
			liberr.ErrIsNil(ctx, err1, "获取access_token失败")
			medId, innerError := adService.AdMaterialUpload().UpLoadByUrl(ctx, aid, imgUrl, fileName)
			liberr.ErrIsNil(ctx, innerError, "上传图片失败")
			_, err = advertiser.GetToutiaoApiClient().AdvertiserAvatarSubmitV2ApiService.
				AccessToken(tokenRes.AccessToken).
				AdvertiserUpdateBudgetV2Request(toutiaoModels.AdvertiserAvatarSubmitV2Request{
					AdvertiserId: gconv.Int64(aid),
					ImageId:      medId,
				}).Do()
		}
	})
	return
}

func (s *sAdAdvertiserAccount) Add(ctx context.Context, req *model.AdAdvertiserAccountAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdAdvertiserAccount.Ctx(ctx).Insert(do.AdAdvertiserAccount{
			Id:                req.Id,
			AdvertiserId:      req.AdvertiserId,
			AdvertiserNick:    req.AdvertiserNick,
			Remark:            req.Remark,
			UserId:            req.UserId,
			AuthStatus:        req.AuthStatus,
			ParentId:          req.ParentId,
			AdStatus:          req.AdStatus,
			Balance:           req.Balance,
			AdvertiserCompany: req.AdvertiserCompany,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdAdvertiserAccount) BatchAdd(ctx context.Context, req []*model.AdAdvertiserAccountAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		advertiserIds := make([]string, 0)
		for _, v := range req {
			advertiserIds = append(advertiserIds, v.AdvertiserId)
		}
		existAdvertisers, _ := s.GetByAdvertiserList(ctx, advertiserIds)
		addReq := make([]*do.AdAdvertiserAccount, 0)
		for _, accountAddReq := range req {
			adAdvertiserAccount := &do.AdAdvertiserAccount{
				AdvertiserId:      accountAddReq.AdvertiserId,
				AdvertiserNick:    accountAddReq.AdvertiserNick,
				Remark:            accountAddReq.Remark,
				UserId:            accountAddReq.UserId,
				AuthStatus:        accountAddReq.AuthStatus,
				ParentId:          accountAddReq.ParentId,
				AdStatus:          accountAddReq.AdStatus,
				Balance:           accountAddReq.Balance,
				AdvertiserCompany: accountAddReq.AdvertiserCompany,
				Status:            accountAddReq.Status,
				BudgeModel:        accountAddReq.BudgeModel,
				Budget:            accountAddReq.Budget,
				DeletedAt: sql.NullTime{
					Time:  time.Time{},
					Valid: false,
				},
			}
			for _, existAdvertiser := range existAdvertisers {
				if existAdvertiser.AdvertiserId == accountAddReq.AdvertiserId {
					adAdvertiserAccount.Id = existAdvertiser.Id
					break
				}
			}
			addReq = append(addReq, adAdvertiserAccount)
		}
		_, err = dao.AdAdvertiserAccount.Ctx(ctx).Save(addReq)
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdAdvertiserAccount) Edit(ctx context.Context, req *model.AdAdvertiserAccountEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdAdvertiserAccount.Ctx(ctx).WherePri(req.Id).Update(do.AdAdvertiserAccount{
			AdvertiserId:      req.AdvertiserId,
			AdvertiserNick:    req.AdvertiserNick,
			Remark:            req.Remark,
			UserId:            req.UserId,
			AuthStatus:        req.AuthStatus,
			ParentId:          req.ParentId,
			AdStatus:          req.AdStatus,
			Balance:           req.Balance,
			AdvertiserCompany: req.AdvertiserCompany,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdAdvertiserAccount) BatchEdit(ctx context.Context, req []*model.AdAdvertiserAccountEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		editReq := make([]*do.AdAdvertiserAccount, 0)
		for _, v := range req {
			advertiserAccount := &do.AdAdvertiserAccount{
				AdvertiserId: v.AdvertiserId,
			}
			if v.AdvertiserNick != "" {
				advertiserAccount.AdvertiserNick = v.AdvertiserNick
			}
			if v.Remark != "" {
				advertiserAccount.Remark = v.Remark
			}
			editReq = append(editReq, advertiserAccount)
		}
		if len(editReq) == 0 {
			return
		}
		_, err = dao.AdAdvertiserAccount.Ctx(ctx).Save(editReq)
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdAdvertiserAccount) Delete(ctx context.Context, ids []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdAdvertiserAccount.Ctx(ctx).Delete(dao.AdAdvertiserAccount.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sAdAdvertiserAccount) DeleteByParentIds(ctx context.Context, parentIds []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdAdvertiserAccount.Ctx(ctx).Delete(dao.AdAdvertiserAccount.Columns().ParentId+" in (?)", parentIds)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sAdAdvertiserAccount) GetCompanyList(ctx context.Context, company string, pageNo int, pageSize int) (listRes *model.GetCompanyListRes, err error) {
	listRes = new(model.GetCompanyListRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		m := dao.AdAdvertiserAccount.Ctx(ctx)
		if !admin && len(userIds) > 0 {
			m = m.WhereIn("user_id", userIds)
		}
		if company != "" {
			m = m.Where("advertiser_company like ?", "%"+company+"%")
		}
		listRes.Total, err = m.Group("advertiser_company").Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if pageNo == 0 {
			pageNo = 1
		}
		listRes.CurrentPage = pageNo
		if pageSize == 0 {
			pageSize = consts.PageSize
		}
		var companyList = make([]*model.CompanyInfo, 0)
		err = m.Fields("advertiser_company as company").
			Group("advertiser_company").
			Page(pageNo, pageSize).
			Scan(&companyList)
		liberr.ErrIsNil(ctx, err)
		listRes.List = companyList
	})
	return
}

func (s *sAdAdvertiserAccount) GetAccessTokenByAdvertiserId(ctx context.Context, advertiserId string) (res *model.GetAccessTokenRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res = new(model.GetAccessTokenRes)
		err1 := dao.AdAdvertiserAccount.Ctx(ctx).As("ad").
			LeftJoin("ad_majordomo_advertiser_account", "ama", "ad.parent_id = ama.id").
			Where("ad."+dao.AdAdvertiserAccount.Columns().AdvertiserId, advertiserId).
			Fields("ama.access_token as accessToken").
			Fields("ad.user_id as userId").
			Fields("ad.parent_id as parentId").
			Fields("ad.advertiser_nick as advertiserNick").
			Fields("ama.majordomo_type as majordomoType").
			Scan(&res)
		liberr.ErrIsNil(ctx, err1, "获取access_token失败")
	})
	return
}

func (s *sAdAdvertiserAccount) RunSyncAllAdvertiserInfoTask(ctx context.Context, req *model.SyncAllAdvertiserInfoReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if req.StartTime == "" && req.EndTime == "" {
			err = s.SyncAllAdvertiserInfo(ctx, req.StartTime)
			liberr.ErrIsNil(ctx, err, "同步巨量项目和广告计划失败")
		} else {
			startTime := req.StartTime
			endTime := req.EndTime
			for {
				if startTime > endTime {
					break
				}
				errors := s.SyncAllAdvertiserInfo(ctx, req.StartTime)
				if errors != nil {
					g.Log().Error(ctx, errors)
				}
				startTime = libUtils.PlusDays(startTime, 1)
			}
		}
	})
	return
}

func (s *sAdAdvertiserAccount) SyncAllAdvertiserInfoTask(ctx context.Context) {
	err := g.Try(ctx, func(ctx context.Context) {
		pool := goredis.NewPool(commonService.GetGoRedis())
		rs := redsync.New(pool)
		mutex := rs.NewMutex(commonConsts.PlatSyncAdvertiserInfoLock, redsync.WithRetryDelay(50*time.Millisecond))
		// TryLockContext只尝试锁定一次，无论成功或失败立即返回，无需重试
		err := mutex.TryLockContext(ctx)
		liberr.ErrIsNil(ctx, err, fmt.Sprintf("Redisson没有获取到分布式锁：%s", commonConsts.PlatSyncAdvertiserInfoLock))
		// 释放锁
		defer mutex.UnlockContext(ctx)
		err = s.SyncAllAdvertiserInfo(ctx, "")
		liberr.ErrIsNil(ctx, err, "同步巨量广告主/项目/计划失败")
		sysService.SysJobLog().Add(ctx, &sysDo.SysJobLog{
			TargetName: "SyncAllAdvertiserInfoTask",
			CreatedAt:  gtime.Now(),
			Result:     "同步巨量广告主/项目/计划，执行成功",
		})
	})
	if err != nil {
		g.Log().Error(ctx, err)
	}
}

func (s *sAdAdvertiserAccount) SyncAllAdvertiserInfo(ctx context.Context, statDate string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res := make([]*model.SyncAdAdvertiserAccount, 0)
		err1 := dao.AdAdvertiserAccount.Ctx(ctx).As("ad").
			LeftJoin("ad_majordomo_advertiser_account", "ama", "ad.parent_id = ama.id").
			Fields("ad.parent_id as adMajordomoAdvertiserId").
			Fields("ama.access_token as accessToken").
			Fields("ad.user_id as userId").
			Group("ad.parent_id, ad.user_id, ama.access_token").Scan(&res)
		liberr.ErrIsNil(ctx, err1, "获取广告主信息失败")
		for _, v := range res {
			adAdvertisers := make([]*model.AdAdvertiserAccountInfoRes, 0)
			// 根据管家账户ID和归属人员查询广告主账户列表
			err1 = dao.AdAdvertiserAccount.Ctx(ctx).Where("parent_id", v.AdMajordomoAdvertiserId).
				Where("user_id", v.UserId).Scan(&adAdvertisers)
			if err1 != nil {
				g.Log().Error(ctx, err1, "获取广告主信息失败")
				continue
			}
			advertiserIds := make([]string, 0)
			for _, innerV := range adAdvertisers {
				advertiserIds = append(advertiserIds, innerV.AdvertiserId)
			}
			var pageSize = 20
			totalPage := int(math.Ceil(float64(len(advertiserIds)) / float64(pageSize)))
			for i := 1; i <= totalPage; i++ {
				pageAdvertiserIds := make([]string, 0)
				linq.From(advertiserIds).Skip((i - 1) * pageSize).Take(pageSize).ToSlice(&pageAdvertiserIds)
				err2 := service.AdMajordomoAdvertiserAccount().BatchAddAdvertiser(ctx, pageAdvertiserIds, v.AccessToken, v.AdMajordomoAdvertiserId, v.UserId)
				if err2 != nil {
					g.Log().Error(ctx, err2, "批量更新广告主信息失败")
					continue
				}
				_, _ = service.AdProject().SyncAdProject(ctx, statDate, pageAdvertiserIds, nil)
				_, _ = service.AdPromotion().SyncAdPromotion(ctx, statDate, pageAdvertiserIds, nil)
			}
		}
	})
	return
}
