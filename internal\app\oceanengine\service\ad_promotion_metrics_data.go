// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2024-11-19 10:47:09
// 生成路径: internal/app/oceanengine/service/ad_promotion_metrics_data.go
// 生成人：cyao
// desc:广告账户下的广告的指标数据
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

type IAdPromotionMetricsData interface {
	AdPromotionReportDataSearch(ctx context.Context, req *model.AdPromotionReportDataSearch) (listRes *model.AdPromotionReportDataSearchRes, err error)
	AdPromotionReportDataSearch2(ctx context.Context, req *model.AdPromotionReportDataSearch2) (listRes *model.AdPromotionReportDataSearchRes2, err error)
	AdPromotionAccountReportDataSearch(ctx context.Context, req *model.AdPromotionAccountReportDataSearch) (listRes *model.AdPromotionAccountReportDataSearchRes, err error)
	AdPromotionMaterialStatistics(ctx context.Context, req *model.AdPromotionMaterialStatisticsReq) (listRes *model.AdPromotionMaterialReportDataSearchRes, err error)
	GetProjectMetricsByPlanTask(ctx context.Context, adIds, metricsName []string, TimeScope int) (list []*model.MetricsByPlanTaskRes)
	List(ctx context.Context, req *model.AdPromotionMetricsDataSearchReq) (res *model.AdPromotionMetricsDataSearchRes, err error)
	AdPromotionReportReportStatTask(ctx context.Context, startTime string, endTime string) (err error)
	UpdateMetricsByPIds(ctx context.Context, pIds []string, nowDate string) (err error)
	UpdateMetricsByAdvertiserIds(ctx context.Context, advertiserIds []string, nowDate string) (err error)
	GetById(ctx context.Context, Id int) (res *model.AdPromotionMetricsDataInfoRes, err error)
	Add(ctx context.Context, req *model.AdPromotionMetricsDataAddReq) (err error)
	Edit(ctx context.Context, req *model.AdPromotionMetricsDataEditReq) (err error)
	Delete(ctx context.Context, Id []int) (err error)
	OptimizerDataStatistics(ctx context.Context, req *model.OptimizerDataStatisticsReq) (listRes *model.OptimizerDataStatisticsRes, err error)
}

var localAdPromotionMetricsData IAdPromotionMetricsData

func AdPromotionMetricsData() IAdPromotionMetricsData {
	if localAdPromotionMetricsData == nil {
		panic("implement not found for interface IAdPromotionMetricsData, forgot register?")
	}
	return localAdPromotionMetricsData
}

func RegisterAdPromotionMetricsData(i IAdPromotionMetricsData) {
	localAdPromotionMetricsData = i
}
