// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-07-28 14:33:14
// 生成路径: internal/app/oceanengine/controller/ad_advertiser_account_hour_metrics_data.go
// 生成人：cq
// desc:广告账户小时指标数据
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/oceanengine"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type adAdvertiserAccountHourMetricsDataController struct {
	systemController.BaseController
}

var AdAdvertiserAccountHourMetricsData = new(adAdvertiserAccountHourMetricsDataController)

// List 列表
func (c *adAdvertiserAccountHourMetricsDataController) List(ctx context.Context, req *oceanengine.AdAdvertiserAccountHourMetricsDataSearchReq) (res *oceanengine.AdAdvertiserAccountHourMetricsDataSearchRes, err error) {
	res = new(oceanengine.AdAdvertiserAccountHourMetricsDataSearchRes)
	res.AdAdvertiserAccountHourMetricsDataSearchRes, err = service.AdAdvertiserAccountHourMetricsData().List(ctx, &req.AdAdvertiserAccountHourMetricsDataSearchReq)
	return
}

// Add 添加广告账户小时指标数据
func (c *adAdvertiserAccountHourMetricsDataController) Add(ctx context.Context, req *oceanengine.AdAdvertiserAccountHourMetricsDataAddReq) (res *oceanengine.AdAdvertiserAccountHourMetricsDataAddRes, err error) {
	err = service.AdAdvertiserAccountHourMetricsData().Add(ctx, req.AdAdvertiserAccountHourMetricsDataAddReq)
	return
}

// RunSyncAdAdvertiserAccountHourMetricsData 广告账户小时指标数据任务
func (c *adAdvertiserAccountHourMetricsDataController) RunSyncAdAdvertiserAccountHourMetricsData(ctx context.Context, req *oceanengine.AdAdvertiserAccountHourMetricsDataTaskReq) (res *oceanengine.AdAdvertiserAccountHourMetricsDataTaskRes, err error) {
	err = service.AdAdvertiserAccountHourMetricsData().RunSyncAdAdvertiserAccountHourMetricsData(ctx, req.AdAdvertiserAccountHourMetricsDataTaskReq)
	return
}
