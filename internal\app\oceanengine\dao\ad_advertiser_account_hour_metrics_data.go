// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-07-28 14:33:14
// 生成路径: internal/app/oceanengine/dao/ad_advertiser_account_hour_metrics_data.go
// 生成人：cq
// desc:广告账户小时指标数据
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/dao/internal"
)

// adAdvertiserAccountHourMetricsDataDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type adAdvertiserAccountHourMetricsDataDao struct {
	*internal.AdAdvertiserAccountHourMetricsDataDao
}

var (
	// AdAdvertiserAccountHourMetricsData is globally public accessible object for table tools_gen_table operations.
	AdAdvertiserAccountHourMetricsData = adAdvertiserAccountHourMetricsDataDao{
		internal.NewAdAdvertiserAccountHourMetricsDataDao(),
	}
)

// Fill with you ideas below.
