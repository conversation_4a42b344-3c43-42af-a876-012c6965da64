// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2024-11-15 16:27:19
// 生成路径: internal/app/oceanengine/service/ad_advertiser_account_metrics_data.go
// 生成人：cyao
// desc:广告账户的指标数据
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

type IAdAdvertiserAccountMetricsData interface {
	List(ctx context.Context, req *model.AdAdvertiserAccountMetricsDataSearchReq) (res *model.AdAdvertiserAccountMetricsDataSearchRes, err error)
	GetById(ctx context.Context, Id int) (res *model.AdAdvertiserAccountMetricsDataInfoRes, err error)
	Add(ctx context.Context, req *model.AdAdvertiserAccountMetricsDataAddReq) (err error)
	Edit(ctx context.Context, req *model.AdAdvertiserAccountMetricsDataEditReq) (err error)
	Delete(ctx context.Context, Id []int) (err error)
	AdAdvertiserAllMetricsReportStat(ctx context.Context, startTime string, endTime string) (err error)
	AdAdvertiserAccountReportSubTask(ctx context.Context, adId string, statDate string) (err error)
	AdAdvertiserAccountReportSubTask2(ctx context.Context, req []*model.AdAdvertiserAccountTaskListRes, statDate string) (err error)
	GetAccountMetricsByPlanTask(ctx context.Context, adIds, metricsName []string, TimeScope int) (list []*model.MetricsByPlanTaskRes)
	AdAdvertiserAccountReportStatTask(ctx context.Context, startTime string, endTime string) (err error)
	AdAdvertiserAccountReportDataSearch(ctx context.Context, m *model.AdAdvertiserAccountReportDataSearch) (*model.AdAdvertiserAccountReportDataSearchRes, error)
	// CreateAdAdvertiserAccountTransactionsStat 单单条数据的统计
	CreateAdAdvertiserAccountTransactionsStat(ctx context.Context, token, adId string, statDate string) (err error)
	// CreateAdAdvertiserAccountMetricsDataStat 单条数据的统计
	CreateAdAdvertiserAccountMetricsDataStat(ctx context.Context, token, adId string, statDate string) (err error)
	AccountSubjectDataStatistics(ctx context.Context, req *model.AccountSubjectDataStatisticsReq) (listRes *model.AccountSubjectDataStatisticsRes, err error)
}

var localAdAdvertiserAccountMetricsData IAdAdvertiserAccountMetricsData

func AdAdvertiserAccountMetricsData() IAdAdvertiserAccountMetricsData {
	if localAdAdvertiserAccountMetricsData == nil {
		panic("implement not found for interface IAdAdvertiserAccountMetricsData, forgot register?")
	}
	return localAdAdvertiserAccountMetricsData
}

func RegisterAdAdvertiserAccountMetricsData(i IAdAdvertiserAccountMetricsData) {
	localAdAdvertiserAccountMetricsData = i
}
