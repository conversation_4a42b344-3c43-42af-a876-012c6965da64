// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-07-28 14:33:14
// 生成路径: internal/app/oceanengine/logic/ad_advertiser_account_hour_metrics_data.go
// 生成人：cq
// desc:广告账户小时指标数据
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/dao"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	sysDo "github.com/tiger1103/gfast/v3/internal/app/system/model/do"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/advertiser"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
	"slices"
	"strconv"
	"strings"
	"time"
)

func init() {
	service.RegisterAdAdvertiserAccountHourMetricsData(New())
}

func New() service.IAdAdvertiserAccountHourMetricsData {
	return &sAdAdvertiserAccountHourMetricsData{}
}

type sAdAdvertiserAccountHourMetricsData struct{}

func (s *sAdAdvertiserAccountHourMetricsData) List(ctx context.Context, req *model.AdAdvertiserAccountHourMetricsDataSearchReq) (listRes *model.AdAdvertiserAccountHourMetricsDataSearchRes, err error) {
	listRes = new(model.AdAdvertiserAccountHourMetricsDataSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdAdvertiserAccountHourMetricsData.Ctx(ctx).WithAll()
		if req.AdvertiserId != "" {
			m = m.Where(dao.AdAdvertiserAccountHourMetricsData.Columns().AdvertiserId+" = ?", req.AdvertiserId)
		}
		if req.CreateDate != "" {
			m = m.Where(dao.AdAdvertiserAccountHourMetricsData.Columns().CreateDate+" = ?", req.CreateDate)
		}
		if req.Hour != "" {
			m = m.Where(dao.AdAdvertiserAccountHourMetricsData.Columns().Hour+" = ?", gconv.Int(req.Hour))
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdAdvertiserAccountHourMetricsDataListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdAdvertiserAccountHourMetricsDataListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.AdAdvertiserAccountHourMetricsDataListRes{
				AdvertiserId:  v.AdvertiserId,
				CreateDate:    v.CreateDate,
				Hour:          v.Hour,
				StatCost:      v.StatCost,
				StatPayAmount: v.StatPayAmount,
			}
		}
	})
	return
}

func (s *sAdAdvertiserAccountHourMetricsData) Add(ctx context.Context, req *model.AdAdvertiserAccountHourMetricsDataAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdAdvertiserAccountHourMetricsData.Ctx(ctx).Insert(do.AdAdvertiserAccountHourMetricsData{
			AdvertiserId:  req.AdvertiserId,
			CreateDate:    req.CreateDate,
			Hour:          req.Hour,
			StatCost:      req.StatCost,
			StatPayAmount: req.StatPayAmount,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdAdvertiserAccountHourMetricsData) RunSyncAdAdvertiserAccountHourMetricsData(ctx context.Context, req *model.AdAdvertiserAccountHourMetricsDataTaskReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime := req.StartTime
		endTime := req.EndTime
		innerContext, cancel := context.WithCancel(context.Background())
		defer cancel()
		for {
			if startTime > endTime {
				break
			}
			var adMajordomoAdvertiserId int64
			var advertiserId string
			if req.AdvertiserId != "" {
				tokenRes, _ := service.AdAdvertiserAccount().GetAccessTokenByAdvertiserId(ctx, req.AdvertiserId)
				adMajordomoAdvertiserId = tokenRes.ParentId
				advertiserId = req.AdvertiserId
			}
			if req.Hour != nil && *req.Hour >= 0 && *req.Hour <= 23 {
				_ = s.SyncAdAdvertiserAccountHourMetricsData(innerContext, adMajordomoAdvertiserId, advertiserId, startTime, *req.Hour)
			} else {
				for i := 0; i < 24; i++ {
					_ = s.SyncAdAdvertiserAccountHourMetricsData(innerContext, adMajordomoAdvertiserId, advertiserId, startTime, i)
				}
			}
			startTime = libUtils.PlusDays(startTime, 1)
		}
	})
	return
}

func (s *sAdAdvertiserAccountHourMetricsData) SyncAdAdvertiserAccountHourMetricsDataTask(ctx context.Context) {
	err := g.Try(ctx, func(ctx context.Context) {
		pool := goredis.NewPool(commonService.GetGoRedis())
		rs := redsync.New(pool)
		mutex := rs.NewMutex(commonConsts.PlatAdAccountHourMetricsDataStatLock, redsync.WithRetryDelay(50*time.Millisecond))
		// TryLockContext只尝试锁定一次，无论成功或失败立即返回，无需重试
		err := mutex.TryLockContext(ctx)
		liberr.ErrIsNil(ctx, err, fmt.Sprintf("Redisson没有获取到分布式锁：%s", commonConsts.PlatAdAccountHourMetricsDataStatLock))
		// 释放锁
		defer mutex.UnlockContext(ctx)
		var now = gtime.Now()
		var statDate = now.Format("Y-m-d")
		var hour = now.Hour()
		err = s.SyncAdAdvertiserAccountHourMetricsData(ctx, 0, "", statDate, hour)
		liberr.ErrIsNil(ctx, err, "同步广告账户小时指标数据失败")
		sysService.SysJobLog().Add(ctx, &sysDo.SysJobLog{
			TargetName: "SyncAdAdvertiserAccountHourMetricsDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "同步广告账户小时指标数据，执行成功",
		})
	})
	if err != nil {
		g.Log().Error(ctx, err)
	}
}

// SyncAdAdvertiserAccountHourMetricsData 同步广告账户小时指标数据
func (s *sAdAdvertiserAccountHourMetricsData) SyncAdAdvertiserAccountHourMetricsData(
	ctx context.Context,
	adMajordomoAdvertiserId int64,
	advertiserId string,
	statDate string,
	hour int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 0点获取前一天23:00 - 23:59的数据
		if hour == 0 {
			statDate = libUtils.GetYesterdayDate()
			hour = 23
		}

		// 1. 查询所有token未失效的管家账户
		var majordomoAccounts []*model.AdMajordomoAdvertiserAccountInfoRes
		m := dao.AdMajordomoAdvertiserAccount.Ctx(ctx).
			Where(dao.AdMajordomoAdvertiserAccount.Columns().AuthStatus+" = ?", 1). // 已授权
			Where(dao.AdMajordomoAdvertiserAccount.Columns().Expired+" = ?", 1).    // 未过期
			WhereNotNull(dao.AdMajordomoAdvertiserAccount.Columns().AccessToken)
		if adMajordomoAdvertiserId > 0 {
			m = m.Where(dao.AdMajordomoAdvertiserAccount.Columns().Id+" = ?", adMajordomoAdvertiserId)
		}
		_ = m.Scan(&majordomoAccounts)
		if len(majordomoAccounts) == 0 {
			return
		}

		// 2. 根据管家账户查询对应的广告账户
		for _, majordomoAccount := range majordomoAccounts {
			s.SyncAccountHourMetricsData(ctx, majordomoAccount, advertiserId, statDate, hour)
		}
	})
	return
}

// SyncAccountHourMetricsData 同步广告账户小时指标数据
func (s *sAdAdvertiserAccountHourMetricsData) SyncAccountHourMetricsData(
	ctx context.Context,
	majordomoAccount *model.AdMajordomoAdvertiserAccountInfoRes,
	advertiserId string,
	statDate string,
	hour int) {
	// 查询该管家账户下的所有广告账户
	var advertiserAccounts []*model.AdAdvertiserAccountInfoRes
	m := dao.AdAdvertiserAccount.Ctx(ctx).
		Where(dao.AdAdvertiserAccount.Columns().ParentId+" = ?", majordomoAccount.Id).
		Where(dao.AdAdvertiserAccount.Columns().AdStatus+" = ?", 1) // 启用状态
	if advertiserId != "" {
		m = m.Where(dao.AdAdvertiserAccount.Columns().AdvertiserId+" = ?", advertiserId)
	}
	_ = m.Scan(&advertiserAccounts)
	if len(advertiserAccounts) == 0 {
		return
	}
	var statList = make([]*model.AdAdvertiserAccountHourMetricsDataInfoRes, 0)
	// 逐个处理广告账户（因为API每次只能查询一个账户）
	for _, account := range advertiserAccounts {
		data := s.getSingleAccountHourMetricsData(ctx, account, majordomoAccount.AccessToken, statDate, hour)
		if data != nil {
			statList = append(statList, data)
		}
		if statList != nil && len(statList) >= 100 {
			_, _ = dao.AdAdvertiserAccountHourMetricsData.Ctx(ctx).Save(statList)
			statList = slices.Delete(statList, 0, len(statList))
		}
	}
	if len(statList) > 0 {
		_, _ = dao.AdAdvertiserAccountHourMetricsData.Ctx(ctx).Save(statList)
	}
	return
}

// getSingleAccountHourMetricsData 查询单个广告账户小时指标数据
func (s *sAdAdvertiserAccountHourMetricsData) getSingleAccountHourMetricsData(ctx context.Context, account *model.AdAdvertiserAccountInfoRes, accessToken, statDate string, hour int) *model.AdAdvertiserAccountHourMetricsDataInfoRes {
	var advertiserId = gconv.Int64(account.AdvertiserId)
	// 调用巨量API获取小时指标数据
	response, err := advertiser.GetFiltersHourReportByMetrics(ctx, advertiserId, accessToken, statDate, statDate, nil, nil)
	if err != nil && strings.Contains(err.Error(), "Too many requests") {
		time.Sleep(500 * time.Millisecond)
		response, err = advertiser.GetFiltersHourReportByMetrics(ctx, advertiserId, accessToken, statDate, statDate, nil, nil)
		if err != nil {
			g.Log().Errorf(ctx, "查询巨量广告账户%v小时指标数据错误: %v", advertiserId, err)
			return nil
		}
	}
	if response == nil || len(response.Rows) == 0 {
		return nil
	}
	// 处理API返回的数据
	var statTimeHour string
	if hour == 0 {
		hour = 24
	}
	hour = hour - 1
	if hour < 10 {
		statTimeHour = statDate + " 0" + strconv.Itoa(hour) + ":00" + " - " + "0" + strconv.Itoa(hour) + ":59"
	} else {
		statTimeHour = statDate + " " + strconv.Itoa(hour) + ":00" + " - " + strconv.Itoa(hour) + ":59"
	}
	var statCost float64
	var statPayAmount float64
	for _, v := range response.Rows {
		if v.Dimensions["stat_time_hour"] == statTimeHour {
			statCost = gconv.Float64(v.Metrics["stat_cost"])
			statPayAmount = gconv.Float64(v.Metrics["stat_pay_amount"])
			break
		}
	}
	if statCost == 0 && statPayAmount == 0 {
		return nil
	}
	return &model.AdAdvertiserAccountHourMetricsDataInfoRes{
		AdvertiserId:  account.AdvertiserId,
		CreateDate:    statDate,
		Hour:          hour,
		StatCost:      statCost,
		StatPayAmount: statPayAmount,
	}
}
