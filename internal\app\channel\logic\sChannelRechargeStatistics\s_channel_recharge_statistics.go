// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-01-25 10:56:08
// 生成路径: internal/app/channel/logic/s_channel_recharge_statistics.go
// 生成人：len
// desc:渠道充值统计
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/grpool"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	jsoniter "github.com/json-iterator/go"
	"github.com/tiger1103/gfast/v3/api/v1/system"
	adService "github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/channel/dao"
	"github.com/tiger1103/gfast/v3/internal/app/channel/model"
	"github.com/tiger1103/gfast/v3/internal/app/channel/model/do"
	channelModel "github.com/tiger1103/gfast/v3/internal/app/channel/model/entity"
	"github.com/tiger1103/gfast/v3/internal/app/channel/service"
	commonConst "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	orderService "github.com/tiger1103/gfast/v3/internal/app/order/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	theaterModel "github.com/tiger1103/gfast/v3/internal/app/theater/model"
	theaterService "github.com/tiger1103/gfast/v3/internal/app/theater/service"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
	"strconv"
	"strings"
	"time"
)

func init() {
	service.RegisterSChannelRechargeStatistics(New())
}

func New() service.ISChannelRechargeStatistics {
	return &sSChannelRechargeStatistics{
		pool: grpool.New(10),
	}
}

type sSChannelRechargeStatistics struct {
	pool *grpool.Pool
}

func (s *sSChannelRechargeStatistics) GetSumRecharge(ctx context.Context, createDate string) (res *model.SChannelRechargeStatisticsRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.SChannelRechargeStatistics.Ctx(ctx).WithAll()
		m = m.Where(dao.SChannelRechargeStatistics.Columns().CreateTime, createDate)
		m = m.Fields("sum(total_amount) totalAmount,sum(new_user_amount) newUserAmount,sum(account_coin_consume) accountCoinConsume")
		//查询出权限来
		ids, isAdmin, _ := sysService.SysUser().GetContainUser(ctx, sysService.Context().GetLoginUser(ctx))
		if !isAdmin {
			//非admin查询当前用户下的数据
			if len(ids) > 0 {
				m = m.WhereIn("pitcher_id", ids)
			}
		}
		m.Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sSChannelRechargeStatistics) GetTheaterRecharge(ctx context.Context, parentId int, pitcherId int, account string, startTime string, endTime string) (res *model.TheaterPitcherRechargeListRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.SChannelRechargeStatistics.Ctx(ctx)
		if startTime != "" {
			m = m.Where("create_time>=?", startTime)
		}
		if endTime != "" {
			m = m.Where("create_time<=?", endTime)
		}
		if parentId != 0 {
			m = m.Where("parent_id=?", parentId)
		}
		m = m.Where("pitcher_id=?", pitcherId)
		m = m.Where("account=?", account)
		fields := []string{" new_user_amount newUserAmount,total_amount totalAmount ,recharge_nums rechargeNums,new_user_recharge_nums newUserRechargeNums,account_coin_consume accountCoinConsume,total_recharge_times totalRechargeTimes,parent_id parentId,total_roi totalRoi,total_ad_up totalAdUp"}
		m = m.Fields(fields)
		err = m.Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sSChannelRechargeStatistics) GetTheaterPitcherRechargeExportData(ctx context.Context, req *model.TheaterPitcherRechargeReq) (listRes []*model.TheaterPitcherRechargeListRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := buildTheaterRechargeSql(ctx, req)
		fields := []string{" IFNULL(sum(new_user_amount),0.00) newUserAmount,IFNULL(sum(total_amount),0.00) totalAmount ,IFNULL(sum(recharge_nums),0.00) rechargeNums,IFNULL(sum(new_user_recharge_nums),0.00) newUserRechargeNums,IFNULL(sum(account_coin_consume),0.00) accountCoinConsume,IFNULL(SUM(total_recharge_times),0.00) totalRechargeTimes,s.pitcher_id pitcherId,su.user_name pitcher,create_time createTime,ANY_VALUE(su1.user_name) distributor"}
		m = m.Fields(fields)
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}

		err = m.Page(req.PageNum, req.PageSize).Scan(&listRes)
		//计算roi,totalRoi,客单价
		for _, v := range listRes {
			v.TotalRoi = libUtils.DivideAndRound(v.TotalAmount, v.AccountCoinConsume, 2, libUtils.RoundUp)
			v.DayRoi = libUtils.DivideAndRound(v.NewUserAmount, v.AccountCoinConsume, 2, libUtils.RoundUp)
			v.CustomerPrice = libUtils.DivideAndRound(v.TotalAmount, float64(v.RechargeNums), 2, libUtils.RoundUp)
		}
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sSChannelRechargeStatistics) GetTheaterPitcherRecharge(ctx context.Context, req *model.TheaterPitcherRechargeReq) (res *model.TheaterPitcherRechargeRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := buildTheaterRechargeSql(ctx, req)

		res = new(model.TheaterPitcherRechargeRes)
		res.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		fields := []string{" sum(new_user_amount) newUserAmount,sum(total_amount) totalAmount ,sum(recharge_nums) rechargeNums,sum(new_user_recharge_nums) newUserRechargeNums,sum(account_coin_consume) accountCoinConsume,SUM(total_recharge_times) totalRechargeTimes,s.pitcher_id pitcherId,su.user_name pitcher,create_time createTime,ANY_VALUE(su1.user_name) distributor"}
		m = m.Fields(fields)
		var resList []*model.TheaterPitcherRechargeListRes
		err = m.Page(req.PageNum, req.PageSize).Scan(&resList)
		//计算roi,totalRoi,客单价
		for _, v := range resList {
			v.TotalRoi = libUtils.DivideAndRound(v.TotalAmount, v.AccountCoinConsume, 2, libUtils.RoundUp)
			v.DayRoi = libUtils.DivideAndRound(v.NewUserAmount, v.AccountCoinConsume, 2, libUtils.RoundUp)
			v.CustomerPrice = libUtils.DivideAndRound(v.TotalAmount, float64(v.RechargeNums), 2, libUtils.RoundUp)
		}
		res.List = resList
		liberr.ErrIsNil(ctx, err, "获取数据失败")

	})

	return
}

func (s *sSChannelRechargeStatistics) RechargeStatics(ctx context.Context, req *model.RechargeStatisticsSearchReq) (res *model.RechargeStatisticsSearchRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		sql := "select IFNULL(sum(totalAmount),0.00) totalAmount,IFNULL(sum(wechatAndroidRechargeAmount),0.00) wechatAndroidRechargeAmount,IFNULL(sum(wechatIosRechargeAmount),0.00) wechatIosRechargeAmount,IFNULL(sum(totalAdUp),0.00) as totalAdUp," +
			"IFNULL(sum(dyRechargeAmount),0.00) dyRechargeAmount, IFNULL(sum(dyAndroidRechargeAmount),0.00) dyAndroidRechargeAmount,IFNULL(sum(dyIosRechargeAmount),0.00) as dyIosRechargeAmount," +
			"IFNULL(sum(aliAndroidRechargeAmount),0.00) as aliAndroidRechargeAmount, IFNULL(sum(aliIosRechargeAmount),0.00) as aliIosRechargeAmount" +
			" from (SELECT IFNULL(sum(total_amount),0.00) as totalAmount,IFNULL(sum(wechat_android_recharge_amount),0.00) as wechatAndroidRechargeAmount," +
			" IFNULL(sum(wechat_ios_recharge_amount),0.00) as wechatIosRechargeAmount,IFNULL(sum(dy_recharge_amount),0.00) as dyRechargeAmount," +
			" IFNULL(sum(dy_android_recharge_amount),0.00) as dyAndroidRechargeAmount,IFNULL(sum(dy_ios_recharge_amount),0.00) as dyIosRechargeAmount," +
			" IFNULL(sum(total_ad_up),0.00) as totalAdUp," +
			" IFNULL(sum(ali_android_recharge_amount),0.00) as aliAndroidRechargeAmount, IFNULL(sum(ali_ios_recharge_amount),0.00) as aliIosRechargeAmount" +
			" FROM `s_channel_recharge_statistics` AS sc" +
			" LEFT JOIN `s_channel` s ON sc.account = s.channel_code " +
			" WHERE (create_time>='" + req.StartTime + "') AND (create_time<='" + req.EndTime + "') AND s.channel_platform = 1" +
			" GROUP BY create_time ) c"
		//fields := []string{" IFNULL(sum(total_amount), 0.00) as totalAmount", "IFNULL(sum(wechat_android_recharge_amount), 0.00) as wechatAndroidRechargeAmount",
		//	"IFNULL(sum(wechat_ios_recharge_amount), 0.00) as wechatIosRechargeAmount", "IFNULL(sum(dy_recharge_amount), 0.00) as dyRechargeAmount", "IFNULL(sum(dy_android_recharge_amount), 0.00) as dyAndroidRechargeAmount",
		//	"IFNULL(sum(dy_ios_recharge_amount), 0.00) as dyIosRechargeAmount", "IFNULL(sum(new_user_amount), 0.00) as newUserAmount"}
		//var groupBy = "create_time "
		userInfo := sysService.Context().GetLoginUser(ctx)
		var userId = userInfo.Id
		roleList := sysService.SysUser().GetNowUserRole(ctx, userId)
		if len(roleList) <= 0 {
			//无角色，直接返回空
			return
		}
		user, _ := sysService.SysUser().GetUserById(ctx, userId)
		userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     user.Id,
				DeptId: user.DeptId,
			},
		})
		if !admin && len(userIds) > 0 {
			strArr := make([]string, len(userIds))
			for i, num := range userIds {
				strArr[i] = strconv.Itoa(num) // 将每个元素转换为字符串类型
			}
			var str = strings.Join(strArr, ",")
			sql = "select IFNULL(sum(totalAmount),0.00) totalAmount,IFNULL(sum(wechatAndroidRechargeAmount),0.00) wechatAndroidRechargeAmount,IFNULL(sum(wechatIosRechargeAmount),0.00) wechatIosRechargeAmount,IFNULL(sum(totalAdUp),0.00) as totalAdUp," +
				"IFNULL(sum(dyRechargeAmount),0.00) dyRechargeAmount, IFNULL(sum(dyAndroidRechargeAmount),0.00) dyAndroidRechargeAmount,IFNULL(sum(dyIosRechargeAmount),0.00) as dyIosRechargeAmount," +
				"IFNULL(sum(aliAndroidRechargeAmount),0.00) as aliAndroidRechargeAmount, IFNULL(sum(aliIosRechargeAmount),0.00) as aliIosRechargeAmount" +
				" from (SELECT IFNULL(sum(total_amount),0.00) as totalAmount,IFNULL(sum(wechat_android_recharge_amount),0.00) as wechatAndroidRechargeAmount," +
				" IFNULL(sum(wechat_ios_recharge_amount),0.00) as wechatIosRechargeAmount,IFNULL(sum(dy_recharge_amount),0.00) as dyRechargeAmount," +
				" IFNULL(sum(dy_android_recharge_amount),0.00) as dyAndroidRechargeAmount,IFNULL(sum(dy_ios_recharge_amount),0.00) as dyIosRechargeAmount," +
				" IFNULL(sum(total_ad_up),0.00) as totalAdUp," +
				" IFNULL(sum(ali_android_recharge_amount),0.00) as aliAndroidRechargeAmount, IFNULL(sum(ali_ios_recharge_amount),0.00) as aliIosRechargeAmount" +
				//" FROM `s_channel_recharge_statistics` WHERE (create_time>='" + req.StartTime + "') AND (create_time<='" + req.EndTime +
				" FROM `s_channel_recharge_statistics` AS sc" +
				" LEFT JOIN `s_channel` s ON sc.account = s.channel_code " +
				" WHERE (create_time>='" + req.StartTime + "') AND (create_time<='" + req.EndTime + "') AND s.channel_platform = 1" +
				" and pitcher_id in(" + str + ") GROUP BY create_time ) c"
		}

		adMap, err := dao.SChannelRechargeStatisticsAnalytic.DB().GetAll(ctx, sql)
		for _, q := range adMap {
			res = new(model.RechargeStatisticsSearchRes)
			libUtils.RecordToEntity(q, res)
		}
		liberr.ErrIsNil(ctx, err)

		if res != nil {
			totalAmount := res.TotalAmount + res.TotalAdUp
			res.TotalAmount = totalAmount
			res.DyAndroidRate = libUtils.MultiplyAndRound(libUtils.DivideAndRound(res.DyAndroidRechargeAmount, totalAmount, 5, libUtils.RoundDown), 100, 2, libUtils.RoundDown)
			res.DyIosRate = libUtils.MultiplyAndRound(libUtils.DivideAndRound(res.DyIosRechargeAmount, totalAmount, 5, libUtils.RoundDown), 100, 2, libUtils.RoundDown)
			res.WechatAndroidRate = libUtils.MultiplyAndRound(libUtils.DivideAndRound(res.WechatAndroidRechargeAmount, totalAmount, 5, libUtils.RoundDown), 100, 2, libUtils.RoundDown)
			res.WechatIosRate = libUtils.MultiplyAndRound(libUtils.DivideAndRound(res.WechatIosRechargeAmount, totalAmount, 5, libUtils.RoundDown), 100, 2, libUtils.RoundDown)
			res.AdUpRate = libUtils.MultiplyAndRound(libUtils.DivideAndRound(res.TotalAdUp, totalAmount, 5, libUtils.RoundDown), 100, 2, libUtils.RoundDown)
			res.DyAndroidRate = libUtils.MultiplyAndRound(libUtils.DivideAndRound(res.DyAndroidRechargeAmount, res.TotalAmount, 5, libUtils.RoundDown), 100, 2, libUtils.RoundDown)
			res.DyIosRate = libUtils.MultiplyAndRound(libUtils.DivideAndRound(res.DyIosRechargeAmount, res.TotalAmount, 5, libUtils.RoundDown), 100, 2, libUtils.RoundDown)
			res.WechatAndroidRate = libUtils.MultiplyAndRound(libUtils.DivideAndRound(res.WechatAndroidRechargeAmount, res.TotalAmount, 5, libUtils.RoundDown), 100, 2, libUtils.RoundDown)
			res.WechatIosRate = libUtils.MultiplyAndRound(libUtils.DivideAndRound(res.WechatIosRechargeAmount, res.TotalAmount, 5, libUtils.RoundDown), 100, 2, libUtils.RoundDown)
			res.AliAndroidRate = libUtils.MultiplyAndRound(libUtils.DivideAndRound(res.AliAndroidRechargeAmount, res.TotalAmount, 5, libUtils.RoundDown), 100, 2, libUtils.RoundDown)
			res.AliIosRate = libUtils.MultiplyAndRound(libUtils.DivideAndRound(res.AliIosRechargeAmount, res.TotalAmount, 5, libUtils.RoundDown), 100, 2, libUtils.RoundDown)
		}
		liberr.ErrIsNil(ctx, err, "获取数据失败")

	})
	return
}

func (s *sSChannelRechargeStatistics) GetChannelRechargeExportData(ctx context.Context, req *model.ChannelRechargeStatisticsReq) (listRes []*model.ChannelStatisticsListRes, err error) {

	err = g.Try(ctx, func(ctx context.Context) {

		m := dao.SChannelRechargeStatisticsAnalytic.Ctx(ctx).WithAll().As("sc").
			LeftJoin("s_channel c", "sc.account = c.channel_code").
			LeftJoin("sys_user user", "user.id=c.user_id")
		if req.Remarks != nil && len(req.Remarks) > 0 {
			var remarkStr = libUtils.BuildSqlInStr(req.Remarks)
			listMap, _ := dao.SChannel.DB().GetAll(ctx, "select channel_code from s_channel where remark in("+remarkStr+")")
			if listMap != nil {
				var channelCodeArr string
				for k, v := range listMap {
					channelCodeArr = channelCodeArr + "'" + v.GMap().GetVar("channel_code").String() + "'"
					if k < len(listMap)-1 {
						channelCodeArr = channelCodeArr + ","
					}
				}
				m = m.Where("sc.account in (" + channelCodeArr + ")")
			}
		}
		if req.PitcherIds != nil && len(req.PitcherIds) > 0 {
			strArr := make([]string, len(req.PitcherIds))
			for i, num := range req.PitcherIds {
				strArr[i] = strconv.Itoa(num) // 将每个元素转换为字符串类型
			}
			var str = strings.Join(strArr, ",")
			m = m.Where("c.user_id in (" + str + ")")

		}
		if req.StartTime != "" {
			m = m.Where("sc.create_time>=?", req.StartTime)
		}
		if req.EndTime != "" {
			m = m.Where("sc.create_time<=?", req.EndTime)

		}
		if req.Account != "" {
			m = m.Where("sc.account =?", req.Account)
		}
		if req.Accounts != nil && len(req.Accounts) > 0 {
			m = m.WhereIn("sc.account", req.Accounts)
		}
		if len(req.DeptIds) > 0 {
			m = m.WhereIn("user.dept_id", req.DeptIds)
		}
		fields := []string{" IFNULL(sum(total_amount), 0.00) as totalAmount", "IFNULL(sum(wechat_android_recharge_amount), 0.00) as wechatAndroidRechargeAmount",
			"IFNULL(sum(wechat_ios_recharge_amount), 0.00) as wechatIosRechargeAmount", "IFNULL(sum(dy_recharge_amount), 0.00) as dyRechargeAmount", "IFNULL(sum(dy_android_recharge_amount), 0.00) as dyAndroidRechargeAmount",
			"IFNULL(sum(dy_ios_recharge_amount), 0.00) as dyIosRechargeAmount", "IFNULL(sum(new_user_amount), 0.00) as newUserAmount", "IFNULL(sum(recharge_nums), 0.00) as rechargeNums",
			"IFNULL(sum(ali_android_recharge_amount), 0.00) as aliAndroidRechargeAmount", "IFNULL(sum(ali_ios_recharge_amount), 0.00) as aliIosRechargeAmount", "IFNULL(sum(ali_ios_recharge_amount)+sum(ali_android_recharge_amount), 0.00) as aliRechargeAmount",
			"IFNULL(sum(total_recharge_times), 0.00) as totalRechargeTimes", " IFNULL(sum(account_coin_consume), 0.00) as accountCoinConsume", "IFNULL(sum(total_ad_up), 0.00) as totalAdUp",
			"IFNULL(sum(new_user_recharge_nums), 0.00) as newUserRechargeNums", "ROUND(IFNULL(sum(account_coin_consume), 0.00) / IFNULL(sum(new_user_recharge_nums), 0.00), 2) as newUserPaymentCost",
			"ROUND(IFNULL(sum(total_amount+total_ad_up), 0.00) / IFNULL(sum(recharge_nums), 0.00), 2) as customerPrice", "ROUND(IFNULL(sum(total_recharge_times), 0.00) / IFNULL(sum(recharge_nums), 0.00), 2) as avgRechargeTimes",
			"ROUND(IFNULL(sum(new_user_amount+new_user_ad_up), 0.00) / IFNULL(sum(account_coin_consume), 0.00), 2) as dayRoi", "ROUND(IFNULL(sum(total_amount+total_ad_up), 0.00) / IFNULL(sum(account_coin_consume), 0.00), 2) as totalRoi",
			"IFNULL(sum(active_count), 0.00) as activeCount", "IFNULL(sum(first_recharge_amount), 0.00) as firstRechargeAmount", "IFNULL(sum(first_recharge_nums), 0.00) as firstRechargeNums",
			"ROUND(IFNULL(sum(first_recharge_amount), 0.00) / IFNULL(sum(first_recharge_nums), 0.00), 2) as firstArup",
			"IFNULL(sum(stat_attribution_micro_game_24h_amount), 0.00) as statAttributionMicroGame24hAmount",
			"ROUND(IFNULL(sum(stat_attribution_micro_game_24h_amount), 0.00) / IFNULL(sum(account_coin_consume), 0.00), 2) as attributionMicroGame24hRoi",
			"IFNULL(sum(stat_micro_game_0d_amount), 0.00) as statMicroGame0dAmount",
			"IFNULL(sum(new_user_ad_up), 0.00) as newUserAdUp,IFNULL(sum(total_ad_up), 0.00) as totalAdUp"}
		fields = append(fields, "  account as account")
		fields = append(fields, "  any_value(user.user_name) as pitcher")
		fields = append(fields, "any_value(channel_platform) as channelPlatform")
		var groupBy = ""
		var order string
		if req.OrderBy != "" && !libUtils.IsSQLInjection(req.OrderBy) && !libUtils.IsSQLInjection(req.OrderType) {

			order = req.OrderBy + " " + req.OrderType
		}
		if req.Merge == 0 {
			fields = append(fields, "sc.create_time as createTime")
			groupBy = "sc.create_time, sc.account "
			if req.OrderBy == "" {
				order = "accountCoinConsume desc, totalAmount desc, sc.create_time desc"
			}
		}
		if req.Merge == 1 {
			fields = append(fields, "min(sc.create_time) as startTime")
			fields = append(fields, "max(sc.create_time) as endTime")
			groupBy = " sc.account"
			if req.OrderBy == "" {
				order = "accountCoinConsume desc, totalAmount desc"
			}
		}

		var (
			where g.Map
			ent   *model.SChannelStatisticsListRes
		)
		where, err = sysService.SysUser().GetManSenDataWhere(ctx,
			sysService.Context().GetLoginUser(ctx),
			ent, consts.SearchFiledName)
		liberr.ErrIsNil(ctx, err)
		m = m.Where(where) //添加条件
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		var accountList []string
		err = m.Page(req.PageNum, req.PageSize).Order(order).Fields(fields).Group(groupBy).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		for _, v := range listRes {
			var channelPlatformStr string
			if v.ChannelPlatform == commonConst.ManSenPlatform {
				channelPlatformStr = commonConst.ManSenPlatformStr
			} else if v.ChannelPlatform == commonConst.FanQiePlatform {
				channelPlatformStr = commonConst.FanQiePlatformStr
			} else if v.ChannelPlatform == commonConst.DianZhongPlatform {
				channelPlatformStr = commonConst.DianZhongPlatformStr
			}
			v.ChannelPlatformStr = channelPlatformStr
		}
		//若需要合并
		if req.Merge == 1 {
			for _, v := range listRes {
				v.CreateTime = v.StartTime + "-" + v.EndTime
			}
		}
		for _, v := range listRes {
			accountList = append(accountList, v.Account)
		}
		//通过accountList查询出备注信息
		if accountList != nil {
			var accountStr string = libUtils.BuildSqlInStr(accountList)
			//查询列表信息
			list, _ := dao.SChannel.DB().GetAll(ctx, "select channel_code,remark from s_channel where channel_code in("+accountStr+")")
			if listRes != nil {
				for _, v := range listRes {
					var account = v.Account
					for _, v1 := range list {
						if v1.GMap().Get("channel_code") == account {
							//var value = v1.GMap().Get("remark")
							v.Remark = v1.GMap().GetVar("remark").String()
						}
					}
				}

			}

		}

	})

	return
}

func buildChannelRechargeSql(ctx context.Context, req *model.ChannelRechargeStatisticsReq) *gdb.Model {
	m := dao.SChannelRechargeStatisticsAnalytic.Ctx(ctx).WithAll().As("sc").
		LeftJoin("s_channel c", "sc.account = c.channel_code").
		LeftJoin("sys_user user", "user.id=c.user_id").
		LeftJoin("fq_ad_account_channel fq", "fq.channel_distributor_id=c.fq_distributor_id").
		LeftJoin("dz_ad_account_channel dz", "dz.channel_id=c.dz_channel_id")
	if req.Remarks != nil && len(req.Remarks) > 0 {
		var remarkStr = libUtils.BuildSqlInStr(req.Remarks)
		listMap, _ := dao.SChannel.DB().GetAll(ctx, "select channel_code from s_channel where remark in("+remarkStr+")")
		if listMap != nil {
			var channelCodeArr string
			for k, v := range listMap {
				channelCodeArr = channelCodeArr + "'" + v.GMap().GetVar("channel_code").String() + "'"
				if k < len(listMap)-1 {
					channelCodeArr = channelCodeArr + ","
				}
			}
			m = m.Where("sc.account in (" + channelCodeArr + ")")
		}
	}
	if req.Accounts != nil && len(req.Accounts) > 0 {
		var str = libUtils.BuildSqlInStr(req.Accounts)
		m = m.Where("sc.account in (" + str + ")")
	}
	if req.PitcherIds != nil && len(req.PitcherIds) > 0 {
		strArr := make([]string, len(req.PitcherIds))
		for i, num := range req.PitcherIds {
			strArr[i] = strconv.Itoa(num) // 将每个元素转换为字符串类型
		}
		var str = strings.Join(strArr, ",")
		m = m.Where("c.user_id in (" + str + ")")
	}
	if req.StartTime != "" {
		m = m.Where("sc.create_time>=?", req.StartTime)
	}
	if req.EndTime != "" {
		m = m.Where("sc.create_time<=?", req.EndTime)
	}
	if req.Account != "" {
		m = m.Where("sc.account =?", req.Account)
	}
	if len(req.DeptIds) > 0 {
		m = m.WhereIn("user.dept_id", req.DeptIds)
	}
	if len(req.ParentIds) > 0 {
		m = m.WhereIn("sc.parent_id", req.ParentIds)
	}
	if req.ChannelPlatform > 0 {
		m = m.Where("c.channel_platform =?", req.ChannelPlatform)
	}
	if len(req.FqDistributorIds) > 0 {
		m = m.WhereIn("fq.distributor_id", req.FqDistributorIds)
	}
	if len(req.FqChannelDistributorIds) > 0 {
		m = m.WhereIn("c.fq_distributor_id", req.FqChannelDistributorIds)
	}
	if len(req.DzAccountIds) > 0 {
		m = m.WhereIn("dz.account_id", req.DzAccountIds)
	}
	if len(req.DzChannelIds) > 0 {
		m = m.WhereIn("dz.channel_id", req.DzChannelIds)
	}
	var (
		where g.Map
		ent   *model.SChannelStatisticsListRes
	)
	where, _ = sysService.SysUser().GetManSenDataWhere(ctx,
		sysService.Context().GetLoginUser(ctx),
		ent, consts.SearchFiledName)
	m = m.Where(where) //添加条件
	return m
}

func (s *sSChannelRechargeStatistics) ChannelRechargeStat(ctx context.Context, req *model.ChannelRechargeStatisticsReq) (listRes *model.SChannelRechargeStatisticsRes, err error) {
	//TODO implement me
	listRes = new(model.SChannelRechargeStatisticsRes)
	err = g.Try(ctx, func(ctx context.Context) {

		m := buildChannelRechargeSql(ctx, req)
		//查询所有总数
		totalAmountMap := buildChannelRechargeSql(ctx, req)
		//汇总统计
		channelSumMap := buildChannelRechargeSql(ctx, req)

		fields := []string{" IFNULL(sum(total_amount), 0.00) as totalAmount", "IFNULL(sum(wechat_android_recharge_amount), 0.00) as wechatAndroidRechargeAmount",
			"IFNULL(sum(wechat_ios_recharge_amount), 0.00) as wechatIosRechargeAmount", "IFNULL(sum(dy_recharge_amount), 0.00) as dyRechargeAmount", "IFNULL(sum(dy_android_recharge_amount), 0.00) as dyAndroidRechargeAmount",
			"IFNULL(sum(dy_ios_recharge_amount), 0.00) as dyIosRechargeAmount", "IFNULL(sum(new_user_amount), 0.00) as newUserAmount", "IFNULL(sum(recharge_nums), 0.00) as rechargeNums",
			"IFNULL(sum(total_recharge_times), 0.00) as totalRechargeTimes", " IFNULL(sum(account_coin_consume), 0.00) as accountCoinConsume",
			"IFNULL(sum(new_user_recharge_nums), 0.00) as newUserRechargeNums", "ROUND(IFNULL(sum(account_coin_consume), 0.00) / IFNULL(sum(new_user_recharge_nums), 0.00), 2) as newUserPaymentCost",
			"ROUND(IFNULL(sum(total_amount+total_ad_up), 0.00) / IFNULL(sum(recharge_nums), 0.00), 2) as customerPrice", "ROUND(IFNULL(sum(total_recharge_times), 0.00) / IFNULL(sum(recharge_nums), 0.00), 2) as avgRechargeTimes",
			"ROUND(IFNULL(sum(new_user_amount+new_user_ad_up), 0.00) / IFNULL(sum(account_coin_consume), 0.00), 2) as dayRoi", "ROUND(IFNULL(sum(total_amount+total_ad_up), 0.00) / IFNULL(sum(account_coin_consume), 0.00), 2) as totalRoi",
			"IFNULL(sum(active_count), 0.00) as activeCount", "IFNULL(sum(first_recharge_amount), 0.00) as firstRechargeAmount", "IFNULL(sum(first_recharge_nums), 0.00) as firstRechargeNums",
			"ROUND(IFNULL(sum(first_recharge_amount), 0.00) / IFNULL(sum(first_recharge_nums), 0.00), 2) as firstArup",
			"IFNULL(sum(stat_attribution_micro_game_24h_amount), 0.00) as statAttributionMicroGame24hAmount",
			"ROUND(IFNULL(sum(stat_attribution_micro_game_24h_amount), 0.00) / IFNULL(sum(account_coin_consume), 0.00), 2) as attributionMicroGame24hRoi",
			"IFNULL(sum(stat_micro_game_0d_amount), 0.00) as statMicroGame0dAmount",
			"IFNULL(sum(ali_android_recharge_amount), 0.00) as aliAndroidRechargeAmount",
			"IFNULL(sum(ali_ios_recharge_amount), 0.00) as aliIosRechargeAmount",
			"IFNULL(sum(ali_android_recharge_amount+ali_ios_recharge_amount), 0.00) as aliRechargeAmount",
			"IFNULL(sum(new_user_ad_up), 0.00) as newUserAdUp,IFNULL(sum(total_ad_up), 0.00) as totalAdUp,any_value(sc.parent_id) parentId"}
		fields = append(fields, "  account as account")
		fields = append(fields, "  any_value(user.user_name) as pitcher")
		fields = append(fields, "  any_value(user.id) as pitcher_id")
		fields = append(fields, "any_value(channel_platform) as channelPlatform")
		fields = append(fields, "any_value(fq_distributor_id) as fqChannelDistributorId")
		var groupBy = ""
		var order string
		if req.OrderBy != "" && !libUtils.IsSQLInjection(req.OrderBy) && !libUtils.IsSQLInjection(req.OrderType) {

			order = req.OrderBy + " " + req.OrderType
		}
		if req.Merge == 0 {
			fields = append(fields, "sc.create_time as createTime")
			groupBy = "sc.create_time, sc.account "
			if req.OrderBy == "" {
				order = "accountCoinConsume desc, totalAmount desc, sc.create_time desc"
			}
		}
		if req.Merge == 1 {
			fields = append(fields, "min(sc.create_time) as startTime")
			fields = append(fields, "max(sc.create_time) as endTime")
			groupBy = " sc.account"
			if req.OrderBy == "" {
				order = "accountCoinConsume desc, totalAmount desc"
			}
		}

		listRes.Total, err = m.Order(order).Group(groupBy).Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		var accountList []string
		var res []*model.ChannelStatisticsListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Fields(fields).Group(groupBy).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		// 查询番茄渠道名称
		fqChannelDistributorIds := make([]int64, 0)
		for _, v := range res {
			fqChannelDistributorIds = append(fqChannelDistributorIds, gconv.Int64(v.FqChannelDistributorId))
		}
		fqAdAccountChannels, _ := adService.FqAdAccountChannel().GetByChannelDistributorIds(ctx, fqChannelDistributorIds)
		fqDistributorIds := make([]string, 0)
		for _, v := range fqAdAccountChannels {
			fqDistributorIds = append(fqDistributorIds, gconv.String(v.DistributorId))
		}
		fqAdAccounts, _ := adService.FqAdAccount().GetByDistributorIds(ctx, fqDistributorIds)
		listRes.List = make([]*model.ChannelStatisticsListRes, len(res))
		for k, v := range res {
			accountList = append(accountList, v.Account)
			var channelPlatformStr string
			if v.ChannelPlatform == commonConst.ManSenPlatform {
				channelPlatformStr = commonConst.ManSenPlatformStr
			} else if v.ChannelPlatform == commonConst.FanQiePlatform {
				channelPlatformStr = commonConst.FanQiePlatformStr
			} else if v.ChannelPlatform == commonConst.DianZhongPlatform {
				channelPlatformStr = commonConst.DianZhongPlatformStr
			}
			var fqChannelDistributor string
			var fqAppName string
			var fqDistributor string
			for _, fqAdAccountChannel := range fqAdAccountChannels {
				if fqAdAccountChannel.ChannelDistributorId == gconv.Int64(v.FqChannelDistributorId) {
					for _, fqAdAccount := range fqAdAccounts {
						if fqAdAccount.DistributorId == fqAdAccountChannel.DistributorId {
							fqDistributor = fqAdAccount.AccountName
							break
						}
					}
					fqChannelDistributor = fqAdAccountChannel.NickName
					fqAppName = fqAdAccountChannel.AppName
					break
				}
			}
			listRes.List[k] = &model.ChannelStatisticsListRes{
				CreateTime:                        v.CreateTime,
				DistributorId:                     v.DistributorId,
				PitcherId:                         v.PitcherId,
				Account:                           v.Account,
				ChannelType:                       v.ChannelType,
				NewUserAmount:                     v.NewUserAmount,
				TotalAmount:                       v.TotalAmount,
				RechargeNums:                      v.RechargeNums,
				CustomerPrice:                     v.CustomerPrice,
				AvgRechargeTimes:                  v.AvgRechargeTimes,
				AccountCoinConsume:                v.AccountCoinConsume,
				DayRoi:                            v.DayRoi,
				TotalRoi:                          v.TotalRoi,
				TotalRechargeTimes:                v.TotalRechargeTimes,
				NewUserRechargeNums:               v.NewUserRechargeNums,
				WechatAndroidRechargeAmount:       v.WechatAndroidRechargeAmount,
				WechatIosRechargeAmount:           v.WechatIosRechargeAmount,
				WechatRechargeAmount:              libUtils.AddAndRound(v.WechatIosRechargeAmount, v.WechatAndroidRechargeAmount, 2, libUtils.RoundDown),
				DyRechargeAmount:                  v.DyRechargeAmount,
				StartTime:                         v.StartTime,
				EndTime:                           v.EndTime,
				Pitcher:                           v.Pitcher,
				NewUserPaymentCost:                v.NewUserPaymentCost,
				DyAndroidRechargeAmount:           v.DyAndroidRechargeAmount,
				DyIosRechargeAmount:               v.DyIosRechargeAmount,
				AliAndroidRechargeAmount:          v.AliAndroidRechargeAmount,
				AliIosRechargeAmount:              v.AliIosRechargeAmount,
				AliRechargeAmount:                 v.AliRechargeAmount,
				TotalAdUp:                         v.TotalAdUp,
				NewUserAdUp:                       v.NewUserAdUp,
				TheaterTitle:                      v.TheaterTitle,
				ActiveCount:                       v.ActiveCount,
				FirstRechargeAmount:               v.FirstRechargeAmount,
				FirstRechargeNums:                 v.FirstRechargeNums,
				FirstArup:                         v.FirstArup,
				ChannelPlatform:                   v.ChannelPlatform,
				ChannelPlatformStr:                channelPlatformStr,
				FqChannelDistributorId:            v.FqChannelDistributorId,
				FqChannelDistributor:              fqChannelDistributor,
				FqAppName:                         fqAppName,
				FqDistributor:                     fqDistributor,
				StatAttributionMicroGame24hAmount: v.StatAttributionMicroGame24hAmount,
				AttributionMicroGame24hRoi:        v.AttributionMicroGame24hRoi,
				StatMicroGame0dAmount:             v.StatMicroGame0dAmount,
			}
		}
		//若需要合并
		if req.Merge == 1 {
			for _, v := range listRes.List {
				v.CreateTime = v.StartTime + " - " + v.EndTime
			}
		}
		//通过accountList查询出备注信息
		if accountList != nil {

			var accountStr string = libUtils.BuildSqlInStr(accountList)
			//查询列表信息
			list, _ := dao.SChannel.DB().GetAll(ctx, "select channel_code,remark from s_channel where channel_code in("+accountStr+")")
			adLink, _ := dao.AdDiversionLink.DB().GetAll(ctx, "select account, video_id,dz_book_name,fq_book_name from ad_diversion_link where account in("+accountStr+")")
			if listRes.List != nil {
				for _, v := range listRes.List {
					var account = v.Account
					for _, v1 := range list {
						if v1.GMap().Get("channel_code") == account {
							//var value = v1.GMap().Get("remark")
							v.Remark = v1.GMap().GetVar("remark").String()
							break
						}
					}
					for _, link := range adLink {
						if link.GMap().Get("account") == account {
							if link.GMap().GetVar("video_id").Int() > 0 {
								//获取短剧名字
								videoInfo, _ := theaterService.TheaterInfo().GetById(ctx, gconv.Uint(link.GMap().GetVar("video_id").Int()))
								if videoInfo != nil {
									v.TheaterTitle = videoInfo.Title
									break
								}
							} else if len(link.GMap().GetVar("dz_book_name").String()) > 0 {
								v.TheaterTitle = link.GMap().GetVar("dz_book_name").String()
								break
							} else if len(link.GMap().GetVar("fq_book_name").String()) > 0 {
								v.TheaterTitle = link.GMap().GetVar("fq_book_name").String()
								break
							}

						}
					}
				}

			}

		}

		//查询当日新增用户总充值
		var newUserTotalCount float64
		//查询总充值金额
		var totalAmount float64
		record, err2 := totalAmountMap.Fields(" IFNULL(sum(total_amount), 0.00) as totalAmount," +
			"IFNULL(sum(new_user_amount), 0.00) as newUserTotalCount,IFNULL(sum(total_ad_up), 0.00) as totalAdUp,IFNULL(sum(new_user_ad_up), 0.00) as newUserAdUp").One()
		totalAmount, _ = strconv.ParseFloat(record["totalAmount"].String(), 64)
		newUserTotalCount, _ = strconv.ParseFloat(record["newUserTotalCount"].String(), 64)
		totalAdUp, _ := strconv.ParseFloat(record["totalAdUp"].String(), 64)
		newUserAdUp, _ := strconv.ParseFloat(record["newUserAdUp"].String(), 64)
		liberr.ErrIsNil(ctx, err2, "获取总充值失败")
		//汇总总计
		sumField := []string{" IFNULL(sum(total_amount), 0.00) as totalAmount", "IFNULL(sum(wechat_android_recharge_amount), 0.00) as wechatAndroidRechargeAmount",
			"IFNULL(sum(wechat_ios_recharge_amount), 0.00) as wechatIosRechargeAmount", "IFNULL(sum(dy_recharge_amount), 0.00) as dyRechargeAmount", "IFNULL(sum(dy_android_recharge_amount), 0.00) as dyAndroidRechargeAmount",
			"IFNULL(sum(dy_ios_recharge_amount), 0.00) as dyIosRechargeAmount", "IFNULL(sum(total_ad_up), 0.00) as totalAdUp",
			"IFNULL(sum(new_user_amount), 0.00) as newUserAmount", "IFNULL(sum(recharge_nums), 0.00) as rechargeNums", "IFNULL(sum(total_recharge_times), 0.00) as totalRechargeTimes",
			" IFNULL(sum(account_coin_consume), 0.00) as accountCoinConsume", "IFNULL(sum(new_user_recharge_nums), 0.00) as newUserRechargeNums", "ROUND(IFNULL(sum(account_coin_consume), 0.00) / IFNULL(sum(new_user_recharge_nums), 0.00), 2) as newUserPaymentCost",
			"ROUND(IFNULL(sum(total_amount+total_ad_up), 0.00) / IFNULL(sum(recharge_nums), 0.00), 2) as customerPrice", "ROUND(IFNULL(sum(total_recharge_times), 0.00) / IFNULL(sum(recharge_nums), 0.00), 2) as avgRechargeTimes",
			"ROUND(IFNULL(sum(new_user_amount+new_user_ad_up), 0.00) / IFNULL(sum(account_coin_consume), 0.00), 2) as dayRoi", "ROUND(IFNULL(sum(total_amount+total_ad_up), 0.00) / IFNULL(sum(account_coin_consume), 0.00), 2) as totalRoi",
			"IFNULL(sum(active_count), 0.00) as activeCount", "IFNULL(sum(first_recharge_amount), 0.00) as firstRechargeAmount", "IFNULL(sum(first_recharge_nums), 0.00) as firstRechargeNums",
			"ROUND(IFNULL(sum(first_recharge_amount), 0.00) / IFNULL(sum(first_recharge_nums), 0.00), 2) as firstArup",
			"IFNULL(sum(stat_attribution_micro_game_24h_amount), 0.00) as statAttributionMicroGame24hAmount",
			"ROUND(IFNULL(sum(stat_attribution_micro_game_24h_amount), 0.00) / IFNULL(sum(account_coin_consume), 0.00), 2) as attributionMicroGame24hRoi",
			"IFNULL(sum(stat_micro_game_0d_amount), 0.00) as statMicroGame0dAmount",
			"IFNULL(sum(ali_android_recharge_amount), 0.00) as aliAndroidRechargeAmount",
			"IFNULL(sum(ali_ios_recharge_amount), 0.00) as aliIosRechargeAmount",
			"IFNULL(sum(ali_android_recharge_amount+ali_ios_recharge_amount), 0.00) as aliRechargeAmount",
			"IFNULL(sum(new_user_ad_up), 0.00) as newUserAdUp,IFNULL(sum(total_ad_up), 0.00) as totalAdUp"}
		channelRecord, err2 := channelSumMap.Fields(sumField).One()
		listRes.TotalAmount = totalAmount
		listRes.TotalAdUp = totalAdUp
		listRes.NewUserAdUp = newUserAdUp
		listRes.NewUserAmount = newUserTotalCount
		listRes.SummaryStat = new(model.SummaryStat)
		libUtils.RecordToEntity(channelRecord, listRes.SummaryStat)
		listRes.SummaryStat.WechatRechargeAmount = libUtils.AddAndRound(listRes.SummaryStat.WechatIosRechargeAmount, listRes.SummaryStat.WechatAndroidRechargeAmount, 2, libUtils.RoundDown)

		if len(req.ParentIds) > 0 {
			var targetAccounts []string
			for _, v := range listRes.List {
				if v.ChannelPlatform == commonConst.ManSenPlatform {
					targetAccounts = append(targetAccounts, v.Account)
				}
			}
			if len(targetAccounts) > 0 {
				var fqRechargeStat = &model.ChannelStatisticsListRes{}
				_ = totalAmountMap.Where("c.channel_platform", commonConst.FanQiePlatform).
					Fields("IFNULL(sum(total_amount), 0.00) as totalAmount").
					Fields("IFNULL(sum(total_ad_up), 0.00) as totalAdUp").
					Scan(&fqRechargeStat)
				req.TargetAccounts = targetAccounts
				_ = s.ChannelVideoRechargeStat(ctx, req, listRes, fqRechargeStat)
			}
		}
	})

	return
}

// ChannelVideoRechargeStat 需求：请求参数带短剧，需要实时查询总充值金额和广告收入
func (s *sSChannelRechargeStatistics) ChannelVideoRechargeStat(
	ctx context.Context,
	req *model.ChannelRechargeStatisticsReq,
	listRes *model.SChannelRechargeStatisticsRes,
	fqRechargeStat *model.ChannelStatisticsListRes) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 总充值金额
		videoRechargeStats, _ := orderService.OrderInfo().CalcVideoRechargeStat(ctx, req)
		sumTotalAmount, _ := orderService.OrderInfo().CalcSumVideoRechargeStat(ctx, req)
		// 广告收入
		videoAdUpStats, _ := service.MMemberAdCallback().CalcVideoAdUpStat(ctx, req)
		sumAdUp, _ := service.MMemberAdCallback().CalcSumVideoAdUpStat(ctx, req)
		for _, v := range listRes.List {
			for _, stat := range videoRechargeStats {
				if req.Merge == 1 {
					if v.Account == stat.Account {
						v.TotalAmount = stat.TotalAmount
						break
					}
				} else {
					if v.Account == stat.Account && v.CreateTime == stat.CreateTime {
						v.TotalAmount = stat.TotalAmount
						break
					}
				}
			}
			for _, stat := range videoAdUpStats {
				if req.Merge == 1 {
					if v.Account == stat.Account {
						v.TotalAdUp = stat.TotalAdUp
						break
					}
				} else {
					if v.Account == stat.Account && v.CreateTime == stat.CreateTime {
						v.TotalAdUp = stat.TotalAdUp
						break
					}
				}
			}
		}
		listRes.TotalAmount = fqRechargeStat.TotalAmount + sumTotalAmount
		listRes.TotalAdUp = fqRechargeStat.TotalAdUp + sumAdUp
		listRes.SummaryStat.TotalAmount = fqRechargeStat.TotalAmount + sumTotalAmount
		listRes.SummaryStat.TotalAdUp = fqRechargeStat.TotalAdUp + sumAdUp
	})
	return
}

func (s *sSChannelRechargeStatistics) GetPitcherRechargeExportData(ctx context.Context, req *model.PitcherRechargeStatisticsSearchReq) (listRes []*model.PitcherRechargeStatisticsListRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {

		m := buildPitcherRechargeSql(ctx, req)
		fields := []string{" IFNULL(sum(total_amount), 0.00) as totalAmount", "IFNULL(sum(wechat_android_recharge_amount), 0.00) as wechatAndroidRechargeAmount",
			"IFNULL(sum(wechat_ios_recharge_amount), 0.00) as wechatIosRechargeAmount", "IFNULL(sum(wechat_ios_recharge_amount), 0.00)+IFNULL(sum(wechat_android_recharge_amount), 0.00) as wechatRechargeAmount", "IFNULL(sum(dy_recharge_amount), 0.00) as dyRechargeAmount", "IFNULL(sum(dy_android_recharge_amount), 0.00) as dyAndroidRechargeAmount",
			"IFNULL(sum(dy_ios_recharge_amount), 0.00) as dyIosRechargeAmount", "IFNULL(sum(new_user_amount), 0.00) as newUserAmount", "IFNULL(sum(recharge_nums), 0.00) as rechargeNums", "IFNULL(sum(total_recharge_times), 0.00) as totalRechargeTimes",
			" IFNULL(sum(account_coin_consume), 0.00) as accountCoinConsume", "IFNULL(sum(new_user_recharge_nums), 0.00) as newUserRechargeNums", "ROUND(IFNULL(sum(account_coin_consume), 0.00) / IFNULL(sum(new_user_recharge_nums), 0.00), 2) as newUserPaymentCost",
			"ROUND(IFNULL(sum(total_amount), 0.00) / IFNULL(sum(recharge_nums), 0.00), 2) as customerPrice", "ROUND(IFNULL(sum(total_recharge_times), 0.00) / IFNULL(sum(recharge_nums), 0.00), 2) as avgRechargeTimes",
			"ROUND(IFNULL(sum(new_user_amount+new_user_ad_up), 0.00) / IFNULL(sum(account_coin_consume), 0.00), 2) as dayRoi", "ROUND(IFNULL(sum(total_amount+total_ad_up), 0.00) / IFNULL(sum(account_coin_consume), 0.00), 2) as totalRoi",
			"IFNULL(sum(ali_android_recharge_amount), 0.00) as aliAndroidRechargeAmount",
			"IFNULL(sum(ali_ios_recharge_amount), 0.00) as aliIosRechargeAmount",
			"IFNULL(sum(ali_android_recharge_amount+ali_ios_recharge_amount), 0.00) as aliRechargeAmount",
			"IFNULL(sum(stat_attribution_micro_game_24h_amount), 0.00) as statAttributionMicroGame24hAmount",
			"ROUND(IFNULL(sum(stat_attribution_micro_game_24h_amount), 0.00) / IFNULL(sum(account_coin_consume), 0.00), 2) as attributionMicroGame24hRoi",
			"IFNULL(sum(stat_micro_game_0d_amount), 0.00) as statMicroGame0dAmount",
			"IFNULL(sum(total_ad_up), 0.00) as totalAdUp,IFNULL(sum(new_user_ad_up), 0.00) as newUserAdUp"}
		var groupBy = ""
		var order string
		if req.OrderBy != "" && !libUtils.IsSQLInjection(req.OrderBy) && !libUtils.IsSQLInjection(req.OrderType) {

			order = req.OrderBy + " " + req.OrderType
		}
		if req.Merge == 0 {
			fields = append(fields, "sc.create_time as createTime")
			fields = append(fields, "c.channel_platform as channelPlatform")
			groupBy = "sc.create_time, c.user_id, c.channel_platform"
			if req.OrderBy == "" {
				order = "accountCoinConsume desc, totalAmount desc, sc.create_time desc"
			}
		}
		if req.Merge == 1 {
			fields = append(fields, "min(sc.create_time) as startTime")
			fields = append(fields, "max(sc.create_time) as endTime")
			groupBy = "c.user_id"
			if req.OrderBy == "" {
				order = "accountCoinConsume desc, totalAmount desc"
			}
		}

		fields = append(fields, "  any_value(user.user_name) as pitcher")

		//var res []*model.PitcherRechargeStatisticsListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Fields(fields).Group(groupBy).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		//若需要合并
		if req.Merge == 1 {
			for _, v := range listRes {
				v.CreateTime = v.StartTime + "-" + v.EndTime
			}
		}
	})

	return
}
func buildPitcherRechargeSql(ctx context.Context, req *model.PitcherRechargeStatisticsSearchReq) *gdb.Model {
	m := dao.SChannelRechargeStatisticsAnalytic.Ctx(ctx).WithAll().As("sc").
		LeftJoin("s_channel c", "sc.account = c.channel_code").
		LeftJoin("sys_user user", "user.id=c.user_id").
		LeftJoin("fq_ad_account_channel fq", "fq.channel_distributor_id=c.fq_distributor_id").
		LeftJoin("dz_ad_account_channel dz", "dz.channel_id=c.dz_channel_id")
	if req.DistributorId != "" {
		m = m.Where(dao.SChannelRechargeStatistics.Columns().DistributorId+" = ?", gconv.Int(req.DistributorId))
	}
	if req.PitcherId != "" {
		m = m.Where(dao.SChannelRechargeStatistics.Columns().PitcherId+" = ?", gconv.Int(req.PitcherId))

	}
	if req.Account != "" {
		m = m.Where(dao.SChannelRechargeStatistics.Columns().Account+" = ?", req.Account)
	}
	if req.ChannelType != "" {
		m = m.Where(dao.SChannelRechargeStatistics.Columns().ChannelType+" = ?", gconv.Int(req.ChannelType))
	}
	if req.StartTime != "" {
		m = m.Where(" sc.create_time>= ?", req.StartTime)
	}
	if req.EndTime != "" {
		m = m.Where(" sc.create_time<= ?", req.EndTime)
	}
	if len(req.DeptIds) > 0 {
		m = m.WhereIn("user.dept_id", req.DeptIds)
	}
	if len(req.ChannelTypes) > 0 {
		m = m.WhereIn("sc.channel_type", req.ChannelTypes)
	}
	if len(req.PitcherIds) > 0 {
		m = m.WhereIn("c.user_id", req.PitcherIds)
	}
	if req.ChannelPlatform > 0 {
		m = m.Where("c.channel_platform", req.ChannelPlatform)
	}
	if len(req.FqDistributorIds) > 0 {
		m = m.WhereIn("fq.distributor_id", req.FqDistributorIds)
	}
	if len(req.FqChannelDistributorIds) > 0 {
		m = m.WhereIn("c.fq_distributor_id", req.FqChannelDistributorIds)
	}
	if len(req.DzAccountIds) > 0 {
		m = m.WhereIn("dz.account_id", req.DzAccountIds)
	}
	if len(req.DzChannelIds) > 0 {
		m = m.WhereIn("dz.channel_id", req.DzChannelIds)
	}
	var (
		where g.Map
		ent   *model.PitcherRechargeStatisticsListRes
	)
	where, _ = sysService.SysUser().GetManSenDataWhere(ctx,
		sysService.Context().GetLoginUser(ctx),
		ent, consts.SearchFiledName)
	m = m.Where(where) //添加条件

	return m
}

func (s *sSChannelRechargeStatistics) PitcherRechargeStat(ctx context.Context, req *model.PitcherRechargeStatisticsSearchReq) (listRes *model.PitcherRechargeStatisticsSearchRes, err error) {
	//TODO implement me
	listRes = new(model.PitcherRechargeStatisticsSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {

		m := buildPitcherRechargeSql(ctx, req)
		//查询所有总数
		totalAmountMap := buildPitcherRechargeSql(ctx, req)
		//汇总统计
		channelSumMap := buildPitcherRechargeSql(ctx, req)

		fields := []string{" IFNULL(sum(total_amount), 0.00) as totalAmount", "IFNULL(sum(wechat_android_recharge_amount), 0.00) as wechatAndroidRechargeAmount",
			"IFNULL(sum(wechat_ios_recharge_amount), 0.00) as wechatIosRechargeAmount", "IFNULL(sum(wechat_ios_recharge_amount), 0.00)+IFNULL(sum(wechat_android_recharge_amount), 0.00) as wechatRechargeAmount", "IFNULL(sum(dy_recharge_amount), 0.00) as dyRechargeAmount", "IFNULL(sum(dy_android_recharge_amount), 0.00) as dyAndroidRechargeAmount",
			"IFNULL(sum(dy_ios_recharge_amount), 0.00) as dyIosRechargeAmount", "IFNULL(sum(new_user_amount), 0.00) as newUserAmount", "IFNULL(sum(recharge_nums), 0.00) as rechargeNums", "IFNULL(sum(total_recharge_times), 0.00) as totalRechargeTimes",
			" IFNULL(sum(account_coin_consume), 0.00) as accountCoinConsume", "IFNULL(sum(new_user_recharge_nums), 0.00) as newUserRechargeNums", "ROUND(IFNULL(sum(account_coin_consume), 0.00) / IFNULL(sum(new_user_recharge_nums), 0.00), 2) as newUserPaymentCost",
			"ROUND(IFNULL(sum(total_amount), 0.00) / IFNULL(sum(recharge_nums), 0.00), 2) as customerPrice", "ROUND(IFNULL(sum(total_recharge_times), 0.00) / IFNULL(sum(recharge_nums), 0.00), 2) as avgRechargeTimes",
			"ROUND(IFNULL(sum(new_user_amount+new_user_ad_up), 0.00) / IFNULL(sum(account_coin_consume), 0.00), 2) as dayRoi", "ROUND(IFNULL(sum(total_amount+total_ad_up), 0.00) / IFNULL(sum(account_coin_consume), 0.00), 2) as totalRoi",
			"IFNULL(sum(ali_android_recharge_amount), 0.00) as aliAndroidRechargeAmount",
			"IFNULL(sum(ali_ios_recharge_amount), 0.00) as aliIosRechargeAmount",
			"IFNULL(sum(ali_android_recharge_amount+ali_ios_recharge_amount), 0.00) as aliRechargeAmount",
			"IFNULL(sum(stat_attribution_micro_game_24h_amount), 0.00) as statAttributionMicroGame24hAmount",
			"ROUND(IFNULL(sum(stat_attribution_micro_game_24h_amount), 0.00) / IFNULL(sum(account_coin_consume), 0.00), 2) as attributionMicroGame24hRoi",
			"IFNULL(sum(stat_micro_game_0d_amount), 0.00) as statMicroGame0dAmount",
			"IFNULL(sum(total_ad_up), 0.00) as totalAdUp,IFNULL(sum(new_user_ad_up), 0.00) as newUserAdUp"}
		var groupBy = ""
		var order string

		if req.OrderBy != "" && !libUtils.IsSQLInjection(req.OrderBy) && !libUtils.IsSQLInjection(req.OrderType) {

			order = req.OrderBy + " " + req.OrderType
		}
		if req.Merge == 0 {
			fields = append(fields, "sc.create_time as createTime")
			fields = append(fields, "c.channel_platform as channelPlatform")
			groupBy = "sc.create_time, c.user_id, c.channel_platform"
			if req.OrderBy == "" {
				order = "accountCoinConsume desc, totalAmount desc, sc.create_time desc"
			}
		}
		if req.Merge == 1 {
			fields = append(fields, "min(sc.create_time) as startTime")
			fields = append(fields, "max(sc.create_time) as endTime")
			groupBy = " c.user_id"
			if req.OrderBy == "" {
				order = "accountCoinConsume desc, totalAmount desc"
			}
		}

		fields = append(fields, "  any_value(user.user_name) as pitcher")

		listRes.Total, err = m.Order(order).Group(groupBy).Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		var res []*model.PitcherRechargeStatisticsListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Fields(fields).Group(groupBy).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.PitcherRechargeStatisticsListRes, len(res))
		for k, v := range res {
			var channelPlatformStr string
			if v.ChannelPlatform == commonConst.ManSenPlatform {
				channelPlatformStr = commonConst.ManSenPlatformStr
			} else if v.ChannelPlatform == commonConst.FanQiePlatform {
				channelPlatformStr = commonConst.FanQiePlatformStr
			} else if v.ChannelPlatform == commonConst.DianZhongPlatform {
				channelPlatformStr = commonConst.DianZhongPlatformStr
			}
			listRes.List[k] = &model.PitcherRechargeStatisticsListRes{
				CreateTime:                        v.CreateTime,
				DistributorId:                     v.DistributorId,
				PitcherId:                         v.PitcherId,
				Account:                           v.Account,
				ChannelType:                       v.ChannelType,
				NewUserAmount:                     v.NewUserAmount,
				TotalAmount:                       v.TotalAmount,
				RechargeNums:                      v.RechargeNums,
				CustomerPrice:                     v.CustomerPrice,
				AvgRechargeTimes:                  v.AvgRechargeTimes,
				AccountCoinConsume:                v.AccountCoinConsume,
				DayRoi:                            v.DayRoi,
				TotalRoi:                          v.TotalRoi,
				TotalRechargeTimes:                v.TotalRechargeTimes,
				NewUserRechargeNums:               v.NewUserRechargeNums,
				WechatAndroidRechargeAmount:       v.WechatAndroidRechargeAmount,
				WechatIosRechargeAmount:           v.WechatIosRechargeAmount,
				WechatRechargeAmount:              libUtils.AddAndRound(v.WechatIosRechargeAmount, v.WechatAndroidRechargeAmount, 2, libUtils.RoundDown),
				DyRechargeAmount:                  v.DyRechargeAmount,
				StartTime:                         v.StartTime,
				EndTime:                           v.EndTime,
				Pitcher:                           v.Pitcher,
				DyIosRechargeAmount:               v.DyIosRechargeAmount,
				DyAndroidRechargeAmount:           v.DyAndroidRechargeAmount,
				AliAndroidRechargeAmount:          v.AliAndroidRechargeAmount,
				AliIosRechargeAmount:              v.AliIosRechargeAmount,
				AliRechargeAmount:                 v.AliRechargeAmount,
				NewUserAdUp:                       v.NewUserAdUp,
				TotalAdUp:                         v.TotalAdUp,
				ChannelPlatform:                   v.ChannelPlatform,
				ChannelPlatformStr:                channelPlatformStr,
				StatAttributionMicroGame24hAmount: v.StatAttributionMicroGame24hAmount,
				AttributionMicroGame24hRoi:        v.AttributionMicroGame24hRoi,
				StatMicroGame0dAmount:             v.StatMicroGame0dAmount,
			}
		}
		//若需要合并
		if req.Merge == 1 {
			for _, v := range listRes.List {
				v.CreateTime = v.StartTime + "-" + v.EndTime
			}
		}

		//查询总充值金额
		record, err2 := totalAmountMap.Fields(" IFNULL(sum(total_amount), 0.00) as totalAmount,IFNULL(sum(new_user_amount), 0.00) as newUserTotalCount," +
			"IFNULL(sum(total_ad_up), 0.00) as totalAdUp,IFNULL(sum(new_user_ad_up), 0.00) as newUserAdUp").One()
		totalAmount, _ := strconv.ParseFloat(record["totalAmount"].String(), 64)
		newUserTotalCount, _ := strconv.ParseFloat(record["newUserTotalCount"].String(), 64)
		totalAdUp, _ := strconv.ParseFloat(record["totalAdUp"].String(), 64)
		newUserAdUp, _ := strconv.ParseFloat(record["newUserAdUp"].String(), 64)
		liberr.ErrIsNil(ctx, err2, "查询总数据失败")
		//汇总总计
		sumField := []string{" IFNULL(sum(total_amount), 0.00) as totalAmount", "IFNULL(sum(wechat_android_recharge_amount), 0.00) as wechatAndroidRechargeAmount",
			"IFNULL(sum(wechat_ios_recharge_amount), 0.00) as wechatIosRechargeAmount", "IFNULL(sum(dy_recharge_amount), 0.00) as dyRechargeAmount", "IFNULL(sum(dy_android_recharge_amount), 0.00) as dyAndroidRechargeAmount",
			"IFNULL(sum(dy_ios_recharge_amount), 0.00) as dyIosRechargeAmount", "IFNULL(sum(new_user_amount), 0.00) as newUserAmount", "IFNULL(sum(recharge_nums), 0.00) as rechargeNums", "IFNULL(sum(total_recharge_times), 0.00) as totalRechargeTimes",
			" IFNULL(sum(account_coin_consume), 0.00) as accountCoinConsume", "IFNULL(sum(new_user_recharge_nums), 0.00) as newUserRechargeNums", "ROUND(IFNULL(sum(account_coin_consume), 0.00) / IFNULL(sum(new_user_recharge_nums), 0.00), 2) as newUserPaymentCost",
			"ROUND(IFNULL(sum(total_amount), 0.00) / IFNULL(sum(recharge_nums), 0.00), 2) as customerPrice", "ROUND(IFNULL(sum(total_recharge_times), 0.00) / IFNULL(sum(recharge_nums), 0.00), 2) as avgRechargeTimes",
			"ROUND(IFNULL(sum(new_user_amount+new_user_ad_up), 0.00) / IFNULL(sum(account_coin_consume), 0.00), 2) as dayRoi", "ROUND(IFNULL(sum(total_amount+total_ad_up), 0.00) / IFNULL(sum(account_coin_consume), 0.00), 2) as totalRoi",
			"IFNULL(sum(ali_android_recharge_amount), 0.00) as aliAndroidRechargeAmount",
			"IFNULL(sum(ali_ios_recharge_amount), 0.00) as aliIosRechargeAmount",
			"IFNULL(sum(ali_android_recharge_amount+ali_ios_recharge_amount), 0.00) as aliRechargeAmount",
			"IFNULL(sum(stat_attribution_micro_game_24h_amount), 0.00) as statAttributionMicroGame24hAmount",
			"ROUND(IFNULL(sum(stat_attribution_micro_game_24h_amount), 0.00) / IFNULL(sum(account_coin_consume), 0.00), 2) as attributionMicroGame24hRoi",
			"IFNULL(sum(stat_micro_game_0d_amount), 0.00) as statMicroGame0dAmount",
			"IFNULL(sum(total_ad_up), 0.00) as totalAdUp,IFNULL(sum(new_user_ad_up), 0.00) as newUserAdUp"}
		record, err2 = channelSumMap.Fields(sumField).One()
		listRes.TotalAmount = totalAmount
		listRes.NewUserAmount = newUserTotalCount
		listRes.TotalAdUp = totalAdUp
		listRes.NewUserAdUp = newUserAdUp
		listRes.SummaryStat = new(model.SummaryStat)
		libUtils.RecordToEntity(record, listRes.SummaryStat)
		listRes.SummaryStat.WechatRechargeAmount = libUtils.AddAndRound(listRes.SummaryStat.WechatIosRechargeAmount, listRes.SummaryStat.WechatAndroidRechargeAmount, 2, libUtils.RoundDown)
	})

	return
}

func (s *sSChannelRechargeStatistics) List(ctx context.Context, req *model.SChannelRechargeStatisticsSearchReq) (listRes *model.SChannelRechargeStatisticsSearchRes, err error) {
	listRes = new(model.SChannelRechargeStatisticsSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.SChannelRechargeStatistics.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.SChannelRechargeStatistics.Columns().Id+" = ?", req.Id)
		}
		if req.CreateTime != "" {
			m = m.Where(dao.SChannelRechargeStatistics.Columns().CreateTime+" = ?", req.CreateTime)
		}
		if req.DistributorId != "" {
			m = m.Where(dao.SChannelRechargeStatistics.Columns().DistributorId+" = ?", gconv.Int(req.DistributorId))
		}
		if req.PitcherId != "" {
			m = m.Where(dao.SChannelRechargeStatistics.Columns().PitcherId+" = ?", gconv.Int(req.PitcherId))
		}
		if req.Account != "" {
			m = m.Where(dao.SChannelRechargeStatistics.Columns().Account+" = ?", req.Account)
		}
		if req.ChannelType != "" {
			m = m.Where(dao.SChannelRechargeStatistics.Columns().ChannelType+" = ?", gconv.Int(req.ChannelType))
		}
		if req.RechargeNums != "" {
			m = m.Where(dao.SChannelRechargeStatistics.Columns().RechargeNums+" = ?", gconv.Int(req.RechargeNums))
		}
		if req.TotalRechargeTimes != "" {
			m = m.Where(dao.SChannelRechargeStatistics.Columns().TotalRechargeTimes+" = ?", gconv.Int(req.TotalRechargeTimes))
		}
		if req.NewUserRechargeNums != "" {
			m = m.Where(dao.SChannelRechargeStatistics.Columns().NewUserRechargeNums+" = ?", gconv.Int(req.NewUserRechargeNums))
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.SChannelRechargeStatisticsListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.SChannelRechargeStatisticsListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.SChannelRechargeStatisticsListRes{
				Id:                          v.Id,
				CreateTime:                  v.CreateTime,
				DistributorId:               v.DistributorId,
				PitcherId:                   v.PitcherId,
				Account:                     v.Account,
				ChannelType:                 v.ChannelType,
				NewUserAmount:               v.NewUserAmount,
				TotalAmount:                 v.TotalAmount,
				RechargeNums:                v.RechargeNums,
				CustomerPrice:               v.CustomerPrice,
				AvgRechargeTimes:            v.AvgRechargeTimes,
				AccountCoinConsume:          v.AccountCoinConsume,
				DayRoi:                      v.DayRoi,
				TotalRoi:                    v.TotalRoi,
				TotalRechargeTimes:          v.TotalRechargeTimes,
				NewUserRechargeNums:         v.NewUserRechargeNums,
				WechatAndroidRechargeAmount: v.WechatAndroidRechargeAmount,
				WechatIosRechargeAmount:     v.WechatIosRechargeAmount,
				DyRechargeAmount:            v.DyRechargeAmount,
			}
		}
	})
	return
}

func (s *sSChannelRechargeStatistics) DistributorReconciliationStat(ctx context.Context, req *model.DistributorReconciliationStatReq) (listRes *model.DistributorReconciliationStatRes, err error) {
	listRes = new(model.DistributorReconciliationStatRes)
	listRes.SummaryStat, err = s.DistributorReconciliationStatGetSummary(ctx, req)
	err = g.Try(ctx, func(ctx context.Context) {
		// 构造根据分销 来查询用户数据的sql
		var userIds []int
		var admin bool
		if len(req.UserIds) > 0 {
			users, _ := sysService.SysUser().GetUserByIds(ctx, req.UserIds)
			admin = false
			for _, v := range users {
				ids, _, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
					LoginUserRes: &systemModel.LoginUserRes{
						Id:     v.Id,
						DeptId: v.DeptId,
					},
				})
				for _, k := range ids {
					userIds = append(userIds, k)
				}
			}
		} else {
			userInfo := sysService.Context().GetLoginUser(ctx)
			userIds, admin, _ = sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
				LoginUserRes: &systemModel.LoginUserRes{
					Id:     userInfo.Id,
					DeptId: userInfo.DeptId,
				},
			})
		}

		m := dao.SChannelRechargeStatistics.Ctx(ctx).WithAll().As("d").
			LeftJoin("sys_user", "u", "u.id = d.pitcher_id").
			LeftJoin("sys_dept", "de", "de.dept_id = u.dept_id")
		if !admin {
			m = m.WhereIn("d.pitcher_id", userIds)
		}
		if req.StartTime != "" {
			m = m.Where("d.create_time >= ?", req.StartTime)
		}
		if req.EndTime != "" {
			m = m.Where("d.create_time <= ?", req.EndTime)
		}
		if req.ChannelEnums > 0 {
			m = m.Where("d.ad_type = ?", commonConst.GetAdStrByAdEmu(req.ChannelEnums))
		}
		if req.Platform > 0 {
			m = m.Where("d.program_type = ?", commonConst.GetADTypeByChannelType(req.Platform))
		}
		//m = m.Where("de.leader is null and d.distributor_id > 1")
		var fields = []string{` any_value (de.leader) AS 'distributorName',
		any_value (de.dept_id) AS 'deptId',
		distributor_id as distributorId,
		SUM( CASE WHEN d.program_type = 'WX' THEN d.total_ad_up ELSE 0 END ) AS wechatAdUp,
		SUM( CASE WHEN d.program_type = 'DY' THEN d.total_ad_up ELSE 0 END ) AS dyAdUp,
		SUM( d.wechat_ios_recharge_amount ) AS wechatIosRecharge,
		SUM( d.wechat_android_recharge_amount ) AS wechatAndroidRecharge,
		SUM( d.dy_ios_recharge_amount ) AS dyIosRecharge,
		SUM( d.dy_android_recharge_amount ) AS dyAndroidRecharge,
		SUM( d.total_ad_up ) AS adUp`}

		var orderBy string
		var groupBy string
		if req.Merge == 1 {
			orderBy = "distributorName desc"
			groupBy = "d.distributor_id"
			if req.HaveAdType > 0 {
				groupBy += ", d.ad_type"
				fields = append(fields, "ad_type as adType")
			}
			if req.HaveProgramType > 0 {
				groupBy += ", d.program_type"
				fields = append(fields, "program_type as programType")
			}

			fields = append(fields, "min(create_time) as startTime")
			fields = append(fields, "max(create_time) as endTime")
		}
		if req.Merge == 0 {
			orderBy = "d.create_time desc "
			groupBy = "d.distributor_id"
			if req.HaveAdType > 0 {
				groupBy += ", d.ad_type"
				fields = append(fields, "ad_type as adType")
			}
			if req.HaveProgramType > 0 {
				groupBy += ", d.program_type"
				fields = append(fields, "program_type as programType")
			}
			groupBy += ", d.create_time"
			fields = append(fields, "d.create_time as createDate")
		}
		if req.OrderBy != "" && !libUtils.IsSQLInjection(req.OrderBy) && !libUtils.IsSQLInjection(req.OrderType) {
			orderBy = fmt.Sprintf("%s %s", req.OrderBy, req.OrderType)
		}
		var total = 0
		err = m.Fields(fields).
			Page(req.PageNum, req.PageSize).
			Group(groupBy).
			Order(orderBy).
			ScanAndCount(&listRes.List, &total, false)

		// 查询出所有的数据
		//var allList = make([]*model.DistributorReconciliationStatList, 0)
		//err = m.Fields(fields).
		//	Page(req.PageNum, req.PageSize).
		//	Group(groupBy).Scan(&allList)

		listRes.Total = total
		dIds := make([]int, len(listRes.List))
		for _, item := range listRes.List {
			dIds = append(dIds, item.DistributorId)
		}
		additions, _ := sysService.SysUser().GetUserAdditionals(ctx, dIds)
		for i, item := range listRes.List {
			if item.DistributorId == 0 || item.DistributorId == 1 || item.DistributorName == "" {
				item.DistributorName = "默认分销"
			}
			if req.Merge == 1 {
				listRes.List[i].CreateDate = item.StartTime + "-" + item.EndTime
			}
			// 计算 充值总金额 安卓充值金额  iOS充值金额
			listRes.List[i].RechargeAndroidAmount = libUtils.ToRound(item.WechatAndroidRecharge+item.DyAndroidRecharge, 2, libUtils.RoundHalfEven)
			listRes.List[i].RechargeIosAmount = libUtils.ToRound(item.WechatIosRecharge+item.DyIosRecharge, 2, libUtils.RoundHalfEven)
			listRes.List[i].RechargeTotalAmount = libUtils.ToRound(item.RechargeAndroidAmount+item.RechargeIosAmount, 2, libUtils.RoundHalfEven)
			// 获取当前用户的分成比例
			var additional = new(systemModel.SysUserAdditionalInfoRes)
			for _, uItem := range additions {
				if item.DistributorId == uItem.UserId {
					additional = uItem
				}
			}
			var shareRatio *system.ShareRatio
			if additional.Id > 0 {
				_ = jsoniter.Unmarshal([]byte(additional.ShareRatio), &shareRatio)
			}

			if shareRatio != nil {
				shareRatio.WxIaa = shareRatio.WxIaa / 100
				shareRatio.DyIaa = shareRatio.DyIaa / 100
				shareRatio.DyIapAndroid = shareRatio.DyIapAndroid / 100
				shareRatio.DyIapIos = shareRatio.DyIapIos / 100
				shareRatio.WxIapAndroid = shareRatio.WxIapAndroid / 100
				shareRatio.WxIapIos = shareRatio.WxIapIos / 100
				// 充值结算金额 微信安卓充值*微信安卓充值分成比例+微信IOS充值*微信IOS充值分成比例+抖音安卓充值*抖音安卓分成比例+抖音IOS充值*抖音IOS充值分成比例
				listRes.List[i].RechargeTotalSettlementAmount = libUtils.ToRound(item.WechatAndroidRecharge*shareRatio.WxIapAndroid+item.WechatIosRecharge*shareRatio.WxIapIos+item.DyAndroidRecharge*shareRatio.DyIapAndroid+item.DyIosRecharge*shareRatio.DyIapIos, 2, libUtils.RoundHalfEven)
				// 广告结算金额 微信广告收入微信广告分成比列+抖音广告收入/70%*抖音广告分成比例
				listRes.List[i].AdTotalSettlementAmount = libUtils.ToRound(item.WechatAdUp*shareRatio.WxIaa+item.DyAdUp/0.7*shareRatio.DyIaa, 2, libUtils.RoundHalfEven)
			} else {
				// 充值结算金额 微信安卓充值*微信安卓充值分成比例+微信IOS充值*微信IOS充值分成比例+抖音安卓充值*抖音安卓分成比例+抖音IOS充值*抖音IOS充值分成比例
				listRes.List[i].RechargeTotalSettlementAmount = libUtils.ToRound(item.WechatAndroidRecharge+item.WechatIosRecharge+item.DyAndroidRecharge+item.DyIosRecharge, 2, libUtils.RoundHalfEven)
				// 广告结算金额 微信广告收入微信广告分成比列+抖音广告收入/70%*抖音广告分成比例
				listRes.List[i].AdTotalSettlementAmount = libUtils.ToRound(item.WechatAdUp+item.DyAdUp/0.7, 2, libUtils.RoundHalfEven)
			}
			listRes.List[i].AdUp = libUtils.ToRound(item.AdUp, 2, libUtils.RoundHalfEven)
			listRes.List[i].DyAdUp = libUtils.ToRound(item.DyAdUp, 2, libUtils.RoundHalfEven)
			listRes.List[i].WechatAdUp = libUtils.ToRound(item.WechatAdUp, 2, libUtils.RoundHalfEven)
			//TotalIncome             float64 `json:"totalIncome"  dc:"总金额（充值+广告收入）"`
			listRes.List[i].TotalIncome = libUtils.ToRound(item.AdTotalSettlementAmount+item.RechargeTotalSettlementAmount, 2, libUtils.RoundHalfEven)
			listRes.List[i].TotalAmount = libUtils.ToRound(item.RechargeTotalAmount+item.AdUp, 2, libUtils.RoundHalfEven)

		}

		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sSChannelRechargeStatistics) DistributorReconciliationStatList(ctx context.Context, req *model.DistributorReconciliationStatReq) (retList []*model.DistributorReconciliationStatList, total int, err error) {
	retList = make([]*model.DistributorReconciliationStatList, 0)
	err = g.Try(ctx, func(ctx context.Context) {
		// 构造根据分销 来查询用户数据的sql
		var userIds []int
		var admin bool
		if len(req.UserIds) > 0 {
			users, _ := sysService.SysUser().GetUserByIds(ctx, req.UserIds)
			admin = false
			for _, v := range users {
				ids, _, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
					LoginUserRes: &systemModel.LoginUserRes{
						Id:     v.Id,
						DeptId: v.DeptId,
					},
				})
				for _, k := range ids {
					userIds = append(userIds, k)
				}
			}
		} else {
			userInfo := sysService.Context().GetLoginUser(ctx)
			userIds, admin, _ = sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
				LoginUserRes: &systemModel.LoginUserRes{
					Id:     userInfo.Id,
					DeptId: userInfo.DeptId,
				},
			})
		}

		m := dao.SChannelRechargeStatistics.Ctx(ctx).WithAll().As("d").
			LeftJoin("sys_user", "u", "u.id = d.pitcher_id").
			LeftJoin("sys_dept", "de", "de.dept_id = u.dept_id")
		if !admin {
			m = m.WhereIn("d.pitcher_id", userIds)
		}
		if req.StartTime != "" {
			m = m.Where("d.create_time >= ?", req.StartTime)
		}
		if req.EndTime != "" {
			m = m.Where("d.create_time <= ?", req.EndTime)
		}
		if req.ChannelEnums > 0 {
			m = m.Where("d.ad_type = ?", commonConst.GetAdStrByAdEmu(req.ChannelEnums))
		}
		if req.Platform > 0 {
			m = m.Where("d.program_type = ?", commonConst.GetADTypeByChannelType(req.Platform))
		}
		//m = m.Where("de.leader is null and d.distributor_id > 1")
		var fields = []string{` any_value (de.leader) AS 'distributorName',
		any_value (de.dept_id) AS 'deptId',
		distributor_id as distributorId,
		SUM( CASE WHEN d.program_type = 'WX' THEN d.total_ad_up ELSE 0 END ) AS wechatAdUp,
		SUM( CASE WHEN d.program_type = 'DY' THEN d.total_ad_up ELSE 0 END ) AS dyAdUp,
		SUM( d.wechat_ios_recharge_amount ) AS wechatIosRecharge,
		SUM( d.wechat_android_recharge_amount ) AS wechatAndroidRecharge,
		SUM( d.dy_ios_recharge_amount ) AS dyIosRecharge,
		SUM( d.dy_android_recharge_amount ) AS dyAndroidRecharge,
		SUM( d.total_ad_up ) AS adUp`}

		var orderBy string
		var groupBy string
		if req.Merge == 1 {
			orderBy = "distributorName desc"
			groupBy = "d.distributor_id"
			if req.HaveAdType > 0 {
				groupBy += ", d.ad_type"
				fields = append(fields, "ad_type as adType")
			}
			if req.HaveProgramType > 0 {
				groupBy += ", d.program_type"
				fields = append(fields, "program_type as programType")
			}

			fields = append(fields, "min(create_time) as startTime")
			fields = append(fields, "max(create_time) as endTime")
		}
		if req.Merge == 0 {
			orderBy = "d.create_time desc "
			groupBy = "d.distributor_id"
			if req.HaveAdType > 0 {
				groupBy += ", d.ad_type"
				fields = append(fields, "ad_type as adType")
			}
			if req.HaveProgramType > 0 {
				groupBy += ", d.program_type"
				fields = append(fields, "program_type as programType")
			}
			groupBy += ", d.create_time"
			fields = append(fields, "d.create_time as createDate")
		}
		if req.OrderBy != "" && !libUtils.IsSQLInjection(req.OrderBy) && !libUtils.IsSQLInjection(req.OrderType) {
			orderBy = fmt.Sprintf("%s %s", req.OrderBy, req.OrderType)
		}
		total = 0
		err = m.Fields(fields).
			Page(req.PageNum, req.PageSize).
			Group(groupBy).
			Order(orderBy).
			ScanAndCount(&retList, &total, false)

		dIds := make([]int, len(retList))
		for _, item := range retList {
			dIds = append(dIds, item.DistributorId)
		}
		additions, _ := sysService.SysUser().GetUserAdditionals(ctx, dIds)
		for i, item := range retList {
			if item.DistributorId == 0 || item.DistributorId == 1 || item.DistributorName == "" {
				item.DistributorName = "默认分销"
			}
			if req.Merge == 1 {
				retList[i].CreateDate = item.StartTime + "-" + item.EndTime
			}
			// 计算 充值总金额 安卓充值金额  iOS充值金额
			retList[i].RechargeAndroidAmount = libUtils.ToRound(item.WechatAndroidRecharge+item.DyAndroidRecharge, 2, libUtils.RoundHalfEven)
			retList[i].RechargeIosAmount = libUtils.ToRound(item.WechatIosRecharge+item.DyIosRecharge, 2, libUtils.RoundHalfEven)
			retList[i].RechargeTotalAmount = libUtils.ToRound(item.RechargeAndroidAmount+item.RechargeIosAmount, 2, libUtils.RoundHalfEven)
			// 获取当前用户的分成比例
			var additional = new(systemModel.SysUserAdditionalInfoRes)
			for _, uItem := range additions {
				if item.DistributorId == uItem.UserId {
					additional = uItem
				}
			}
			var shareRatio *system.ShareRatio
			if additional.Id > 0 {
				_ = jsoniter.Unmarshal([]byte(additional.ShareRatio), &shareRatio)
			}

			if shareRatio != nil {
				shareRatio.WxIaa = shareRatio.WxIaa / 100
				shareRatio.DyIaa = shareRatio.DyIaa / 100
				shareRatio.DyIapAndroid = shareRatio.DyIapAndroid / 100
				shareRatio.DyIapIos = shareRatio.DyIapIos / 100
				shareRatio.WxIapAndroid = shareRatio.WxIapAndroid / 100
				shareRatio.WxIapIos = shareRatio.WxIapIos / 100
				// 充值结算金额 微信安卓充值*微信安卓充值分成比例+微信IOS充值*微信IOS充值分成比例+抖音安卓充值*抖音安卓分成比例+抖音IOS充值*抖音IOS充值分成比例
				retList[i].RechargeTotalSettlementAmount = libUtils.ToRound(item.WechatAndroidRecharge*shareRatio.WxIapAndroid+item.WechatIosRecharge*shareRatio.WxIapIos+item.DyAndroidRecharge*shareRatio.DyIapAndroid+item.DyIosRecharge*shareRatio.DyIapIos, 2, libUtils.RoundHalfEven)
				// 广告结算金额 微信广告收入微信广告分成比列+抖音广告收入/70%*抖音广告分成比例
				retList[i].AdTotalSettlementAmount = libUtils.ToRound(item.WechatAdUp*shareRatio.WxIaa+item.DyAdUp/0.7*shareRatio.DyIaa, 2, libUtils.RoundHalfEven)
			} else {
				// 充值结算金额 微信安卓充值*微信安卓充值分成比例+微信IOS充值*微信IOS充值分成比例+抖音安卓充值*抖音安卓分成比例+抖音IOS充值*抖音IOS充值分成比例
				retList[i].RechargeTotalSettlementAmount = libUtils.ToRound(item.WechatAndroidRecharge+item.WechatIosRecharge+item.DyAndroidRecharge+item.DyIosRecharge, 2, libUtils.RoundHalfEven)
				// 广告结算金额 微信广告收入微信广告分成比列+抖音广告收入/70%*抖音广告分成比例
				retList[i].AdTotalSettlementAmount = libUtils.ToRound(item.WechatAdUp+item.DyAdUp/0.7, 2, libUtils.RoundHalfEven)
			}
			retList[i].AdUp = libUtils.ToRound(item.AdUp, 2, libUtils.RoundHalfEven)
			retList[i].DyAdUp = libUtils.ToRound(item.DyAdUp, 2, libUtils.RoundHalfEven)
			retList[i].WechatAdUp = libUtils.ToRound(item.WechatAdUp, 2, libUtils.RoundHalfEven)
			//TotalIncome             float64 `json:"totalIncome"  dc:"总金额（充值+广告收入）"`
			retList[i].TotalIncome = libUtils.ToRound(item.AdTotalSettlementAmount+item.RechargeTotalSettlementAmount, 2, libUtils.RoundHalfEven)
			retList[i].TotalAmount = libUtils.ToRound(item.RechargeTotalAmount+item.AdUp, 2, libUtils.RoundHalfEven)
		}

		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sSChannelRechargeStatistics) DistributorReconciliationStatGetSummary(ctx context.Context, req *model.DistributorReconciliationStatReq) (listRes *model.DistributorReconciliationSummaryStat, err error) {
	listRes = new(model.DistributorReconciliationSummaryStat)
	err = g.Try(ctx, func(ctx context.Context) {
		// 构造根据分销 来查询用户数据的sql
		var userIds []int
		var admin bool
		if len(req.UserIds) > 0 {
			users, _ := sysService.SysUser().GetUserByIds(ctx, req.UserIds)
			admin = false
			for _, v := range users {
				ids, _, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
					LoginUserRes: &systemModel.LoginUserRes{
						Id:     v.Id,
						DeptId: v.DeptId,
					},
				})
				for _, k := range ids {
					userIds = append(userIds, k)
				}
			}
		} else {
			userInfo := sysService.Context().GetLoginUser(ctx)
			userIds, admin, _ = sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
				LoginUserRes: &systemModel.LoginUserRes{
					Id:     userInfo.Id,
					DeptId: userInfo.DeptId,
				},
			})
		}

		m := dao.SChannelRechargeStatistics.Ctx(ctx).WithAll().As("d").
			LeftJoin("sys_user", "u", "u.id = d.pitcher_id").
			LeftJoin("sys_dept", "de", "de.dept_id = u.dept_id")
		if !admin {
			m = m.WhereIn("d.pitcher_id", userIds)
		}
		if req.StartTime != "" {
			m = m.Where("d.create_time >= ?", req.StartTime)
		}
		if req.EndTime != "" {
			m = m.Where("d.create_time <= ?", req.EndTime)
		}
		if req.ChannelEnums > 0 {
			m = m.Where("d.ad_type = ?", commonConst.GetAdStrByAdEmu(req.ChannelEnums))
		}
		if req.Platform > 0 {
			m = m.Where("d.program_type = ?", commonConst.GetADTypeByChannelType(req.Platform))
		}
		//m = m.Where("de.leader is null and d.distributor_id > 1")
		var fields = []string{` any_value (de.leader) AS 'distributorName',
		any_value (de.dept_id) AS 'deptId',
		distributor_id as distributorId,
		SUM( CASE WHEN d.program_type = 'WX' THEN d.total_ad_up ELSE 0 END ) AS wechatAdUp,
		SUM( CASE WHEN d.program_type = 'DY' THEN d.total_ad_up ELSE 0 END ) AS dyAdUp,
		SUM( d.wechat_ios_recharge_amount ) AS wechatIosRecharge,
		SUM( d.wechat_android_recharge_amount ) AS wechatAndroidRecharge,
		SUM( d.dy_ios_recharge_amount ) AS dyIosRecharge,
		SUM( d.dy_android_recharge_amount ) AS dyAndroidRecharge,
		SUM( d.total_ad_up ) AS adUp`}

		var groupBy string
		if req.Merge == 1 {
			groupBy = "d.distributor_id"
			if req.HaveAdType > 0 {
				groupBy += ", d.ad_type"
				fields = append(fields, "ad_type as adType")
			}
			if req.HaveProgramType > 0 {
				groupBy += ", d.program_type"
				fields = append(fields, "program_type as programType")
			}

			fields = append(fields, "min(create_time) as startTime")
			fields = append(fields, "max(create_time) as endTime")
		}
		if req.Merge == 0 {
			groupBy = "d.distributor_id"
			if req.HaveAdType > 0 {
				groupBy += ", d.ad_type"
				fields = append(fields, "ad_type as adType")
			}
			if req.HaveProgramType > 0 {
				groupBy += ", d.program_type"
				fields = append(fields, "program_type as programType")
			}
			groupBy += ", d.create_time"
			fields = append(fields, "d.create_time as createDate")
		}
		// 查询出所有的数据
		var allList = make([]*model.DistributorReconciliationStatList, 0)
		err = m.Fields(fields).
			//Page(req.PageNum, req.PageSize).
			Group(groupBy).Scan(&allList)

		dIds := make([]int, len(allList))
		for _, item := range allList {
			dIds = append(dIds, item.DistributorId)
		}
		additions, _ := sysService.SysUser().GetUserAdditionals(ctx, dIds)

		for _, item := range allList {
			listRes.RechargeAndroidAmount += item.WechatAndroidRecharge + item.DyAndroidRecharge
			listRes.RechargeIosAmount += item.WechatIosRecharge + item.DyIosRecharge
			listRes.AdUp += item.AdUp
			// 获取当前用户的分成比例
			var additional = new(systemModel.SysUserAdditionalInfoRes)
			for _, uItem := range additions {
				if item.DistributorId == uItem.UserId {
					additional = uItem
				}
			}
			var shareRatio *system.ShareRatio
			if additional.Id > 0 {
				_ = jsoniter.Unmarshal([]byte(additional.ShareRatio), &shareRatio)
			}

			if shareRatio != nil {
				shareRatio.WxIaa = shareRatio.WxIaa / 100
				shareRatio.DyIaa = shareRatio.DyIaa / 100
				shareRatio.DyIapAndroid = shareRatio.DyIapAndroid / 100
				shareRatio.DyIapIos = shareRatio.DyIapIos / 100
				shareRatio.WxIapAndroid = shareRatio.WxIapAndroid / 100
				shareRatio.WxIapIos = shareRatio.WxIapIos / 100
				// 充值结算金额 微信安卓充值*微信安卓充值分成比例+微信IOS充值*微信IOS充值分成比例+抖音安卓充值*抖音安卓分成比例+抖音IOS充值*抖音IOS充值分成比例
				listRes.RechargeTotalSettlementAmount += item.WechatAndroidRecharge*shareRatio.WxIapAndroid + item.WechatIosRecharge*shareRatio.WxIapIos + item.DyAndroidRecharge*shareRatio.DyIapAndroid + item.DyIosRecharge*shareRatio.DyIapIos
				// 广告结算金额 微信广告收入微信广告分成比列+抖音广告收入/70%*抖音广告分成比例
				listRes.AdTotalSettlementAmount += item.WechatAdUp*shareRatio.WxIaa + item.DyAdUp/0.7*shareRatio.DyIaa
			} else {
				// 充值结算金额 微信安卓充值*微信安卓充值分成比例+微信IOS充值*微信IOS充值分成比例+抖音安卓充值*抖音安卓分成比例+抖音IOS充值*抖音IOS充值分成比例
				listRes.RechargeTotalSettlementAmount += item.WechatAndroidRecharge + item.WechatIosRecharge + item.DyAndroidRecharge + item.DyIosRecharge
				// 广告结算金额 微信广告收入微信广告分成比列+抖音广告收入/70%*抖音广告分成比例
				listRes.AdTotalSettlementAmount += item.WechatAdUp + item.DyAdUp/0.7
			}
		}
		listRes.RechargeTotalAmount = listRes.RechargeAndroidAmount + listRes.RechargeIosAmount
		listRes.TotalAmount = listRes.AdUp + listRes.RechargeTotalAmount
		//TotalIncome             float64 `json:"totalIncome"  dc:"总金额（充值+广告收入）"`
		listRes.AdUp = libUtils.ToRound(listRes.AdUp, 2, libUtils.RoundHalfEven)
		listRes.TotalIncome = listRes.AdTotalSettlementAmount + listRes.RechargeTotalSettlementAmount
		listRes.RechargeAndroidAmount = libUtils.ToRound(listRes.RechargeAndroidAmount, 2, libUtils.RoundHalfEven)
		listRes.RechargeIosAmount = libUtils.ToRound(listRes.RechargeIosAmount, 2, libUtils.RoundHalfEven)
		listRes.RechargeTotalAmount = libUtils.ToRound(listRes.RechargeTotalAmount, 2, libUtils.RoundHalfEven)
		listRes.RechargeTotalSettlementAmount = libUtils.ToRound(listRes.RechargeTotalSettlementAmount, 2, libUtils.RoundHalfEven)
		listRes.AdTotalSettlementAmount = libUtils.ToRound(listRes.AdTotalSettlementAmount, 2, libUtils.RoundHalfEven)
		listRes.TotalIncome = libUtils.ToRound(listRes.TotalIncome, 2, libUtils.RoundHalfEven)
		listRes.TotalAmount = libUtils.ToRound(listRes.TotalAmount, 2, libUtils.RoundHalfEven)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sSChannelRechargeStatistics) GetExportData(ctx context.Context, req *model.SChannelRechargeStatisticsSearchReq) (listRes []*model.SChannelRechargeStatisticsInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.SChannelRechargeStatistics.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.SChannelRechargeStatistics.Columns().Id+" = ?", req.Id)
		}
		if req.CreateTime != "" {
			m = m.Where(dao.SChannelRechargeStatistics.Columns().CreateTime+" = ?", req.CreateTime)
		}
		if req.DistributorId != "" {
			m = m.Where(dao.SChannelRechargeStatistics.Columns().DistributorId+" = ?", gconv.Int(req.DistributorId))
		}
		if req.PitcherId != "" {
			m = m.Where(dao.SChannelRechargeStatistics.Columns().PitcherId+" = ?", gconv.Int(req.PitcherId))
		}
		if req.Account != "" {
			m = m.Where(dao.SChannelRechargeStatistics.Columns().Account+" = ?", req.Account)
		}
		if req.ChannelType != "" {
			m = m.Where(dao.SChannelRechargeStatistics.Columns().ChannelType+" = ?", gconv.Int(req.ChannelType))
		}
		if req.RechargeNums != "" {
			m = m.Where(dao.SChannelRechargeStatistics.Columns().RechargeNums+" = ?", gconv.Int(req.RechargeNums))
		}
		if req.TotalRechargeTimes != "" {
			m = m.Where(dao.SChannelRechargeStatistics.Columns().TotalRechargeTimes+" = ?", gconv.Int(req.TotalRechargeTimes))
		}
		if req.NewUserRechargeNums != "" {
			m = m.Where(dao.SChannelRechargeStatistics.Columns().NewUserRechargeNums+" = ?", gconv.Int(req.NewUserRechargeNums))
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sSChannelRechargeStatistics) GetById(ctx context.Context, id int64) (res *model.SChannelRechargeStatisticsInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.SChannelRechargeStatistics.Ctx(ctx).WithAll().Where(dao.SChannelRechargeStatistics.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sSChannelRechargeStatistics) GetByAccountAndCreateDate(ctx context.Context, account string, createDate string) (res *model.SChannelRechargeStatisticsInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.SChannelRechargeStatistics.Ctx(ctx).WithAll().
			Where(dao.SChannelRechargeStatistics.Columns().Account, account).
			Where(dao.SChannelRechargeStatistics.Columns().CreateTime, createDate).
			Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}
func (s *sSChannelRechargeStatistics) GetByAccountAndDate(ctx context.Context, account string, createDate string) (res *channelModel.SChannelRechargeStatistics, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.SChannelRechargeStatistics.Ctx(ctx).WithAll().
			Where(dao.SChannelRechargeStatistics.Columns().Account, account).
			Where(dao.SChannelRechargeStatistics.Columns().CreateTime, createDate).
			Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sSChannelRechargeStatistics) GetByDate(ctx context.Context, createDate string) (res []*channelModel.SChannelRechargeStatistics, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res = make([]*channelModel.SChannelRechargeStatistics, 0)
		err = dao.SChannelRechargeStatistics.Ctx(ctx).WithAll().
			Where(dao.SChannelRechargeStatistics.Columns().CreateTime, createDate).
			Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sSChannelRechargeStatistics) GetByAccountAndDateRange(ctx context.Context, accountList []string, startDate string, endDate string) (res []*channelModel.SChannelRechargeStatistics, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res = make([]*channelModel.SChannelRechargeStatistics, 0)
		if accountList == nil || len(accountList) == 0 {
			return
		}
		err = dao.SChannelRechargeStatisticsAnalytic.Ctx(ctx).WithAll().
			WhereIn(dao.SChannelRechargeStatistics.Columns().Account, accountList).
			WhereGTE(dao.SChannelRechargeStatistics.Columns().CreateTime, startDate).
			WhereLTE(dao.SChannelRechargeStatistics.Columns().CreateTime, endDate).
			Fields("account as account").
			Fields("create_time as createTime").
			FieldSum("active_count", "activeCount").
			Group("account, create_time").
			Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sSChannelRechargeStatistics) GetByAccounts(ctx context.Context, accountList []string, statDate string) (res []channelModel.SChannelRechargeStatistics, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res = make([]channelModel.SChannelRechargeStatistics, 0)
		if accountList == nil || len(accountList) == 0 {
			return
		}
		err = dao.SChannelRechargeStatistics.Ctx(ctx).WithAll().
			WhereIn(dao.SChannelRechargeStatistics.Columns().Account, accountList).
			Where(dao.SChannelRechargeStatistics.Columns().CreateTime, statDate).
			Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sSChannelRechargeStatistics) Add(ctx context.Context, req *model.SChannelRechargeStatisticsAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.SChannelRechargeStatistics.Ctx(ctx).Insert(do.SChannelRechargeStatistics{
			CreateTime:                  req.CreateTime,
			DistributorId:               req.DistributorId,
			PitcherId:                   req.PitcherId,
			Account:                     req.Account,
			ChannelType:                 req.ChannelType,
			NewUserAmount:               req.NewUserAmount,
			TotalAmount:                 req.TotalAmount,
			RechargeNums:                req.RechargeNums,
			CustomerPrice:               req.CustomerPrice,
			AvgRechargeTimes:            req.AvgRechargeTimes,
			AccountCoinConsume:          req.AccountCoinConsume,
			DayRoi:                      req.DayRoi,
			TotalRoi:                    req.TotalRoi,
			TotalRechargeTimes:          req.TotalRechargeTimes,
			NewUserRechargeNums:         req.NewUserRechargeNums,
			WechatAndroidRechargeAmount: req.WechatAndroidRechargeAmount,
			WechatIosRechargeAmount:     req.WechatIosRechargeAmount,
			DyRechargeAmount:            req.DyRechargeAmount,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sSChannelRechargeStatistics) Edit(ctx context.Context, req *model.SChannelRechargeStatisticsEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.SChannelRechargeStatistics.Ctx(ctx).WherePri(req.Id).Update(do.SChannelRechargeStatistics{
			CreateTime:                  req.CreateTime,
			DistributorId:               req.DistributorId,
			PitcherId:                   req.PitcherId,
			Account:                     req.Account,
			ChannelType:                 req.ChannelType,
			NewUserAmount:               req.NewUserAmount,
			TotalAmount:                 req.TotalAmount,
			RechargeNums:                req.RechargeNums,
			CustomerPrice:               req.CustomerPrice,
			AvgRechargeTimes:            req.AvgRechargeTimes,
			AccountCoinConsume:          req.AccountCoinConsume,
			DayRoi:                      req.DayRoi,
			TotalRoi:                    req.TotalRoi,
			TotalRechargeTimes:          req.TotalRechargeTimes,
			NewUserRechargeNums:         req.NewUserRechargeNums,
			WechatAndroidRechargeAmount: req.WechatAndroidRechargeAmount,
			WechatIosRechargeAmount:     req.WechatIosRechargeAmount,
			DyRechargeAmount:            req.DyRechargeAmount,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sSChannelRechargeStatistics) Delete(ctx context.Context, ids []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.SChannelRechargeStatistics.Ctx(ctx).Delete(dao.SChannelRechargeStatistics.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

// BuildSqlInStr 将list字符串转换成sql in 参数
func BuildSqlInStr(list []string) string {
	var strIn string
	if list != nil {
		for k, v := range list {
			strIn = strIn + "'" + v + "'"
			if k < len(list)-1 {
				strIn = strIn + ","
			}
		}
	}
	return strIn
}
func buildTheaterRechargeSql(ctx context.Context, req *model.TheaterPitcherRechargeReq) *gdb.Model {
	m := dao.SChannelRechargeStatistics.Ctx(ctx).As("s").LeftJoin("sys_user su", "pitcher_id =su.id").LeftJoin("sys_user su1", "s.distributor_id =su1.id")
	if len(req.PitcherIds) > 0 {
		m = m.Where("s.pitcher_id in (?)", req.PitcherIds)

	}
	if len(req.DistributorIds) > 0 {
		m = m.Where("s.distributor_id in (?)", req.DistributorIds)
	}
	if req.StartTime != "" {
		m = m.Where("s.create_time>=?", req.StartTime)
	}
	if req.EndTime != "" {
		m = m.Where("s.create_time<=?", req.EndTime)
	}
	var order string
	if req.OrderBy != "" && !libUtils.IsSQLInjection(req.OrderBy) && !libUtils.IsSQLInjection(req.OrderType) {
		if req.OrderBy == "totalRoi" {
			order = "IFNULL(sum(total_amount) /sum(account_coin_consume),0)" + " " + req.OrderType
		} else if req.OrderBy == "dayRoi" {
			order = "IFNULL(sum(new_user_amount)/sum(account_coin_consume),0)" + " " + req.OrderType
		} else if req.OrderBy == "customerPrice" {
			order = "IFNULL(sum(total_amount)/sum(recharge_nums),0)" + " " + req.OrderType
		} else {
			order = req.OrderBy + " " + req.OrderType
		}

	}
	m = m.Where("parent_id=?", req.ParentId)
	//查询出权限来
	ids, isAdmin, _ := sysService.SysUser().GetContainUser(ctx, sysService.Context().GetLoginUser(ctx))
	if !isAdmin {
		//非admin查询当前用户下的数据
		if len(ids) > 0 {
			m = m.WhereIn("s.pitcher_id", ids)
		}
	}
	groupBy := "pitcher_id,create_time"

	m = m.Order(order)
	m = m.Group(groupBy)
	return m
}

func (s *sSChannelRechargeStatistics) OceanCallbackValid(ctx context.Context, req *model.OceanCallbackValidReq) (res *model.OceanCallbackValidRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		challenge := ghttp.RequestFromCtx(ctx).GetQuery("challenge").Int64()
		g.Log().Infof(ctx, "OceanCallbackValid: %v", challenge)
		res = &model.OceanCallbackValidRes{
			BaseResp: &model.BaseResp{
				StatusCode:    200,
				StatusMessage: "ok",
			},
			Challenge: challenge,
		}
	})
	return
}

func (s *sSChannelRechargeStatistics) OceanCallback(ctx context.Context) (res *model.OceanCallbackValidRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		request := ghttp.RequestFromCtx(ctx)
		body := request.GetBody()
		g.Log().Infof(ctx, "巨量回调body: %s", string(body))
		secretKey := g.Cfg().MustGet(ctx, "advertiser.toutiao.secretKey").String()
		util := libUtils.AuthTokenUtil{SecretKey: secretKey}
		isValidToken := util.IsValidToken(ctx, body, []byte(request.GetHeader("X-Open-Signature")))
		res = &model.OceanCallbackValidRes{
			BaseResp: &model.BaseResp{
				StatusCode:    400,
				StatusMessage: "fail",
			},
		}
		// 验签失败
		if !isValidToken {
			err = fmt.Errorf("invalid sinature")
			g.Log().Warningf(ctx, "验签名失败, err: %v", err)
			return
		}
		// 验签通过, 数据处理流程
		res = &model.OceanCallbackValidRes{
			BaseResp: &model.BaseResp{
				StatusCode:    200,
				StatusMessage: "ok",
			},
		}
		var oceanCallbackData *model.OceanCallbackData
		err1 := json.Unmarshal(body, &oceanCallbackData)
		if err1 != nil {
			g.Log().Errorf(ctx, "Unmarshal oceanCallbackData err: %v", err1)
			return
		}
		if len(oceanCallbackData.AdvertiserIds) == 0 {
			return
		}
		var createDate = gtime.NewFromTimeStamp(oceanCallbackData.PublishTime).Format("Y-m-d")
		var createTime = gtime.NewFromTimeStamp(oceanCallbackData.PublishTime)
		var advertiserId = gconv.String(oceanCallbackData.AdvertiserIds[0])
		adSetting, err2 := service.AdSetting().GetByAdvertiserId(ctx, advertiserId)
		if err2 != nil || adSetting == nil {
			return
		}
		record := &model.SChannelAdvertiserRecordInfoRes{
			CreateDate:   createDate,
			Account:      adSetting.SubChannel,
			AdvertiserId: advertiserId,
			ChannelType:  gconv.Int(adSetting.ChannelFrom),
			CreateTime:   createTime,
		}
		existRecord, err3 := service.SChannelAdvertiserRecord().GetByAdvertiserIdAndDate(ctx, advertiserId, createDate)
		if err3 != nil {
			g.Log().Errorf(ctx, "GetByAdvertiserIdAndDate err: %v", err3)
			return
		}
		if existRecord != nil {
			record.Id = existRecord.Id
		}
		err4 := service.SChannelAdvertiserRecord().Save(ctx, record)
		if err4 != nil {
			g.Log().Errorf(ctx, "Save Advertiser Record err: %v", err4)
			return
		}
		// 查询消耗
		_ = s.pool.Add(context.Background(), func(ctx context.Context) {
			err5 := service.SChannelHourRechargeStatistics().UpdateChannelHourConsume(ctx, adSetting, createDate, createTime.Hour())
			if err5 != nil {
				g.Log().Errorf(ctx, "UpdateChannelHourConsume err: %v", err5)
				return
			}
			cost, statAttributionMicroGame24hAmount, statMicroGame0dAmount, _ := orderService.OrderTask().CalcStatCostBefore(ctx, createDate, adSetting.AdvertiserIds, gconv.Int(adSetting.ChannelFrom))
			err6 := s.UpdateChannelConsume(ctx, adSetting, createDate, cost, statAttributionMicroGame24hAmount, statMicroGame0dAmount)
			if err6 != nil {
				g.Log().Errorf(ctx, "UpdateChannelConsume err: %v", err6)
				return
			}
			err7 := service.MMemberAdCallbackStatistics().UpdateAdCallbackStatConsume(ctx, adSetting, createDate, cost)
			if err7 != nil {
				g.Log().Errorf(ctx, "UpdateAdCallbackStatConsume err: %v", err7)
				return
			}
		})
	})
	return
}

// UpdateChannelConsume 更新渠道消耗
func (s *sSChannelRechargeStatistics) UpdateChannelConsume(
	ctx context.Context, adSetting *model.AdSettingListRes,
	createDate string, cost float64,
	statAttributionMicroGame24hAmount float64, statMicroGame0dAmount float64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var res *model.SChannelRechargeStatisticsInfoRes
		err = dao.SChannelRechargeStatistics.Ctx(ctx).
			Where(dao.SChannelRechargeStatistics.Columns().Account, adSetting.SubChannel).
			Where(dao.SChannelRechargeStatistics.Columns().CreateTime, createDate).
			Scan(&res)
		if err != nil {
			g.Log().Errorf(ctx, "获取渠道消耗数据异常：%v", err)
			return
		}
		if res == nil {
			res = &model.SChannelRechargeStatisticsInfoRes{
				CreateTime:                        createDate,
				Account:                           adSetting.SubChannel,
				ChannelType:                       gconv.Int(adSetting.ChannelFrom),
				AccountCoinConsume:                cost,
				StatAttributionMicroGame24hAmount: statAttributionMicroGame24hAmount,
				StatMicroGame0dAmount:             statMicroGame0dAmount,
			}
			channelByCode, err1 := service.SChannel().GetChannelByCode(ctx, adSetting.SubChannel)
			if err1 != nil {
				g.Log().Errorf(ctx, "获取渠道所属分销和投手异常：%v", err1)
				return
			}
			if channelByCode != nil {
				res.DistributorId = channelByCode.DistributorId
				res.PitcherId = channelByCode.UserId
			}
		} else {
			res.AccountCoinConsume = cost
			res.StatAttributionMicroGame24hAmount = statAttributionMicroGame24hAmount
			res.StatMicroGame0dAmount = statMicroGame0dAmount
		}
		_, err2 := dao.SChannelRechargeStatistics.Ctx(ctx).Save(res)
		liberr.ErrIsNil(ctx, err2)
	})
	return
}

func (s *sSChannelRechargeStatistics) UpdateChannelAdvertiserRecord(ctx context.Context, statDate string, channel string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		innerContext, cancel := context.WithTimeout(context.Background(), 30*time.Minute)
		defer cancel()
		var pageNo = 1
		var pageSize = 100
		for {
			var records = make([]*model.SChannelRechargeStatisticsInfoRes, 0)
			m := dao.SChannelRechargeStatisticsAnalytic.Ctx(innerContext).Where("create_time = ?", statDate)
			if channel != "" {
				m = m.Where(dao.SChannelRechargeStatistics.Columns().Account, channel)
			}
			err = m.Page(pageNo, pageSize).
				OrderAsc(dao.SChannelRechargeStatistics.Columns().Id).
				Scan(&records)
			if err != nil {
				g.Log().Errorf(innerContext, "获取渠道充值统计异常：%v", err)
				return
			}
			if len(records) == 0 {
				break
			}
			channelList := make([]string, 0)
			for _, v := range records {
				channelList = append(channelList, v.Account)
			}
			adSettingList, err1 := service.AdSetting().GetByAccountList(innerContext, channelList)
			if err1 != nil {
				g.Log().Errorf(innerContext, "获取广告回传配置异常：%v", err1)
				return
			}
			advertiserRecords := make([]*model.SChannelAdvertiserRecordInfoRes, 0)
			for _, adSetting := range adSettingList {
				if adSetting.AdvertiserIds == "" {
					continue
				}
				advertiserIds := strings.Split(adSetting.AdvertiserIds, ",")
				for _, advertiserId := range advertiserIds {
					advertiserRecord := &model.SChannelAdvertiserRecordInfoRes{
						CreateDate:   statDate,
						Account:      adSetting.SubChannel,
						AdvertiserId: advertiserId,
						ChannelType:  gconv.Int(adSetting.ChannelFrom),
						CreateTime:   gtime.Now(),
					}
					advertiserRecords = append(advertiserRecords, advertiserRecord)
				}
			}
			if len(advertiserRecords) > 0 {
				err2 := service.SChannelAdvertiserRecord().BatchSave(innerContext, advertiserRecords)
				if err2 != nil {
					g.Log().Errorf(innerContext, "保存广告回传记录异常：%v", err2)
					return
				}
			}
			pageNo++
		}
	})
	return
}

// CalcFqAccountCoinConsumeStat 获取番茄渠道账户币消耗
func (s *sSChannelRechargeStatistics) CalcFqAccountCoinConsumeStat(ctx context.Context, statTime string) (res []*theaterModel.SPitcherVideoRechargeStatisticsInfoRes) {
	m := dao.SChannelRechargeStatisticsAnalytic.Ctx(ctx).WithAll().As("sc").
		LeftJoin("s_channel c", "sc.account = c.channel_code").
		LeftJoin("ad_diversion_link ad", "ad.account = c.channel_code").
		Where("sc.create_time = ?", statTime).
		WhereNot("c.fq_promotion_id", "")
	err := m.Fields("c.fq_promotion_id as fqPromotionId").
		Fields("ad.fq_book_id as videoId").
		FieldSum("sc.account_coin_consume", "accountCoinConsume").
		Group("c.fq_promotion_id, ad.fq_book_id").
		Scan(&res)
	liberr.ErrIsNil(ctx, err, "获取账户币消耗失败")
	return
}

// CalcDzAccountCoinConsumeStat 获取点众渠道账户币消耗
func (s *sSChannelRechargeStatistics) CalcDzAccountCoinConsumeStat(ctx context.Context, statTime string) (res []*theaterModel.SPitcherVideoRechargeStatisticsInfoRes) {
	m := dao.SChannelRechargeStatisticsAnalytic.Ctx(ctx).WithAll().As("sc").
		LeftJoin("s_channel c", "sc.account = c.channel_code").
		LeftJoin("ad_diversion_link ad", "ad.account = c.channel_code").
		Where("sc.create_time = ?", statTime).
		WhereNot("c.dz_referral_id", "")
	err := m.Fields("c.dz_referral_id as dzReferralId").
		Fields("ad.dz_book_id as videoId").
		FieldSum("sc.account_coin_consume", "accountCoinConsume").
		Group("c.dz_referral_id, ad.dz_book_id").
		Scan(&res)
	liberr.ErrIsNil(ctx, err, "获取账户币消耗失败")
	return
}
