// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2024-11-16 14:11:26
// 生成路径: internal/app/oceanengine/model/ad_advertiser_account_metrics_data.go
// 生成人：cyao
// desc:广告账户的指标数据
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdAdvertiserAccountMetricsDataInfoRes is the golang structure for table ad_advertiser_account_metrics_data.
type AdAdvertiserAccountMetricsDataInfoRes struct {
	gmeta.Meta                 `orm:"table:ad_advertiser_account_metrics_data"`
	Id                         int     `orm:"id,primary" json:"id" dc:"自增id主键"`                  // 自增id主键
	AdvertiserId               string  `orm:"advertiser_id" json:"advertiserId" dc:"账户ID"`       // 账户ID
	StatCost                   float64 `orm:"stat_cost" json:"statCost" dc:"消耗"`                 // 消耗
	ShowCnt                    int64   `orm:"show_cnt" json:"showCnt" dc:"展示数"`                  // 展示数
	CpmPlatform                float64 `orm:"cpm_platform" json:"cpmPlatform" dc:"平均千次展现费用(元)"`  // 平均千次展现费用(元)
	ClickCnt                   int64   `orm:"click_cnt" json:"clickCnt" dc:"点击数"`                // 点击数
	Ctr                        float64 `orm:"ctr" json:"ctr" dc:"点击率"`                           // 点击率
	CpcPlatform                float64 `orm:"cpc_platform" json:"cpcPlatform" dc:"平均点击单价(元)"`    // 平均点击单价(元)
	ConvertCnt                 int64   `orm:"convert_cnt" json:"convertCnt" dc:"转化数"`            // 转化数
	ConversionCost             float64 `orm:"conversion_cost" json:"conversionCost" dc:"平均转化成本"` // 平均转化成本
	StatPayAmount              float64 `orm:"stat_pay_amount" json:"statPayAmount,omitempty"`
	ConversionRate             float64 `orm:"conversion_rate" json:"conversionRate" dc:"转化率"`                                    // 转化率
	DeepConvertCnt             int64   `orm:"deep_convert_cnt" json:"deepConvertCnt" dc:"深度转化数"`                                 // 深度转化数
	DeepConvertCost            float64 `orm:"deep_convert_cost" json:"deepConvertCost" dc:"深度转化成本"`                              // 深度转化成本
	DeepConvertRate            float64 `orm:"deep_convert_rate" json:"deepConvertRate" dc:"深度转化率"`                               // 深度转化率
	AttributionConvertCnt      int64   `orm:"attribution_convert_cnt" json:"attributionConvertCnt" dc:"转化数(计费时间)"`               // 转化数(计费时间)
	AttributionConvertCost     float64 `orm:"attribution_convert_cost" json:"attributionConvertCost" dc:"转化成本(计费时间)"`            // 转化成本(计费时间)
	AttributionDeepConvertCnt  int64   `orm:"attribution_deep_convert_cnt" json:"attributionDeepConvertCnt" dc:"深度转化数(计费时间)"`    // 深度转化数(计费时间)
	AttributionDeepConvertCost float64 `orm:"attribution_deep_convert_cost" json:"attributionDeepConvertCost" dc:"深度转化成本(计费时间)"` // 深度转化成本(计费时间)
	PreConvertCount            int64   `orm:"pre_convert_count" json:"preConvertCount" dc:"预估转化数(计费时间)"`                         // 预估转化数(计费时间)
	PreConvertCost             float64 `orm:"pre_convert_cost" json:"preConvertCost" dc:"预估转化成本(计费时间)"`                          // 预估转化成本(计费时间)
	PreConvertRate             float64 `orm:"pre_convert_rate" json:"preConvertRate" dc:"预估转化率(计费时间)"`                           // 预估转化率(计费时间)
	ClickStartCnt              int64   `orm:"click_start_cnt" json:"clickStartCnt" dc:"安卓下载开始数"`                                 // 安卓下载开始数
	ClickStartCost             float64 `orm:"click_start_cost" json:"clickStartCost" dc:"安卓下载开始成本"`                              // 安卓下载开始成本
	ClickStartRate             float64 `orm:"click_start_rate" json:"clickStartRate" dc:"安卓下载开始率"`                               // 安卓下载开始率
	DownloadFinishCnt          int64   `orm:"download_finish_cnt" json:"downloadFinishCnt" dc:"安卓下载完成数"`                         // 安卓下载完成数
	DownloadFinishCost         float64 `orm:"download_finish_cost" json:"downloadFinishCost" dc:"安卓下载完成成本"`                      // 安卓下载完成成本
	DownloadFinishRate         float64 `orm:"download_finish_rate" json:"downloadFinishRate" dc:"安卓下载完成率"`                       // 安卓下载完成率
	InstallFinishCnt           int64   `orm:"install_finish_cnt" json:"installFinishCnt" dc:"安卓安装完成数"`                           // 安卓安装完成数
	InstallFinishCost          float64 `orm:"install_finish_cost" json:"installFinishCost" dc:"安卓安装完成成本"`                        // 安卓安装完成成本
	InstallFinishRate          float64 `orm:"install_finish_rate" json:"installFinishRate" dc:"安卓安装完成率"`                         // 安卓安装完成率
	Active                     int64   `orm:"active" json:"active" dc:"激活数"`                                                     // 激活数
	ActiveCost                 float64 `orm:"active_cost" json:"activeCost" dc:"激活成本"`                                           // 激活成本
	ActiveRate                 float64 `orm:"active_rate" json:"activeRate" dc:"激活率"`
	Register                   int64   `orm:"register" json:"register" dc:"注册数"`
	// 激活率
	ActiveRegisterCost                    float64 `orm:"active_register_cost" json:"activeRegisterCost" dc:"注册成本"`                                                 // 注册成本
	ActiveRegisterRate                    float64 `orm:"active_register_rate" json:"activeRegisterRate" dc:"注册率"`                                                  // 注册率
	GameAddiction                         int64   `orm:"game_addiction" json:"gameAddiction" dc:"关键行为数"`                                                           // 关键行为数
	GameAddictionCost                     float64 `orm:"game_addiction_cost" json:"gameAddictionCost" dc:"关键行为成本"`                                                 // 关键行为成本
	GameAddictionRate                     float64 `orm:"game_addiction_rate" json:"gameAddictionRate" dc:"关键行为率"`                                                  // 关键行为率
	AttributionNextDayOpenCnt             int64   `orm:"attribution_next_day_open_cnt" json:"attributionNextDayOpenCnt" dc:"次留数"`                                  // 次留数
	AttributionNextDayOpenCost            float64 `orm:"attribution_next_day_open_cost" json:"attributionNextDayOpenCost" dc:"次留成本"`                               // 次留成本
	AttributionNextDayOpenRate            float64 `orm:"attribution_next_day_open_rate" json:"attributionNextDayOpenRate" dc:"次留率"`                                // 次留率
	NextDayOpen                           int64   `orm:"next_day_open" json:"nextDayOpen" dc:"次留回传数"`                                                              // 次留回传数
	ActivePay                             int64   `orm:"active_pay" json:"activePay" dc:"首次付费数"`                                                                   // 首次付费数
	ActivePayCost                         float64 `orm:"active_pay_cost" json:"activePayCost" dc:"首次付费成本"`                                                         // 首次付费成本
	ActivePayRate                         float64 `orm:"active_pay_rate" json:"activePayRate" dc:"首次付费率"`                                                          // 首次付费率
	GamePayCount                          int64   `orm:"game_pay_count" json:"gamePayCount" dc:"付费次数"`                                                             // 付费次数
	GamePayCost                           float64 `orm:"game_pay_cost" json:"gamePayCost" dc:"付费成本"`                                                               // 付费成本
	AttributionGamePay7DCount             int64   `orm:"attribution_game_pay_7d_count" json:"attributionGamePay7DCount" dc:"7日付费次数(激活时间)"`                         // 7日付费次数(激活时间)
	AttributionGamePay7DCost              float64 `orm:"attribution_game_pay_7d_cost" json:"attributionGamePay7DCost" dc:"7日付费成本(激活时间)"`                           // 7日付费成本(激活时间)
	AttributionActivePay7DPerCount        int64   `orm:"attribution_active_pay_7d_per_count" json:"attributionActivePay7DPerCount" dc:"7日人均付费次数(激活时间)"`            // 7日人均付费次数(激活时间)
	InAppUv                               int64   `orm:"in_app_uv" json:"inAppUv" dc:"APP内访问"`                                                                     // APP内访问
	InAppDetailUv                         int64   `orm:"in_app_detail_uv" json:"inAppDetailUv" dc:"APP内访问详情页"`                                                     // APP内访问详情页
	InAppCart                             int64   `orm:"in_app_cart" json:"inAppCart" dc:"APP内加入购物车"`                                                              // APP内加入购物车
	InAppPay                              int64   `orm:"in_app_pay" json:"inAppPay" dc:"APP内付费"`                                                                   // APP内付费
	InAppOrder                            int64   `orm:"in_app_order" json:"inAppOrder" dc:"APP内下单"`                                                               // APP内下单
	AttributionGameInAppLtv1Day           float64 `orm:"attribution_game_in_app_ltv_1day" json:"attributionGameInAppLtv1Day" dc:"当日付费金额"`                          // 当日付费金额
	AttributionGameInAppLtv2Days          float64 `orm:"attribution_game_in_app_ltv_2days" json:"attributionGameInAppLtv2Days" dc:"激活后一日付费金额"`                     // 激活后一日付费金额
	AttributionGameInAppLtv3Days          float64 `orm:"attribution_game_in_app_ltv_3days" json:"attributionGameInAppLtv3Days" dc:"激活后二日付费金额"`                     // 激活后二日付费金额
	AttributionGameInAppLtv4Days          float64 `orm:"attribution_game_in_app_ltv_4days" json:"attributionGameInAppLtv4Days" dc:"激活后三日付费金额"`                     // 激活后三日付费金额
	AttributionGameInAppLtv5Days          float64 `orm:"attribution_game_in_app_ltv_5days" json:"attributionGameInAppLtv5Days" dc:"激活后四日付费金额"`                     // 激活后四日付费金额
	AttributionGameInAppLtv6Days          float64 `orm:"attribution_game_in_app_ltv_6days" json:"attributionGameInAppLtv6Days" dc:"激活后五日付费金额"`                     // 激活后五日付费金额
	AttributionGameInAppLtv7Days          float64 `orm:"attribution_game_in_app_ltv_7days" json:"attributionGameInAppLtv7Days" dc:"激活后六日付费金额"`                     // 激活后六日付费金额
	AttributionGameInAppLtv8Days          float64 `orm:"attribution_game_in_app_ltv_8days" json:"attributionGameInAppLtv8Days" dc:"激活后七日付费金额"`                     // 激活后七日付费金额
	AttributionGameInAppRoi1Day           float64 `orm:"attribution_game_in_app_roi_1day" json:"attributionGameInAppRoi1Day" dc:"当日付费ROI"`                         // 当日付费ROI
	AttributionGameInAppRoi2Days          float64 `orm:"attribution_game_in_app_roi_2days" json:"attributionGameInAppRoi2Days" dc:"激活后一日付费ROI"`                    // 激活后一日付费ROI
	AttributionGameInAppRoi3Days          float64 `orm:"attribution_game_in_app_roi_3days" json:"attributionGameInAppRoi3Days" dc:"激活后二日付费ROI"`                    // 激活后二日付费ROI
	AttributionGameInAppRoi4Days          float64 `orm:"attribution_game_in_app_roi_4days" json:"attributionGameInAppRoi4Days" dc:"激活后三日付费ROI"`                    // 激活后三日付费ROI
	AttributionGameInAppRoi5Days          float64 `orm:"attribution_game_in_app_roi_5days" json:"attributionGameInAppRoi5Days" dc:"激活后四日付费ROI"`                    // 激活后四日付费ROI
	AttributionGameInAppRoi6Days          float64 `orm:"attribution_game_in_app_roi_6days" json:"attributionGameInAppRoi6Days" dc:"激活后五日付费ROI"`                    // 激活后五日付费ROI
	AttributionGameInAppRoi7Days          float64 `orm:"attribution_game_in_app_roi_7days" json:"attributionGameInAppRoi7Days" dc:"激活后六日付费ROI"`                    // 激活后六日付费ROI
	AttributionGameInAppRoi8Days          float64 `orm:"attribution_game_in_app_roi_8days" json:"attributionGameInAppRoi8Days" dc:"激活后七日付费ROI"`                    // 激活后七日付费ROI
	AttributionDayActivePayCount          int64   `orm:"attribution_day_active_pay_count" json:"attributionDayActivePayCount" dc:"计费当日激活且首次付费数"`                   // 计费当日激活且首次付费数
	AttributionActivePayIntraOneDayCount  int64   `orm:"attribution_active_pay_intra_one_day_count" json:"attributionActivePayIntraOneDayCount" dc:"激活后24h首次付费数"`  // 激活后24h首次付费数
	AttributionActivePayIntraOneDayCost   float64 `orm:"attribution_active_pay_intra_one_day_cost" json:"attributionActivePayIntraOneDayCost" dc:"激活后24h首次付费成本"`   // 激活后24h首次付费成本
	AttributionActivePayIntraOneDayRate   float64 `orm:"attribution_active_pay_intra_one_day_rate" json:"attributionActivePayIntraOneDayRate" dc:"激活后24h首次付费率"`    // 激活后24h首次付费率
	AttributionActivePayIntraOneDayAmount float64 `orm:"attribution_active_pay_intra_one_day_amount" json:"attributionActivePayIntraOneDayAmount" dc:"激活后24h付费金额"` // 激活后24h付费金额
	AttributionActivePayIntraOneDayRoi    float64 `orm:"attribution_active_pay_intra_one_day_roi" json:"attributionActivePayIntraOneDayRoi" dc:"激活后24h付费ROI"`      // 激活后24h付费ROI
	AttributionRetention2DCnt             int64   `orm:"attribution_retention_2d_cnt" json:"attributionRetention2DCnt" dc:"2日留存数"`                                 // 2日留存数
	AttributionRetention2DCost            float64 `orm:"attribution_retention_2d_cost" json:"attributionRetention2DCost" dc:"2日留存成本"`                              // 2日留存成本
	AttributionRetention2DRate            float64 `orm:"attribution_retention_2d_rate" json:"attributionRetention2DRate" dc:"2日留存率"`                               // 2日留存率
	AttributionRetention3DCnt             int64   `orm:"attribution_retention_3d_cnt" json:"attributionRetention3DCnt" dc:"3日留存数"`                                 // 3日留存数
	AttributionRetention3DCost            float64 `orm:"attribution_retention_3d_cost" json:"attributionRetention3DCost" dc:"3日留存成本"`                              // 3日留存成本
	AttributionRetention3DRate            float64 `orm:"attribution_retention_3d_rate" json:"attributionRetention3DRate" dc:"3日留存率"`                               // 3日留存率
	AttributionRetention4DCnt             int64   `orm:"attribution_retention_4d_cnt" json:"attributionRetention4DCnt" dc:"4日留存数"`                                 // 4日留存数
	AttributionRetention4DCost            float64 `orm:"attribution_retention_4d_cost" json:"attributionRetention4DCost" dc:"4日留存成本"`                              // 4日留存成本
	AttributionRetention4DRate            float64 `orm:"attribution_retention_4d_rate" json:"attributionRetention4DRate" dc:"4日留存率"`                               // 4日留存率
	AttributionRetention5DCnt             int64   `orm:"attribution_retention_5d_cnt" json:"attributionRetention5DCnt" dc:"5日留存数"`                                 // 5日留存数
	AttributionRetention5DCost            float64 `orm:"attribution_retention_5d_cost" json:"attributionRetention5DCost" dc:"5日留存成本"`                              // 5日留存成本
	AttributionRetention5DRate            float64 `orm:"attribution_retention_5d_rate" json:"attributionRetention5DRate" dc:"5日留存率"`                               // 5日留存率
	AttributionRetention6DCnt             int64   `orm:"attribution_retention_6d_cnt" json:"attributionRetention6DCnt" dc:"6日留存数"`                                 // 6日留存数
	AttributionRetention6DCost            float64 `orm:"attribution_retention_6d_cost" json:"attributionRetention6DCost" dc:"6日留存成本"`                              // 6日留存成本
	AttributionRetention6DRate            float64 `orm:"attribution_retention_6d_rate" json:"attributionRetention6DRate" dc:"6日留存率"`                               // 6日留存率
	AttributionRetention7DCnt             int64   `orm:"attribution_retention_7d_cnt" json:"attributionRetention7DCnt" dc:"7日留存数"`                                 // 7日留存数
	AttributionRetention7DCost            float64 `orm:"attribution_retention_7d_cost" json:"attributionRetention7DCost" dc:"7日留存成本"`                              // 7日留存成本
	AttributionRetention7DRate            float64 `orm:"attribution_retention_7d_rate" json:"attributionRetention7DRate" dc:"7日留存率"`                               // 7日留存率
	AttributionRetention7DSumCnt          int64   `orm:"attribution_retention_7d_sum_cnt" json:"attributionRetention7DSumCnt" dc:"7日留存总数"`                         // 7日留存总数
	AttributionRetention7DTotalCost       float64 `orm:"attribution_retention_7d_total_cost" json:"attributionRetention7DTotalCost" dc:"7日留存总成本"`                  // 7日留存总成本
	TotalPlay                             int64   `orm:"total_play" json:"totalPlay" dc:"播放量"`                                                                     // 播放量
	ValidPlay                             int64   `orm:"valid_play" json:"validPlay" dc:"有效播放数"`                                                                   // 有效播放数
	ValidPlayCost                         float64 `orm:"valid_play_cost" json:"validPlayCost" dc:"有效播放成本"`                                                         // 有效播放成本
	ValidPlayRate                         float64 `orm:"valid_play_rate" json:"validPlayRate" dc:"有效播放率"`                                                          // 有效播放率
	Play25FeedBreak                       int64   `orm:"play_25_feed_break" json:"play25FeedBreak" dc:"25%进度播放数"`                                                  // 25%进度播放数
	Play50FeedBreak                       int64   `orm:"play_50_feed_break" json:"play50FeedBreak" dc:"50%进度播放数"`                                                  // 50%进度播放数
	Play75FeedBreak                       int64   `orm:"play_75_feed_break" json:"play75FeedBreak" dc:"75%进度播放数"`                                                  // 75%进度播放数
	Play99FeedBreak                       int64   `orm:"play_99_feed_break" json:"play99FeedBreak" dc:"99%进度播放数"`                                                  // 99%进度播放数
	AveragePlayTimePerPlay                float64 `orm:"average_play_time_per_play" json:"averagePlayTimePerPlay" dc:"平均单次播放时长"`                                   // 平均单次播放时长
	PlayOverRate                          float64 `orm:"play_over_rate" json:"playOverRate" dc:"完播率"`                                                              // 完播率
	WifiPlayRate                          float64 `orm:"wifi_play_rate" json:"wifiPlayRate" dc:"WiFi播放占比"`                                                         // WiFi播放占比
	CardShow                              int64   `orm:"card_show" json:"cardShow" dc:"3秒卡片展现数"`                                                                   // 3秒卡片展现数
	DyLike                                int64   `orm:"dy_like" json:"dyLike" dc:"点赞数"`                                                                           // 点赞数
	DyComment                             int64   `orm:"dy_comment" json:"dyComment" dc:"评论量"`                                                                     // 评论量
	DyShare                               int64   `orm:"dy_share" json:"dyShare" dc:"分享量"`                                                                         // 分享量
	IesChallengeClick                     int64   `orm:"ies_challenge_click" json:"iesChallengeClick" dc:"挑战赛查看数"`                                                 // 挑战赛查看数
	IesMusicClick                         int64   `orm:"ies_music_click" json:"iesMusicClick" dc:"音乐查看数"`                                                          // 音乐查看数
	LocationClick                         int64   `orm:"location_click" json:"locationClick" dc:"POI点击数"`                                                          // POI点击数
	CustomerEffective                     int64   `orm:"customer_effective" json:"customerEffective" dc:"有效获客"`                                                    // 有效获客
	Wechat                                int64   `orm:"wechat" json:"wechat" dc:"微信复制"`                                                                           // 微信复制
	AttributionMicroGame0DLtv             float64 `orm:"attribution_micro_game_0d_ltv" json:"attributionMicroGame0DLtv" dc:"小程序/小游戏当日LTV"`                         // 小程序/小游戏当日LTV
	AttributionMicroGame3DLtv             float64 `orm:"attribution_micro_game_3d_ltv" json:"attributionMicroGame3DLtv" dc:"小程序/小游戏激活后三日LTV"`                      // 小程序/小游戏激活后三日LTV
	AttributionMicroGame7DLtv             float64 `orm:"attribution_micro_game_7d_ltv" json:"attributionMicroGame7DLtv" dc:"小程序/小游戏激活后七日LTV"`                      // 小程序/小游戏激活后七日LTV
	AttributionMicroGame0DRoi             float64 `orm:"attribution_micro_game_0d_roi" json:"attributionMicroGame0DRoi" dc:"小程序/小游戏当日广告变现ROI"`                     // 小程序/小游戏当日广告变现ROI
	AttributionMicroGame3DRoi             float64 `orm:"attribution_micro_game_3d_roi" json:"attributionMicroGame3DRoi" dc:"小程序/小游戏激活后三日广告变现ROI"`                  // 小程序/小游戏激活后三日广告变现ROI
	AttributionMicroGame7DRoi             float64 `orm:"attribution_micro_game_7d_roi" json:"attributionMicroGame7DRoi" dc:"小程序/小游戏激活后七日广告变现ROI"`
	TotalAdUp                             float64 `orm:"total_ad_up" json:"totalAdUp,omitempty" dc:"广告收入"`
	TotalAmount                           float64 `orm:"total_amount" json:"totalAmount,omitempty" dc:"总充值金额"`
	// 小程序/小游戏激活后七日广告变现ROI
	//CreatedAt  *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`   // 创建时间
	//UpdatedAt  *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`   // 更新时间
	CreateDate string `orm:"create_date" json:"createDate" dc:"统计日期"` // 统计日期
}

type AdAdvertiserAccountMetricsDataListRes struct {
	Id                                    int         `json:"id,omitempty" dc:"自增id主键"`
	AdvertiserId                          string      `json:"advertiserId,omitempty" dc:"账户ID"`
	StatCost                              float64     `json:"statCost,omitempty" dc:"消耗"`
	ShowCnt                               int64       `json:"showCnt,omitempty" dc:"展示数"`
	CpmPlatform                           float64     `json:"cpmPlatform,omitempty" dc:"平均千次展现费用(元)"`
	ClickCnt                              int64       `json:"clickCnt,omitempty" dc:"点击数"`
	Ctr                                   float64     `json:"ctr,omitempty" dc:"点击率"`
	CpcPlatform                           float64     `json:"cpcPlatform,omitempty" dc:"平均点击单价(元)"`
	ConvertCnt                            int64       `json:"convertCnt,omitempty,omitempty" dc:"转化数"`
	ConversionCost                        float64     `json:"conversionCost,omitempty" dc:"平均转化成本"`
	ConversionRate                        float64     `json:"conversionRate,omitempty" dc:"转化率"`
	DeepConvertCnt                        int64       `json:"deepConvertCnt,omitempty" dc:"深度转化数"`
	DeepConvertCost                       float64     `json:"deepConvertCost,omitempty" dc:"深度转化成本"`
	DeepConvertRate                       float64     `json:"deepConvertRate,omitempty" dc:"深度转化率"`
	AttributionConvertCnt                 int64       `json:"attributionConvertCnt,omitempty" dc:"转化数(计费时间)"`
	AttributionConvertCost                float64     `json:"attributionConvertCost,omitempty" dc:"转化成本(计费时间)"`
	AttributionDeepConvertCnt             int64       `json:"attributionDeepConvertCnt,omitempty" dc:"深度转化数(计费时间)"`
	AttributionDeepConvertCost            float64     `json:"attributionDeepConvertCost,omitempty" dc:"深度转化成本(计费时间)"`
	PreConvertCount                       int64       `json:"preConvertCount,omitempty" dc:"预估转化数(计费时间)"`
	PreConvertCost                        float64     `json:"preConvertCost,omitempty" dc:"预估转化成本(计费时间)"`
	PreConvertRate                        float64     `json:"preConvertRate,omitempty" dc:"预估转化率(计费时间)"`
	ClickStartCnt                         int64       `json:"clickStartCnt,omitempty" dc:"安卓下载开始数"`
	ClickStartCost                        float64     `json:"clickStartCost,omitempty" dc:"安卓下载开始成本"`
	ClickStartRate                        float64     `json:"clickStartRate,omitempty" dc:"安卓下载开始率"`
	DownloadFinishCnt                     int64       `json:"downloadFinishCnt,omitempty" dc:"安卓下载完成数"`
	DownloadFinishCost                    float64     `json:"downloadFinishCost,omitempty" dc:"安卓下载完成成本"`
	DownloadFinishRate                    float64     `json:"downloadFinishRate,omitempty" dc:"安卓下载完成率"`
	InstallFinishCnt                      int64       `json:"installFinishCnt,omitempty" dc:"安卓安装完成数"`
	InstallFinishCost                     float64     `json:"installFinishCost,omitempty" dc:"安卓安装完成成本"`
	InstallFinishRate                     float64     `json:"installFinishRate,omitempty" dc:"安卓安装完成率"`
	Active                                int64       `json:"active,omitempty" dc:"激活数"`
	ActiveCost                            float64     `json:"activeCost,omitempty" dc:"激活成本"`
	ActiveRate                            float64     `json:"activeRate,omitempty" dc:"激活率"`
	ActiveRegisterCost                    float64     `json:"activeRegisterCost,omitempty" dc:"注册成本"`
	ActiveRegisterRate                    float64     `json:"activeRegisterRate,omitempty" dc:"注册率"`
	GameAddiction                         int64       `json:"gameAddiction,omitempty" dc:"关键行为数"`
	GameAddictionCost                     float64     `json:"gameAddictionCost,omitempty" dc:"关键行为成本"`
	GameAddictionRate                     float64     `json:"gameAddictionRate,omitempty" dc:"关键行为率"`
	AttributionNextDayOpenCnt             int64       `json:"attributionNextDayOpenCnt,omitempty" dc:"次留数"`
	AttributionNextDayOpenCost            float64     `json:"attributionNextDayOpenCost,omitempty" dc:"次留成本"`
	AttributionNextDayOpenRate            float64     `json:"attributionNextDayOpenRate,omitempty" dc:"次留率"`
	NextDayOpen                           int64       `json:"nextDayOpen,omitempty" dc:"次留回传数"`
	ActivePay                             int64       `json:"activePay,omitempty" dc:"首次付费数"`
	ActivePayCost                         float64     `json:"activePayCost,omitempty" dc:"首次付费成本"`
	ActivePayRate                         float64     `json:"activePayRate,omitempty" dc:"首次付费率"`
	GamePayCount                          int64       `json:"gamePayCount,omitempty" dc:"付费次数"`
	GamePayCost                           float64     `json:"gamePayCost,omitempty" dc:"付费成本"`
	AttributionGamePay7DCount             int64       `json:"attributionGamePay7DCount,omitempty" dc:"7日付费次数(激活时间)"`
	AttributionGamePay7DCost              float64     `json:"attributionGamePay7DCost,omitempty" dc:"7日付费成本(激活时间)"`
	AttributionActivePay7DPerCount        int64       `json:"attributionActivePay7DPerCount,omitempty" dc:"7日人均付费次数(激活时间)"`
	InAppUv                               int64       `json:"inAppUv,omitempty" dc:"APP内访问"`
	InAppDetailUv                         int64       `json:"inAppDetailUv,omitempty" dc:"APP内访问详情页"`
	InAppCart                             int64       `json:"inAppCart,omitempty" dc:"APP内加入购物车"`
	InAppPay                              int64       `json:"inAppPay,omitempty" dc:"APP内付费"`
	InAppOrder                            int64       `json:"inAppOrder,omitempty" dc:"APP内下单"`
	AttributionGameInAppLtv1Day           float64     `json:"attributionGameInAppLtv1Day,omitempty" dc:"当日付费金额"`
	AttributionGameInAppLtv2Days          float64     `json:"attributionGameInAppLtv2Days,omitempty" dc:"激活后一日付费金额"`
	AttributionGameInAppLtv3Days          float64     `json:"attributionGameInAppLtv3Days,omitempty" dc:"激活后二日付费金额"`
	AttributionGameInAppLtv4Days          float64     `json:"attributionGameInAppLtv4Days,omitempty" dc:"激活后三日付费金额"`
	AttributionGameInAppLtv5Days          float64     `json:"attributionGameInAppLtv5Days,omitempty" dc:"激活后四日付费金额"`
	AttributionGameInAppLtv6Days          float64     `json:"attributionGameInAppLtv6Days,omitempty" dc:"激活后五日付费金额"`
	AttributionGameInAppLtv7Days          float64     `json:"attributionGameInAppLtv7Days,omitempty" dc:"激活后六日付费金额"`
	AttributionGameInAppLtv8Days          float64     `json:"attributionGameInAppLtv8Days,omitempty" dc:"激活后七日付费金额"`
	AttributionGameInAppRoi1Day           float64     `json:"attributionGameInAppRoi1Day,omitempty" dc:"当日付费ROI"`
	AttributionGameInAppRoi2Days          float64     `json:"attributionGameInAppRoi2Days,omitempty" dc:"激活后一日付费ROI"`
	AttributionGameInAppRoi3Days          float64     `json:"attributionGameInAppRoi3Days,omitempty" dc:"激活后二日付费ROI"`
	AttributionGameInAppRoi4Days          float64     `json:"attributionGameInAppRoi4Days,omitempty" dc:"激活后三日付费ROI"`
	AttributionGameInAppRoi5Days          float64     `json:"attributionGameInAppRoi5Days,omitempty" dc:"激活后四日付费ROI"`
	AttributionGameInAppRoi6Days          float64     `json:"attributionGameInAppRoi6Days,omitempty" dc:"激活后五日付费ROI"`
	AttributionGameInAppRoi7Days          float64     `json:"attributionGameInAppRoi7Days,omitempty" dc:"激活后六日付费ROI"`
	AttributionGameInAppRoi8Days          float64     `json:"attributionGameInAppRoi8Days,omitempty" dc:"激活后七日付费ROI"`
	AttributionDayActivePayCount          int64       `json:"attributionDayActivePayCount,omitempty" dc:"计费当日激活且首次付费数"`
	AttributionActivePayIntraOneDayCount  int64       `json:"attributionActivePayIntraOneDayCount,omitempty" dc:"激活后24h首次付费数"`
	AttributionActivePayIntraOneDayCost   float64     `json:"attributionActivePayIntraOneDayCost,omitempty" dc:"激活后24h首次付费成本"`
	AttributionActivePayIntraOneDayRate   float64     `json:"attributionActivePayIntraOneDayRate,omitempty" dc:"激活后24h首次付费率"`
	AttributionActivePayIntraOneDayAmount float64     `json:"attributionActivePayIntraOneDayAmount,omitempty" dc:"激活后24h付费金额"`
	AttributionActivePayIntraOneDayRoi    float64     `json:"attributionActivePayIntraOneDayRoi,omitempty" dc:"激活后24h付费ROI"`
	AttributionRetention2DCnt             int64       `json:"attributionRetention2DCnt,omitempty" dc:"2日留存数"`
	AttributionRetention2DCost            float64     `json:"attributionRetention2DCost,omitempty" dc:"2日留存成本"`
	AttributionRetention2DRate            float64     `json:"attributionRetention2DRate,omitempty" dc:"2日留存率"`
	AttributionRetention3DCnt             int64       `json:"attributionRetention3DCnt,omitempty" dc:"3日留存数"`
	AttributionRetention3DCost            float64     `json:"attributionRetention3DCost,omitempty" dc:"3日留存成本"`
	AttributionRetention3DRate            float64     `json:"attributionRetention3DRate,omitempty" dc:"3日留存率"`
	AttributionRetention4DCnt             int64       `json:"attributionRetention4DCnt,omitempty" dc:"4日留存数"`
	AttributionRetention4DCost            float64     `json:"attributionRetention4DCost,omitempty" dc:"4日留存成本"`
	AttributionRetention4DRate            float64     `json:"attributionRetention4DRate,omitempty" dc:"4日留存率"`
	AttributionRetention5DCnt             int64       `json:"attributionRetention5DCnt,omitempty" dc:"5日留存数"`
	AttributionRetention5DCost            float64     `json:"attributionRetention5DCost,omitempty" dc:"5日留存成本"`
	AttributionRetention5DRate            float64     `json:"attributionRetention5DRate,omitempty" dc:"5日留存率"`
	AttributionRetention6DCnt             int64       `json:"attributionRetention6DCnt,omitempty" dc:"6日留存数"`
	AttributionRetention6DCost            float64     `json:"attributionRetention6DCost,omitempty" dc:"6日留存成本"`
	AttributionRetention6DRate            float64     `json:"attributionRetention6DRate,omitempty" dc:"6日留存率"`
	AttributionRetention7DCnt             int64       `json:"attributionRetention7DCnt,omitempty" dc:"7日留存数"`
	AttributionRetention7DCost            float64     `json:"attributionRetention7DCost,omitempty" dc:"7日留存成本"`
	AttributionRetention7DRate            float64     `json:"attributionRetention7DRate,omitempty" dc:"7日留存率"`
	AttributionRetention7DSumCnt          int64       `json:"attributionRetention7DSumCnt,omitempty" dc:"7日留存总数"`
	AttributionRetention7DTotalCost       float64     `json:"attributionRetention7DTotalCost,omitempty" dc:"7日留存总成本"`
	TotalPlay                             int64       `json:"totalPlay,omitempty" dc:"播放量"`
	ValidPlay                             int64       `json:"validPlay,omitempty" dc:"有效播放数"`
	ValidPlayCost                         float64     `json:"validPlayCost,omitempty" dc:"有效播放成本"`
	ValidPlayRate                         float64     `json:"validPlayRate,omitempty" dc:"有效播放率"`
	Play25FeedBreak                       int64       `json:"play25FeedBreak,omitempty" dc:"25%进度播放数"`
	Play50FeedBreak                       int64       `json:"play50FeedBreak,omitempty" dc:"50%进度播放数"`
	Play75FeedBreak                       int64       `json:"play75FeedBreak,omitempty" dc:"75%进度播放数"`
	Play99FeedBreak                       int64       `json:"play99FeedBreak,omitempty" dc:"99%进度播放数"`
	AveragePlayTimePerPlay                float64     `json:"averagePlayTimePerPlay,omitempty" dc:"平均单次播放时长"`
	PlayOverRate                          float64     `json:"playOverRate,omitempty" dc:"完播率"`
	WifiPlayRate                          float64     `json:"wifiPlayRate,omitempty" dc:"WiFi播放占比"`
	CardShow                              int64       `json:"cardShow,omitempty" dc:"3秒卡片展现数"`
	DyLike                                int64       `json:"dyLike,omitempty" dc:"点赞数"`
	DyComment                             int64       `json:"dyComment,omitempty" dc:"评论量"`
	DyShare                               int64       `json:"dyShare,omitempty" dc:"分享量"`
	IesChallengeClick                     int64       `json:"iesChallengeClick,omitempty" dc:"挑战赛查看数"`
	IesMusicClick                         int64       `json:"iesMusicClick,omitempty" dc:"音乐查看数"`
	LocationClick                         int64       `json:"locationClick,omitempty" dc:"POI点击数"`
	CustomerEffective                     int64       `json:"customerEffective,omitempty" dc:"有效获客"`
	Wechat                                int64       `json:"wechat,omitempty" dc:"微信复制"`
	AttributionMicroGame0DLtv             float64     `json:"attributionMicroGame0DLtv,omitempty" dc:"小程序/小游戏当日LTV"`
	AttributionMicroGame3DLtv             float64     `json:"attributionMicroGame3DLtv,omitempty" dc:"小程序/小游戏激活后三日LTV"`
	AttributionMicroGame7DLtv             float64     `json:"attributionMicroGame7DLtv,omitempty" dc:"小程序/小游戏激活后七日LTV"`
	AttributionMicroGame0DRoi             float64     `json:"attributionMicroGame0DRoi,omitempty" dc:"小程序/小游戏当日广告变现ROI"`
	AttributionMicroGame3DRoi             float64     `json:"attributionMicroGame3DRoi,omitempty" dc:"小程序/小游戏激活后三日广告变现ROI"`
	AttributionMicroGame7DRoi             float64     `json:"attributionMicroGame7DRoi,omitempty" dc:"小程序/小游戏激活后七日广告变现ROI"`
	CreatedAt                             *gtime.Time `json:"createdAt" dc:"创建时间"`
	CreateDate                            string      `json:"createDate" dc:"统计日期"`
}

type AdAdvertiserAccountTokenRes struct {
	AdvertiserId string `json:"advertiserId" dc:"账户ID"`   //账户ID
	AccessToken  string `json:"accessToken" dc:"授权token"` // token
	ProjectId    string `json:"projectId" dc:"项目ID"`
	PromotionId  string `json:"promotion_id" dc:"promotion_id"`
}

type AdAdvertiserAccountReportDataSearch struct {
	comModel.PageReq
	StartTime string `p:"startTime" dc:"开始时间格式 yyyy-MM-dd"`
	EndTime   string `p:"endTime" dc:"结束时间格式 yyyy-MM-dd"`
	// 优化师
	UserId int `p:"userId"  dc:"用户ID"`
	// 账户主体
	Company string `p:"company"  dc:"账户主体"`
	// 账户名称
	AccountName string `p:"accountName"  dc:"账户名称"`
	// 账户id
	AccountId string `p:"accountId"  dc:"账户名称"`
	// 账户备注
	AccountRemark string `p:"accountRemark"  dc:"备注"`
	// 项目Id ？
	//ProjectId int `p:"projectId"  dc:"项目ID"`
	// 部门id
	DeptId int `p:"deptId"  dc:"部门ID"`
	//产品id
	//ProductId int `p:"productId"  dc:"产品ID"`
	// 需要筛选的列名
	Fields       string   `p:"fields"`
	AccountNames []string `p:"accountNames"  dc:"账户名称列表"`
	AccountIds   string   `p:"accountIds"  dc:"账户ID列表"`
	Keyword      string   `p:"keyword" dc:"关键字"`
}

// AdAdvertiserAccountMetricsDataSearchReq 分页请求参数
type AdAdvertiserAccountMetricsDataSearchReq struct {
	comModel.PageReq
	Id                                    string `p:"id" dc:"自增id主键"`                                                                                                         //自增id主键
	AdvertiserId                          string `p:"advertiserId" dc:"账户ID"`                                                                                                 //账户ID
	StatCost                              string `p:"statCost" v:"statCost@float#消耗需为浮点数" dc:"消耗"`                                                                            //消耗
	ShowCnt                               string `p:"showCnt" v:"showCnt@integer#展示数需为整数" dc:"展示数"`                                                                           //展示数
	CpmPlatform                           string `p:"cpmPlatform" v:"cpmPlatform@float#平均千次展现费用(元)需为浮点数" dc:"平均千次展现费用(元)"`                                                    //平均千次展现费用(元)
	ClickCnt                              string `p:"clickCnt" v:"clickCnt@integer#点击数需为整数" dc:"点击数"`                                                                         //点击数
	Ctr                                   string `p:"ctr" v:"ctr@float#点击率需为浮点数" dc:"点击率"`                                                                                    //点击率
	CpcPlatform                           string `p:"cpcPlatform" v:"cpcPlatform@float#平均点击单价(元)需为浮点数" dc:"平均点击单价(元)"`                                                        //平均点击单价(元)
	ConvertCnt                            string `p:"convertCnt" v:"convertCnt@integer#转化数需为整数" dc:"转化数"`                                                                     //转化数
	ConversionCost                        string `p:"conversionCost" v:"conversionCost@float#平均转化成本需为浮点数" dc:"平均转化成本"`                                                        //平均转化成本
	ConversionRate                        string `p:"conversionRate" v:"conversionRate@float#转化率需为浮点数" dc:"转化率"`                                                              //转化率
	DeepConvertCnt                        string `p:"deepConvertCnt" v:"deepConvertCnt@integer#深度转化数需为整数" dc:"深度转化数"`                                                         //深度转化数
	DeepConvertCost                       string `p:"deepConvertCost" v:"deepConvertCost@float#深度转化成本需为浮点数" dc:"深度转化成本"`                                                      //深度转化成本
	DeepConvertRate                       string `p:"deepConvertRate" v:"deepConvertRate@float#深度转化率需为浮点数" dc:"深度转化率"`                                                        //深度转化率
	AttributionConvertCnt                 string `p:"attributionConvertCnt" v:"attributionConvertCnt@integer#转化数(计费时间)需为整数" dc:"转化数(计费时间)"`                                   //转化数(计费时间)
	AttributionConvertCost                string `p:"attributionConvertCost" v:"attributionConvertCost@float#转化成本(计费时间)需为浮点数" dc:"转化成本(计费时间)"`                                //转化成本(计费时间)
	AttributionDeepConvertCnt             string `p:"attributionDeepConvertCnt" v:"attributionDeepConvertCnt@integer#深度转化数(计费时间)需为整数" dc:"深度转化数(计费时间)"`                       //深度转化数(计费时间)
	AttributionDeepConvertCost            string `p:"attributionDeepConvertCost" v:"attributionDeepConvertCost@float#深度转化成本(计费时间)需为浮点数" dc:"深度转化成本(计费时间)"`                    //深度转化成本(计费时间)
	PreConvertCount                       string `p:"preConvertCount" v:"preConvertCount@integer#预估转化数(计费时间)需为整数" dc:"预估转化数(计费时间)"`                                           //预估转化数(计费时间)
	PreConvertCost                        string `p:"preConvertCost" v:"preConvertCost@float#预估转化成本(计费时间)需为浮点数" dc:"预估转化成本(计费时间)"`                                            //预估转化成本(计费时间)
	PreConvertRate                        string `p:"preConvertRate" v:"preConvertRate@float#预估转化率(计费时间)需为浮点数" dc:"预估转化率(计费时间)"`                                              //预估转化率(计费时间)
	ClickStartCnt                         string `p:"clickStartCnt" v:"clickStartCnt@integer#安卓下载开始数需为整数" dc:"安卓下载开始数"`                                                       //安卓下载开始数
	ClickStartCost                        string `p:"clickStartCost" v:"clickStartCost@float#安卓下载开始成本需为浮点数" dc:"安卓下载开始成本"`                                                    //安卓下载开始成本
	ClickStartRate                        string `p:"clickStartRate" v:"clickStartRate@float#安卓下载开始率需为浮点数" dc:"安卓下载开始率"`                                                      //安卓下载开始率
	DownloadFinishCnt                     string `p:"downloadFinishCnt" v:"downloadFinishCnt@integer#安卓下载完成数需为整数" dc:"安卓下载完成数"`                                               //安卓下载完成数
	DownloadFinishCost                    string `p:"downloadFinishCost" v:"downloadFinishCost@float#安卓下载完成成本需为浮点数" dc:"安卓下载完成成本"`                                            //安卓下载完成成本
	DownloadFinishRate                    string `p:"downloadFinishRate" v:"downloadFinishRate@float#安卓下载完成率需为浮点数" dc:"安卓下载完成率"`                                              //安卓下载完成率
	InstallFinishCnt                      string `p:"installFinishCnt" v:"installFinishCnt@integer#安卓安装完成数需为整数" dc:"安卓安装完成数"`                                                 //安卓安装完成数
	InstallFinishCost                     string `p:"installFinishCost" v:"installFinishCost@float#安卓安装完成成本需为浮点数" dc:"安卓安装完成成本"`                                              //安卓安装完成成本
	InstallFinishRate                     string `p:"installFinishRate" v:"installFinishRate@float#安卓安装完成率需为浮点数" dc:"安卓安装完成率"`                                                //安卓安装完成率
	Active                                string `p:"active" v:"active@integer#激活数需为整数" dc:"激活数"`                                                                             //激活数
	ActiveCost                            string `p:"activeCost" v:"activeCost@float#激活成本需为浮点数" dc:"激活成本"`                                                                    //激活成本
	ActiveRate                            string `p:"activeRate" v:"activeRate@float#激活率需为浮点数" dc:"激活率"`                                                                      //激活率
	ActiveRegisterCost                    string `p:"activeRegisterCost" v:"activeRegisterCost@float#注册成本需为浮点数" dc:"注册成本"`                                                    //注册成本
	ActiveRegisterRate                    string `p:"activeRegisterRate" v:"activeRegisterRate@float#注册率需为浮点数" dc:"注册率"`                                                      //注册率
	GameAddiction                         string `p:"gameAddiction" v:"gameAddiction@integer#关键行为数需为整数" dc:"关键行为数"`                                                           //关键行为数
	GameAddictionCost                     string `p:"gameAddictionCost" v:"gameAddictionCost@float#关键行为成本需为浮点数" dc:"关键行为成本"`                                                  //关键行为成本
	GameAddictionRate                     string `p:"gameAddictionRate" v:"gameAddictionRate@float#关键行为率需为浮点数" dc:"关键行为率"`                                                    //关键行为率
	AttributionNextDayOpenCnt             string `p:"attributionNextDayOpenCnt" v:"attributionNextDayOpenCnt@integer#次留数需为整数" dc:"次留数"`                                       //次留数
	AttributionNextDayOpenCost            string `p:"attributionNextDayOpenCost" v:"attributionNextDayOpenCost@float#次留成本需为浮点数" dc:"次留成本"`                                    //次留成本
	AttributionNextDayOpenRate            string `p:"attributionNextDayOpenRate" v:"attributionNextDayOpenRate@float#次留率需为浮点数" dc:"次留率"`                                      //次留率
	NextDayOpen                           string `p:"nextDayOpen" v:"nextDayOpen@integer#次留回传数需为整数" dc:"次留回传数"`                                                               //次留回传数
	ActivePay                             string `p:"activePay" v:"activePay@integer#首次付费数需为整数" dc:"首次付费数"`                                                                   //首次付费数
	ActivePayCost                         string `p:"activePayCost" v:"activePayCost@float#首次付费成本需为浮点数" dc:"首次付费成本"`                                                          //首次付费成本
	ActivePayRate                         string `p:"activePayRate" v:"activePayRate@float#首次付费率需为浮点数" dc:"首次付费率"`                                                            //首次付费率
	GamePayCount                          string `p:"gamePayCount" v:"gamePayCount@integer#付费次数需为整数" dc:"付费次数"`                                                               //付费次数
	GamePayCost                           string `p:"gamePayCost" v:"gamePayCost@float#付费成本需为浮点数" dc:"付费成本"`                                                                  //付费成本
	AttributionGamePay7DCount             string `p:"attributionGamePay7DCount" v:"attributionGamePay7DCount@integer#7日付费次数(激活时间)需为整数" dc:"7日付费次数(激活时间)"`                     //7日付费次数(激活时间)
	AttributionGamePay7DCost              string `p:"attributionGamePay7DCost" v:"attributionGamePay7DCost@float#7日付费成本(激活时间)需为浮点数" dc:"7日付费成本(激活时间)"`                        //7日付费成本(激活时间)
	AttributionActivePay7DPerCount        string `p:"attributionActivePay7DPerCount" v:"attributionActivePay7DPerCount@integer#7日人均付费次数(激活时间)需为整数" dc:"7日人均付费次数(激活时间)"`       //7日人均付费次数(激活时间)
	InAppUv                               string `p:"inAppUv" v:"inAppUv@integer#APP内访问需为整数" dc:"APP内访问"`                                                                     //APP内访问
	InAppDetailUv                         string `p:"inAppDetailUv" v:"inAppDetailUv@integer#APP内访问详情页需为整数" dc:"APP内访问详情页"`                                                   //APP内访问详情页
	InAppCart                             string `p:"inAppCart" v:"inAppCart@integer#APP内加入购物车需为整数" dc:"APP内加入购物车"`                                                           //APP内加入购物车
	InAppPay                              string `p:"inAppPay" v:"inAppPay@integer#APP内付费需为整数" dc:"APP内付费"`                                                                   //APP内付费
	InAppOrder                            string `p:"inAppOrder" v:"inAppOrder@integer#APP内下单需为整数" dc:"APP内下单"`                                                               //APP内下单
	AttributionGameInAppLtv1Day           string `p:"attributionGameInAppLtv1Day" v:"attributionGameInAppLtv1Day@float#当日付费金额需为浮点数" dc:"当日付费金额"`                              //当日付费金额
	AttributionGameInAppLtv2Days          string `p:"attributionGameInAppLtv2Days" v:"attributionGameInAppLtv2Days@float#激活后一日付费金额需为浮点数" dc:"激活后一日付费金额"`                      //激活后一日付费金额
	AttributionGameInAppLtv3Days          string `p:"attributionGameInAppLtv3Days" v:"attributionGameInAppLtv3Days@float#激活后二日付费金额需为浮点数" dc:"激活后二日付费金额"`                      //激活后二日付费金额
	AttributionGameInAppLtv4Days          string `p:"attributionGameInAppLtv4Days" v:"attributionGameInAppLtv4Days@float#激活后三日付费金额需为浮点数" dc:"激活后三日付费金额"`                      //激活后三日付费金额
	AttributionGameInAppLtv5Days          string `p:"attributionGameInAppLtv5Days" v:"attributionGameInAppLtv5Days@float#激活后四日付费金额需为浮点数" dc:"激活后四日付费金额"`                      //激活后四日付费金额
	AttributionGameInAppLtv6Days          string `p:"attributionGameInAppLtv6Days" v:"attributionGameInAppLtv6Days@float#激活后五日付费金额需为浮点数" dc:"激活后五日付费金额"`                      //激活后五日付费金额
	AttributionGameInAppLtv7Days          string `p:"attributionGameInAppLtv7Days" v:"attributionGameInAppLtv7Days@float#激活后六日付费金额需为浮点数" dc:"激活后六日付费金额"`                      //激活后六日付费金额
	AttributionGameInAppLtv8Days          string `p:"attributionGameInAppLtv8Days" v:"attributionGameInAppLtv8Days@float#激活后七日付费金额需为浮点数" dc:"激活后七日付费金额"`                      //激活后七日付费金额
	AttributionGameInAppRoi1Day           string `p:"attributionGameInAppRoi1Day" v:"attributionGameInAppRoi1Day@float#当日付费ROI需为浮点数" dc:"当日付费ROI"`                            //当日付费ROI
	AttributionGameInAppRoi2Days          string `p:"attributionGameInAppRoi2Days" v:"attributionGameInAppRoi2Days@float#激活后一日付费ROI需为浮点数" dc:"激活后一日付费ROI"`                    //激活后一日付费ROI
	AttributionGameInAppRoi3Days          string `p:"attributionGameInAppRoi3Days" v:"attributionGameInAppRoi3Days@float#激活后二日付费ROI需为浮点数" dc:"激活后二日付费ROI"`                    //激活后二日付费ROI
	AttributionGameInAppRoi4Days          string `p:"attributionGameInAppRoi4Days" v:"attributionGameInAppRoi4Days@float#激活后三日付费ROI需为浮点数" dc:"激活后三日付费ROI"`                    //激活后三日付费ROI
	AttributionGameInAppRoi5Days          string `p:"attributionGameInAppRoi5Days" v:"attributionGameInAppRoi5Days@float#激活后四日付费ROI需为浮点数" dc:"激活后四日付费ROI"`                    //激活后四日付费ROI
	AttributionGameInAppRoi6Days          string `p:"attributionGameInAppRoi6Days" v:"attributionGameInAppRoi6Days@float#激活后五日付费ROI需为浮点数" dc:"激活后五日付费ROI"`                    //激活后五日付费ROI
	AttributionGameInAppRoi7Days          string `p:"attributionGameInAppRoi7Days" v:"attributionGameInAppRoi7Days@float#激活后六日付费ROI需为浮点数" dc:"激活后六日付费ROI"`                    //激活后六日付费ROI
	AttributionGameInAppRoi8Days          string `p:"attributionGameInAppRoi8Days" v:"attributionGameInAppRoi8Days@float#激活后七日付费ROI需为浮点数" dc:"激活后七日付费ROI"`                    //激活后七日付费ROI
	AttributionDayActivePayCount          string `p:"attributionDayActivePayCount" v:"attributionDayActivePayCount@integer#计费当日激活且首次付费数需为整数" dc:"计费当日激活且首次付费数"`               //计费当日激活且首次付费数
	AttributionActivePayIntraOneDayCount  string `p:"attributionActivePayIntraOneDayCount" v:"attributionActivePayIntraOneDayCount@integer#激活后24h首次付费数需为整数" dc:"激活后24h首次付费数"` //激活后24h首次付费数
	AttributionActivePayIntraOneDayCost   string `p:"attributionActivePayIntraOneDayCost" v:"attributionActivePayIntraOneDayCost@float#激活后24h首次付费成本需为浮点数" dc:"激活后24h首次付费成本"`  //激活后24h首次付费成本
	AttributionActivePayIntraOneDayRate   string `p:"attributionActivePayIntraOneDayRate" v:"attributionActivePayIntraOneDayRate@float#激活后24h首次付费率需为浮点数" dc:"激活后24h首次付费率"`    //激活后24h首次付费率
	AttributionActivePayIntraOneDayAmount string `p:"attributionActivePayIntraOneDayAmount" v:"attributionActivePayIntraOneDayAmount@float#激活后24h付费金额需为浮点数" dc:"激活后24h付费金额"`  //激活后24h付费金额
	AttributionActivePayIntraOneDayRoi    string `p:"attributionActivePayIntraOneDayRoi" v:"attributionActivePayIntraOneDayRoi@float#激活后24h付费ROI需为浮点数" dc:"激活后24h付费ROI"`      //激活后24h付费ROI
	AttributionRetention2DCnt             string `p:"attributionRetention2DCnt" v:"attributionRetention2DCnt@integer#2日留存数需为整数" dc:"2日留存数"`                                   //2日留存数
	AttributionRetention2DCost            string `p:"attributionRetention2DCost" v:"attributionRetention2DCost@float#2日留存成本需为浮点数" dc:"2日留存成本"`                                //2日留存成本
	AttributionRetention2DRate            string `p:"attributionRetention2DRate" v:"attributionRetention2DRate@float#2日留存率需为浮点数" dc:"2日留存率"`                                  //2日留存率
	AttributionRetention3DCnt             string `p:"attributionRetention3DCnt" v:"attributionRetention3DCnt@integer#3日留存数需为整数" dc:"3日留存数"`                                   //3日留存数
	AttributionRetention3DCost            string `p:"attributionRetention3DCost" v:"attributionRetention3DCost@float#3日留存成本需为浮点数" dc:"3日留存成本"`                                //3日留存成本
	AttributionRetention3DRate            string `p:"attributionRetention3DRate" v:"attributionRetention3DRate@float#3日留存率需为浮点数" dc:"3日留存率"`                                  //3日留存率
	AttributionRetention4DCnt             string `p:"attributionRetention4DCnt" v:"attributionRetention4DCnt@integer#4日留存数需为整数" dc:"4日留存数"`                                   //4日留存数
	AttributionRetention4DCost            string `p:"attributionRetention4DCost" v:"attributionRetention4DCost@float#4日留存成本需为浮点数" dc:"4日留存成本"`                                //4日留存成本
	AttributionRetention4DRate            string `p:"attributionRetention4DRate" v:"attributionRetention4DRate@float#4日留存率需为浮点数" dc:"4日留存率"`                                  //4日留存率
	AttributionRetention5DCnt             string `p:"attributionRetention5DCnt" v:"attributionRetention5DCnt@integer#5日留存数需为整数" dc:"5日留存数"`                                   //5日留存数
	AttributionRetention5DCost            string `p:"attributionRetention5DCost" v:"attributionRetention5DCost@float#5日留存成本需为浮点数" dc:"5日留存成本"`                                //5日留存成本
	AttributionRetention5DRate            string `p:"attributionRetention5DRate" v:"attributionRetention5DRate@float#5日留存率需为浮点数" dc:"5日留存率"`                                  //5日留存率
	AttributionRetention6DCnt             string `p:"attributionRetention6DCnt" v:"attributionRetention6DCnt@integer#6日留存数需为整数" dc:"6日留存数"`                                   //6日留存数
	AttributionRetention6DCost            string `p:"attributionRetention6DCost" v:"attributionRetention6DCost@float#6日留存成本需为浮点数" dc:"6日留存成本"`                                //6日留存成本
	AttributionRetention6DRate            string `p:"attributionRetention6DRate" v:"attributionRetention6DRate@float#6日留存率需为浮点数" dc:"6日留存率"`                                  //6日留存率
	AttributionRetention7DCnt             string `p:"attributionRetention7DCnt" v:"attributionRetention7DCnt@integer#7日留存数需为整数" dc:"7日留存数"`                                   //7日留存数
	AttributionRetention7DCost            string `p:"attributionRetention7DCost" v:"attributionRetention7DCost@float#7日留存成本需为浮点数" dc:"7日留存成本"`                                //7日留存成本
	AttributionRetention7DRate            string `p:"attributionRetention7DRate" v:"attributionRetention7DRate@float#7日留存率需为浮点数" dc:"7日留存率"`                                  //7日留存率
	AttributionRetention7DSumCnt          string `p:"attributionRetention7DSumCnt" v:"attributionRetention7DSumCnt@integer#7日留存总数需为整数" dc:"7日留存总数"`                           //7日留存总数
	AttributionRetention7DTotalCost       string `p:"attributionRetention7DTotalCost" v:"attributionRetention7DTotalCost@float#7日留存总成本需为浮点数" dc:"7日留存总成本"`                    //7日留存总成本
	TotalPlay                             string `p:"totalPlay" v:"totalPlay@integer#播放量需为整数" dc:"播放量"`                                                                       //播放量
	ValidPlay                             string `p:"validPlay" v:"validPlay@integer#有效播放数需为整数" dc:"有效播放数"`                                                                   //有效播放数
	ValidPlayCost                         string `p:"validPlayCost" v:"validPlayCost@float#有效播放成本需为浮点数" dc:"有效播放成本"`                                                          //有效播放成本
	ValidPlayRate                         string `p:"validPlayRate" v:"validPlayRate@float#有效播放率需为浮点数" dc:"有效播放率"`                                                            //有效播放率
	Play25FeedBreak                       string `p:"play25FeedBreak" v:"play25FeedBreak@integer#25%进度播放数需为整数" dc:"25%进度播放数"`                                                 //25%进度播放数
	Play50FeedBreak                       string `p:"play50FeedBreak" v:"play50FeedBreak@integer#50%进度播放数需为整数" dc:"50%进度播放数"`                                                 //50%进度播放数
	Play75FeedBreak                       string `p:"play75FeedBreak" v:"play75FeedBreak@integer#75%进度播放数需为整数" dc:"75%进度播放数"`                                                 //75%进度播放数
	Play99FeedBreak                       string `p:"play99FeedBreak" v:"play99FeedBreak@integer#99%进度播放数需为整数" dc:"99%进度播放数"`                                                 //99%进度播放数
	AveragePlayTimePerPlay                string `p:"averagePlayTimePerPlay" v:"averagePlayTimePerPlay@float#平均单次播放时长需为浮点数" dc:"平均单次播放时长"`                                    //平均单次播放时长
	PlayOverRate                          string `p:"playOverRate" v:"playOverRate@float#完播率需为浮点数" dc:"完播率"`                                                                  //完播率
	WifiPlayRate                          string `p:"wifiPlayRate" v:"wifiPlayRate@float#WiFi播放占比需为浮点数" dc:"WiFi播放占比"`                                                        //WiFi播放占比
	CardShow                              string `p:"cardShow" v:"cardShow@integer#3秒卡片展现数需为整数" dc:"3秒卡片展现数"`                                                                 //3秒卡片展现数
	DyLike                                string `p:"dyLike" v:"dyLike@integer#点赞数需为整数" dc:"点赞数"`                                                                             //点赞数
	DyComment                             string `p:"dyComment" v:"dyComment@integer#评论量需为整数" dc:"评论量"`                                                                       //评论量
	DyShare                               string `p:"dyShare" v:"dyShare@integer#分享量需为整数" dc:"分享量"`                                                                           //分享量
	IesChallengeClick                     string `p:"iesChallengeClick" v:"iesChallengeClick@integer#挑战赛查看数需为整数" dc:"挑战赛查看数"`                                                 //挑战赛查看数
	IesMusicClick                         string `p:"iesMusicClick" v:"iesMusicClick@integer#音乐查看数需为整数" dc:"音乐查看数"`                                                           //音乐查看数
	LocationClick                         string `p:"locationClick" v:"locationClick@integer#POI点击数需为整数" dc:"POI点击数"`                                                         //POI点击数
	CustomerEffective                     string `p:"customerEffective" v:"customerEffective@integer#有效获客需为整数" dc:"有效获客"`                                                     //有效获客
	Wechat                                string `p:"wechat" v:"wechat@integer#微信复制需为整数" dc:"微信复制"`                                                                           //微信复制
	AttributionMicroGame0DLtv             string `p:"attributionMicroGame0DLtv" v:"attributionMicroGame0DLtv@float#小程序/小游戏当日LTV需为浮点数" dc:"小程序/小游戏当日LTV"`                      //小程序/小游戏当日LTV
	AttributionMicroGame3DLtv             string `p:"attributionMicroGame3DLtv" v:"attributionMicroGame3DLtv@float#小程序/小游戏激活后三日LTV需为浮点数" dc:"小程序/小游戏激活后三日LTV"`                //小程序/小游戏激活后三日LTV
	AttributionMicroGame7DLtv             string `p:"attributionMicroGame7DLtv" v:"attributionMicroGame7DLtv@float#小程序/小游戏激活后七日LTV需为浮点数" dc:"小程序/小游戏激活后七日LTV"`                //小程序/小游戏激活后七日LTV
	AttributionMicroGame0DRoi             string `p:"attributionMicroGame0DRoi" v:"attributionMicroGame0DRoi@float#小程序/小游戏当日广告变现ROI需为浮点数" dc:"小程序/小游戏当日广告变现ROI"`              //小程序/小游戏当日广告变现ROI
	AttributionMicroGame3DRoi             string `p:"attributionMicroGame3DRoi" v:"attributionMicroGame3DRoi@float#小程序/小游戏激活后三日广告变现ROI需为浮点数" dc:"小程序/小游戏激活后三日广告变现ROI"`        //小程序/小游戏激活后三日广告变现ROI
	AttributionMicroGame7DRoi             string `p:"attributionMicroGame7DRoi" v:"attributionMicroGame7DRoi@float#小程序/小游戏激活后七日广告变现ROI需为浮点数" dc:"小程序/小游戏激活后七日广告变现ROI"`        //小程序/小游戏激活后七日广告变现ROI
	CreatedAt                             string `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"`                                                 //创建时间
	CreateDate                            string `p:"createDate" dc:"统计日期"`                                                                                                   //统计日期
}

// AdAdvertiserAccountMetricsDataSearchRes 列表返回结果
type AdAdvertiserAccountMetricsDataSearchRes struct {
	comModel.ListRes
	List []*AdAdvertiserAccountMetricsDataListRes `json:"list"`
}

type AdAdvertiserAccountReportDataSearchRes struct {
	comModel.ListRes
	List    []*AdAdvertiserAccountReportDataRes   `json:"list"`
	Summary *AdAdvertiserAccountReportDataSummary `json:"summary"`
}

type AdAdvertiserAccountReportDataSummary struct {
	TotalAdUp   float64 `orm:"total_ad_up" json:"totalAdUp" dc:"广告收入/IAA收入"`
	TotalAmount float64 `orm:"total_amount" json:"totalAmount" dc:"总充值金额"`
	Roi         float64 `json:"roi" dc:"ROI"`
	StatCost    float64 `json:"statCost" dc:"消耗"`
	//Cost                        float64 `json:"cost" dc:"总支出(单位元)"`
	ShowCnt            int64   `json:"showCnt" dc:"展示数"`
	Active             int64   `json:"active" dc:"激活数"`
	Register           int64   `orm:"register" json:"register" dc:"注册数"`
	ActiveRegisterCost float64 `json:"activeRegisterCost" dc:"注册成本"`
	ActiveCost         float64 `json:"activeCost" dc:"激活成本"`
	StatPayAmount      float64 `orm:"stat_pay_amount" json:"statPayAmount,omitempty"` // 付费金额（回传时间）
	PayAmountRoi       float64 `orm:"pay_amount_roi" json:"payAmountRoi,omitempty"`   // 付费ROI（回传时间）
	//AttributionGameInAppRoi1Day float64 `json:"attributionGameInAppRoi1Day" dc:"当日付费ROI"`
	//AttributionGameInAppLtv1Day float64 `json:"attributionGameInAppLtv1Day" dc:"付费金额"`
}

type AdAdvertiserAccountReportDataRes struct {
	TotalAdUp   float64 `orm:"total_ad_up" json:"totalAdUp" dc:"广告收入"`
	TotalAmount float64 `orm:"total_amount" json:"totalAmount" dc:"总充值金额"`
	Roi         float64 `json:"roi" dc:"ROI"`
	// -----------------账户信息--------------------
	AdvertiserNick string `orm:"advertiser_nick" json:"advertiserNick"` // 账户名称
	//Remark            string `orm:"remark" json:"remark"`                        // 备注
	Budget            float64 `orm:"budget" json:"budget" dc:"广告主预算"`             // 广告主预算
	BudgetMode        string  `orm:"budget_mode" json:"budgetMode" dc:"广告主预算"`    // 广告主预算Mode
	UserId            int     `orm:"user_id" json:"userId"`                       // 归属人员
	UserName          string  `orm:"user_name" json:"userName"`                   // 归属人员
	AuthStatus        int     `orm:"auth_status" json:"authStatus"`               // 授权状态 1：已授权 2：待授权
	AdvertiserCompany string  `orm:"advertiser_company" json:"advertiserCompany"` // 账户主体
	Balance           float64 `json:"balance" dc:"日终结余(单位元）"`
	// -----------------账户流水--------------------
	CashCost          float64 `json:"cashCost" dc:"现金支出(单位元)"`
	CompanyWalletCost float64 `json:"companyWalletCost" dc:"账户消耗的子钱包的共享余额（单位元）"`
	Cost              float64 `json:"cost" dc:"总支出(单位元)"`
	Frozen            float64 `json:"frozen" dc:"冻结(单位元)"`
	GrantBalance      float64 `json:"grantBalance" dc:"日终赠款结余(单位元）。包括冻结和实结订单pending部分"`
	Income            float64 `json:"income" dc:"总存入(单位元)"`
	NonGrantBalance   float64 `json:"nonGrantBalance" dc:"日终非赠款结余(单位元）。包括冻结和实结订单pending部分"`
	RewardCost        float64 `json:"rewardCost" dc:"赠款支出(单位元)"`
	SharedWalletCost  float64 `json:"sharedWalletCost" dc:"共享返货支出（单位元）"`
	TransferIn        float64 `json:"transferIn" dc:"总转入(单位元)"`
	TransferOut       float64 `json:"transferOut" dc:"总转出(单位元)"`
	// ---------------- 指标数据 --------------
	AdvertiserId                          string      `json:"advertiserId" dc:"账户ID"`
	StatCost                              float64     `json:"statCost" dc:"消耗"`
	ShowCnt                               int64       `json:"showCnt" dc:"展示数"`
	CpmPlatform                           float64     `json:"cpmPlatform" dc:"平均千次展现费用(元)"`
	ClickCnt                              int64       `json:"clickCnt" dc:"点击数"`
	Ctr                                   float64     `json:"ctr" dc:"点击率"`
	CpcPlatform                           float64     `json:"cpcPlatform" dc:"平均点击单价(元)"`
	ConvertCnt                            int64       `json:"convertCnt" dc:"转化数"`
	ConversionCost                        float64     `json:"conversionCost" dc:"平均转化成本"`
	ConversionRate                        float64     `json:"conversionRate" dc:"转化率"`
	DeepConvertCnt                        int64       `json:"deepConvertCnt" dc:"深度转化数"`
	DeepConvertCost                       float64     `json:"deepConvertCost" dc:"深度转化成本"`
	DeepConvertRate                       float64     `json:"deepConvertRate" dc:"深度转化率"`
	AttributionConvertCnt                 int64       `json:"attributionConvertCnt" dc:"转化数(计费时间)"`
	AttributionConvertCost                float64     `json:"attributionConvertCost" dc:"转化成本(计费时间)"`
	AttributionDeepConvertCnt             int64       `json:"attributionDeepConvertCnt" dc:"深度转化数(计费时间)"`
	AttributionDeepConvertCost            float64     `json:"attributionDeepConvertCost" dc:"深度转化成本(计费时间)"`
	PreConvertCount                       int64       `json:"preConvertCount" dc:"预估转化数(计费时间)"`
	PreConvertCost                        float64     `json:"preConvertCost" dc:"预估转化成本(计费时间)"`
	PreConvertRate                        float64     `json:"preConvertRate" dc:"预估转化率(计费时间)"`
	ClickStartCnt                         int64       `json:"clickStartCnt" dc:"安卓下载开始数"`
	ClickStartCost                        float64     `json:"clickStartCost" dc:"安卓下载开始成本"`
	ClickStartRate                        float64     `json:"clickStartRate" dc:"安卓下载开始率"`
	DownloadFinishCnt                     int64       `json:"downloadFinishCnt" dc:"安卓下载完成数"`
	DownloadFinishCost                    float64     `json:"downloadFinishCost" dc:"安卓下载完成成本"`
	DownloadFinishRate                    float64     `json:"downloadFinishRate" dc:"安卓下载完成率"`
	InstallFinishCnt                      int64       `json:"installFinishCnt" dc:"安卓安装完成数"`
	InstallFinishCost                     float64     `json:"installFinishCost" dc:"安卓安装完成成本"`
	InstallFinishRate                     float64     `json:"installFinishRate" dc:"安卓安装完成率"`
	Active                                int64       `json:"active" dc:"激活数"`
	ActiveCost                            float64     `json:"activeCost" dc:"激活成本"`
	ActiveRate                            float64     `json:"activeRate" dc:"激活率"`
	Register                              int64       `orm:"register" json:"register" dc:"注册数"`
	ActiveRegisterCost                    float64     `json:"activeRegisterCost" dc:"注册成本"`
	ActiveRegisterRate                    float64     `json:"activeRegisterRate" dc:"注册率"`
	GameAddiction                         int64       `json:"gameAddiction" dc:"关键行为数"`
	GameAddictionCost                     float64     `json:"gameAddictionCost" dc:"关键行为成本"`
	GameAddictionRate                     float64     `json:"gameAddictionRate" dc:"关键行为率"`
	AttributionNextDayOpenCnt             int64       `json:"attributionNextDayOpenCnt" dc:"次留数"`
	AttributionNextDayOpenCost            float64     `json:"attributionNextDayOpenCost" dc:"次留成本"`
	AttributionNextDayOpenRate            float64     `json:"attributionNextDayOpenRate" dc:"次留率"`
	NextDayOpen                           int64       `json:"nextDayOpen" dc:"次留回传数"`
	ActivePay                             int64       `json:"activePay" dc:"首次付费数"`
	ActivePayCost                         float64     `json:"activePayCost" dc:"首次付费成本"`
	ActivePayRate                         float64     `json:"activePayRate" dc:"首次付费率"`
	GamePayCount                          int64       `json:"gamePayCount" dc:"付费次数"`
	GamePayCost                           float64     `json:"gamePayCost" dc:"付费成本"`
	AttributionGamePay7DCount             int64       `json:"attributionGamePay7DCount" dc:"7日付费次数(激活时间)"`
	AttributionGamePay7DCost              float64     `json:"attributionGamePay7DCost" dc:"7日付费成本(激活时间)"`
	AttributionActivePay7DPerCount        int64       `json:"attributionActivePay7DPerCount" dc:"7日人均付费次数(激活时间)"`
	InAppUv                               int64       `json:"inAppUv" dc:"APP内访问"`
	InAppDetailUv                         int64       `json:"inAppDetailUv" dc:"APP内访问详情页"`
	InAppCart                             int64       `json:"inAppCart" dc:"APP内加入购物车"`
	InAppPay                              int64       `json:"inAppPay" dc:"APP内付费"`
	InAppOrder                            int64       `json:"inAppOrder" dc:"APP内下单"`
	StatPayAmount                         float64     `json:"statPayAmount" dc:"付费金额（回传时间）"`
	PayAmountRoi                          float64     `json:"payAmountRoi" dc:"付费ROI（回传时间）"`
	AttributionGameInAppLtv1Day           float64     `json:"attributionGameInAppLtv1Day" dc:"付费金额"`
	AttributionGameInAppLtv2Days          float64     `json:"attributionGameInAppLtv2Days" dc:"激活后一日付费金额"`
	AttributionGameInAppLtv3Days          float64     `json:"attributionGameInAppLtv3Days" dc:"激活后二日付费金额"`
	AttributionGameInAppLtv4Days          float64     `json:"attributionGameInAppLtv4Days" dc:"激活后三日付费金额"`
	AttributionGameInAppLtv5Days          float64     `json:"attributionGameInAppLtv5Days" dc:"激活后四日付费金额"`
	AttributionGameInAppLtv6Days          float64     `json:"attributionGameInAppLtv6Days" dc:"激活后五日付费金额"`
	AttributionGameInAppLtv7Days          float64     `json:"attributionGameInAppLtv7Days" dc:"激活后六日付费金额"`
	AttributionGameInAppLtv8Days          float64     `json:"attributionGameInAppLtv8Days" dc:"激活后七日付费金额"`
	AttributionGameInAppRoi1Day           float64     `json:"attributionGameInAppRoi1Day" dc:"付费ROI"`
	AttributionGameInAppRoi2Days          float64     `json:"attributionGameInAppRoi2Days" dc:"激活后一日付费ROI"`
	AttributionGameInAppRoi3Days          float64     `json:"attributionGameInAppRoi3Days" dc:"激活后二日付费ROI"`
	AttributionGameInAppRoi4Days          float64     `json:"attributionGameInAppRoi4Days" dc:"激活后三日付费ROI"`
	AttributionGameInAppRoi5Days          float64     `json:"attributionGameInAppRoi5Days" dc:"激活后四日付费ROI"`
	AttributionGameInAppRoi6Days          float64     `json:"attributionGameInAppRoi6Days" dc:"激活后五日付费ROI"`
	AttributionGameInAppRoi7Days          float64     `json:"attributionGameInAppRoi7Days" dc:"激活后六日付费ROI"`
	AttributionGameInAppRoi8Days          float64     `json:"attributionGameInAppRoi8Days" dc:"激活后七日付费ROI"`
	AttributionDayActivePayCount          int64       `json:"attributionDayActivePayCount" dc:"计费当日激活且首次付费数"`
	AttributionActivePayIntraOneDayCount  int64       `json:"attributionActivePayIntraOneDayCount" dc:"激活后24h首次付费数"`
	AttributionActivePayIntraOneDayCost   float64     `json:"attributionActivePayIntraOneDayCost" dc:"激活后24h首次付费成本"`
	AttributionActivePayIntraOneDayRate   float64     `json:"attributionActivePayIntraOneDayRate" dc:"激活后24h首次付费率"`
	AttributionActivePayIntraOneDayAmount float64     `json:"attributionActivePayIntraOneDayAmount" dc:"激活后24h付费金额"`
	AttributionActivePayIntraOneDayRoi    float64     `json:"attributionActivePayIntraOneDayRoi" dc:"激活后24h付费ROI"`
	AttributionRetention2DCnt             int64       `json:"attributionRetention2DCnt" dc:"2日留存数"`
	AttributionRetention2DCost            float64     `json:"attributionRetention2DCost" dc:"2日留存成本"`
	AttributionRetention2DRate            float64     `json:"attributionRetention2DRate" dc:"2日留存率"`
	AttributionRetention3DCnt             int64       `json:"attributionRetention3DCnt" dc:"3日留存数"`
	AttributionRetention3DCost            float64     `json:"attributionRetention3DCost" dc:"3日留存成本"`
	AttributionRetention3DRate            float64     `json:"attributionRetention3DRate" dc:"3日留存率"`
	AttributionRetention4DCnt             int64       `json:"attributionRetention4DCnt" dc:"4日留存数"`
	AttributionRetention4DCost            float64     `json:"attributionRetention4DCost" dc:"4日留存成本"`
	AttributionRetention4DRate            float64     `json:"attributionRetention4DRate" dc:"4日留存率"`
	AttributionRetention5DCnt             int64       `json:"attributionRetention5DCnt" dc:"5日留存数"`
	AttributionRetention5DCost            float64     `json:"attributionRetention5DCost" dc:"5日留存成本"`
	AttributionRetention5DRate            float64     `json:"attributionRetention5DRate" dc:"5日留存率"`
	AttributionRetention6DCnt             int64       `json:"attributionRetention6DCnt" dc:"6日留存数"`
	AttributionRetention6DCost            float64     `json:"attributionRetention6DCost" dc:"6日留存成本"`
	AttributionRetention6DRate            float64     `json:"attributionRetention6DRate" dc:"6日留存率"`
	AttributionRetention7DCnt             int64       `json:"attributionRetention7DCnt" dc:"7日留存数"`
	AttributionRetention7DCost            float64     `json:"attributionRetention7DCost" dc:"7日留存成本"`
	AttributionRetention7DRate            float64     `json:"attributionRetention7DRate" dc:"7日留存率"`
	AttributionRetention7DSumCnt          int64       `json:"attributionRetention7DSumCnt" dc:"7日留存总数"`
	AttributionRetention7DTotalCost       float64     `json:"attributionRetention7DTotalCost" dc:"7日留存总成本"`
	TotalPlay                             int64       `json:"totalPlay" dc:"播放量"`
	ValidPlay                             int64       `json:"validPlay" dc:"有效播放数"`
	ValidPlayCost                         float64     `json:"validPlayCost" dc:"有效播放成本"`
	ValidPlayRate                         float64     `json:"validPlayRate" dc:"有效播放率"`
	Play25FeedBreak                       int64       `json:"play25FeedBreak" dc:"25%进度播放数"`
	Play50FeedBreak                       int64       `json:"play50FeedBreak" dc:"50%进度播放数"`
	Play75FeedBreak                       int64       `json:"play75FeedBreak" dc:"75%进度播放数"`
	Play99FeedBreak                       int64       `json:"play99FeedBreak" dc:"99%进度播放数"`
	AveragePlayTimePerPlay                float64     `json:"averagePlayTimePerPlay" dc:"平均单次播放时长"`
	PlayOverRate                          float64     `json:"playOverRate" dc:"完播率"`
	WifiPlayRate                          float64     `json:"wifiPlayRate" dc:"WiFi播放占比"`
	CardShow                              int64       `json:"cardShow" dc:"3秒卡片展现数"`
	DyLike                                int64       `json:"dyLike" dc:"点赞数"`
	DyComment                             int64       `json:"dyComment" dc:"评论量"`
	DyShare                               int64       `json:"dyShare" dc:"分享量"`
	IesChallengeClick                     int64       `json:"iesChallengeClick" dc:"挑战赛查看数"`
	IesMusicClick                         int64       `json:"iesMusicClick" dc:"音乐查看数"`
	LocationClick                         int64       `json:"locationClick" dc:"POI点击数"`
	CustomerEffective                     int64       `json:"customerEffective" dc:"有效获客"`
	Wechat                                int64       `json:"wechat" dc:"微信复制"`
	AttributionMicroGame0DLtv             float64     `json:"attributionMicroGame0DLtv" dc:"小程序/小游戏当日LTV"`
	AttributionMicroGame3DLtv             float64     `json:"attributionMicroGame3DLtv" dc:"小程序/小游戏激活后三日LTV"`
	AttributionMicroGame7DLtv             float64     `json:"attributionMicroGame7DLtv" dc:"小程序/小游戏激活后七日LTV"`
	AttributionMicroGame0DRoi             float64     `json:"attributionMicroGame0DRoi" dc:"小程序/小游戏当日广告变现ROI"`
	AttributionMicroGame3DRoi             float64     `json:"attributionMicroGame3DRoi" dc:"小程序/小游戏激活后三日广告变现ROI"`
	AttributionMicroGame7DRoi             float64     `json:"attributionMicroGame7DRoi" dc:"小程序/小游戏激活后七日广告变现ROI"`
	CreatedAt                             *gtime.Time `json:"createdAt" dc:"创建时间"`
	CreateDate                            string      `json:"createDate" dc:"统计日期"`
}

// AdAdvertiserAccountMetricsDataAddReq 添加操作请求参数
type AdAdvertiserAccountMetricsDataAddReq struct {
	AdvertiserId                          string  `p:"advertiserId"  dc:"账户ID"`
	StatCost                              float64 `p:"statCost"  dc:"消耗"`
	ShowCnt                               int64   `p:"showCnt"  dc:"展示数"`
	CpmPlatform                           float64 `p:"cpmPlatform"  dc:"平均千次展现费用(元)"`
	ClickCnt                              int64   `p:"clickCnt"  dc:"点击数"`
	Ctr                                   float64 `p:"ctr"  dc:"点击率"`
	CpcPlatform                           float64 `p:"cpcPlatform"  dc:"平均点击单价(元)"`
	ConvertCnt                            int64   `p:"convertCnt"  dc:"转化数"`
	ConversionCost                        float64 `p:"conversionCost"  dc:"平均转化成本"`
	ConversionRate                        float64 `p:"conversionRate"  dc:"转化率"`
	DeepConvertCnt                        int64   `p:"deepConvertCnt"  dc:"深度转化数"`
	DeepConvertCost                       float64 `p:"deepConvertCost"  dc:"深度转化成本"`
	DeepConvertRate                       float64 `p:"deepConvertRate"  dc:"深度转化率"`
	AttributionConvertCnt                 int64   `p:"attributionConvertCnt"  dc:"转化数(计费时间)"`
	AttributionConvertCost                float64 `p:"attributionConvertCost"  dc:"转化成本(计费时间)"`
	AttributionDeepConvertCnt             int64   `p:"attributionDeepConvertCnt"  dc:"深度转化数(计费时间)"`
	AttributionDeepConvertCost            float64 `p:"attributionDeepConvertCost"  dc:"深度转化成本(计费时间)"`
	PreConvertCount                       int64   `p:"preConvertCount"  dc:"预估转化数(计费时间)"`
	PreConvertCost                        float64 `p:"preConvertCost"  dc:"预估转化成本(计费时间)"`
	PreConvertRate                        float64 `p:"preConvertRate"  dc:"预估转化率(计费时间)"`
	ClickStartCnt                         int64   `p:"clickStartCnt"  dc:"安卓下载开始数"`
	ClickStartCost                        float64 `p:"clickStartCost"  dc:"安卓下载开始成本"`
	ClickStartRate                        float64 `p:"clickStartRate"  dc:"安卓下载开始率"`
	DownloadFinishCnt                     int64   `p:"downloadFinishCnt"  dc:"安卓下载完成数"`
	DownloadFinishCost                    float64 `p:"downloadFinishCost"  dc:"安卓下载完成成本"`
	DownloadFinishRate                    float64 `p:"downloadFinishRate"  dc:"安卓下载完成率"`
	InstallFinishCnt                      int64   `p:"installFinishCnt"  dc:"安卓安装完成数"`
	InstallFinishCost                     float64 `p:"installFinishCost"  dc:"安卓安装完成成本"`
	InstallFinishRate                     float64 `p:"installFinishRate"  dc:"安卓安装完成率"`
	Active                                int64   `p:"active"  dc:"激活数"`
	ActiveCost                            float64 `p:"activeCost"  dc:"激活成本"`
	ActiveRate                            float64 `p:"activeRate"  dc:"激活率"`
	ActiveRegisterCost                    float64 `p:"activeRegisterCost"  dc:"注册成本"`
	ActiveRegisterRate                    float64 `p:"activeRegisterRate"  dc:"注册率"`
	GameAddiction                         int64   `p:"gameAddiction"  dc:"关键行为数"`
	GameAddictionCost                     float64 `p:"gameAddictionCost"  dc:"关键行为成本"`
	GameAddictionRate                     float64 `p:"gameAddictionRate"  dc:"关键行为率"`
	AttributionNextDayOpenCnt             int64   `p:"attributionNextDayOpenCnt"  dc:"次留数"`
	AttributionNextDayOpenCost            float64 `p:"attributionNextDayOpenCost"  dc:"次留成本"`
	AttributionNextDayOpenRate            float64 `p:"attributionNextDayOpenRate"  dc:"次留率"`
	NextDayOpen                           int64   `p:"nextDayOpen"  dc:"次留回传数"`
	ActivePay                             int64   `p:"activePay"  dc:"首次付费数"`
	ActivePayCost                         float64 `p:"activePayCost"  dc:"首次付费成本"`
	ActivePayRate                         float64 `p:"activePayRate"  dc:"首次付费率"`
	GamePayCount                          int64   `p:"gamePayCount"  dc:"付费次数"`
	GamePayCost                           float64 `p:"gamePayCost"  dc:"付费成本"`
	AttributionGamePay7DCount             int64   `p:"attributionGamePay7DCount"  dc:"7日付费次数(激活时间)"`
	AttributionGamePay7DCost              float64 `p:"attributionGamePay7DCost"  dc:"7日付费成本(激活时间)"`
	AttributionActivePay7DPerCount        int64   `p:"attributionActivePay7DPerCount"  dc:"7日人均付费次数(激活时间)"`
	InAppUv                               int64   `p:"inAppUv"  dc:"APP内访问"`
	InAppDetailUv                         int64   `p:"inAppDetailUv"  dc:"APP内访问详情页"`
	InAppCart                             int64   `p:"inAppCart"  dc:"APP内加入购物车"`
	InAppPay                              int64   `p:"inAppPay"  dc:"APP内付费"`
	InAppOrder                            int64   `p:"inAppOrder"  dc:"APP内下单"`
	AttributionGameInAppLtv1Day           float64 `p:"attributionGameInAppLtv1Day"  dc:"当日付费金额"`
	AttributionGameInAppLtv2Days          float64 `p:"attributionGameInAppLtv2Days"  dc:"激活后一日付费金额"`
	AttributionGameInAppLtv3Days          float64 `p:"attributionGameInAppLtv3Days"  dc:"激活后二日付费金额"`
	AttributionGameInAppLtv4Days          float64 `p:"attributionGameInAppLtv4Days"  dc:"激活后三日付费金额"`
	AttributionGameInAppLtv5Days          float64 `p:"attributionGameInAppLtv5Days"  dc:"激活后四日付费金额"`
	AttributionGameInAppLtv6Days          float64 `p:"attributionGameInAppLtv6Days"  dc:"激活后五日付费金额"`
	AttributionGameInAppLtv7Days          float64 `p:"attributionGameInAppLtv7Days"  dc:"激活后六日付费金额"`
	AttributionGameInAppLtv8Days          float64 `p:"attributionGameInAppLtv8Days"  dc:"激活后七日付费金额"`
	AttributionGameInAppRoi1Day           float64 `p:"attributionGameInAppRoi1Day"  dc:"当日付费ROI"`
	AttributionGameInAppRoi2Days          float64 `p:"attributionGameInAppRoi2Days"  dc:"激活后一日付费ROI"`
	AttributionGameInAppRoi3Days          float64 `p:"attributionGameInAppRoi3Days"  dc:"激活后二日付费ROI"`
	AttributionGameInAppRoi4Days          float64 `p:"attributionGameInAppRoi4Days"  dc:"激活后三日付费ROI"`
	AttributionGameInAppRoi5Days          float64 `p:"attributionGameInAppRoi5Days"  dc:"激活后四日付费ROI"`
	AttributionGameInAppRoi6Days          float64 `p:"attributionGameInAppRoi6Days"  dc:"激活后五日付费ROI"`
	AttributionGameInAppRoi7Days          float64 `p:"attributionGameInAppRoi7Days"  dc:"激活后六日付费ROI"`
	AttributionGameInAppRoi8Days          float64 `p:"attributionGameInAppRoi8Days"  dc:"激活后七日付费ROI"`
	AttributionDayActivePayCount          int64   `p:"attributionDayActivePayCount"  dc:"计费当日激活且首次付费数"`
	AttributionActivePayIntraOneDayCount  int64   `p:"attributionActivePayIntraOneDayCount"  dc:"激活后24h首次付费数"`
	AttributionActivePayIntraOneDayCost   float64 `p:"attributionActivePayIntraOneDayCost"  dc:"激活后24h首次付费成本"`
	AttributionActivePayIntraOneDayRate   float64 `p:"attributionActivePayIntraOneDayRate"  dc:"激活后24h首次付费率"`
	AttributionActivePayIntraOneDayAmount float64 `p:"attributionActivePayIntraOneDayAmount"  dc:"激活后24h付费金额"`
	AttributionActivePayIntraOneDayRoi    float64 `p:"attributionActivePayIntraOneDayRoi"  dc:"激活后24h付费ROI"`
	AttributionRetention2DCnt             int64   `p:"attributionRetention2DCnt"  dc:"2日留存数"`
	AttributionRetention2DCost            float64 `p:"attributionRetention2DCost"  dc:"2日留存成本"`
	AttributionRetention2DRate            float64 `p:"attributionRetention2DRate"  dc:"2日留存率"`
	AttributionRetention3DCnt             int64   `p:"attributionRetention3DCnt"  dc:"3日留存数"`
	AttributionRetention3DCost            float64 `p:"attributionRetention3DCost"  dc:"3日留存成本"`
	AttributionRetention3DRate            float64 `p:"attributionRetention3DRate"  dc:"3日留存率"`
	AttributionRetention4DCnt             int64   `p:"attributionRetention4DCnt"  dc:"4日留存数"`
	AttributionRetention4DCost            float64 `p:"attributionRetention4DCost"  dc:"4日留存成本"`
	AttributionRetention4DRate            float64 `p:"attributionRetention4DRate"  dc:"4日留存率"`
	AttributionRetention5DCnt             int64   `p:"attributionRetention5DCnt"  dc:"5日留存数"`
	AttributionRetention5DCost            float64 `p:"attributionRetention5DCost"  dc:"5日留存成本"`
	AttributionRetention5DRate            float64 `p:"attributionRetention5DRate"  dc:"5日留存率"`
	AttributionRetention6DCnt             int64   `p:"attributionRetention6DCnt"  dc:"6日留存数"`
	AttributionRetention6DCost            float64 `p:"attributionRetention6DCost"  dc:"6日留存成本"`
	AttributionRetention6DRate            float64 `p:"attributionRetention6DRate"  dc:"6日留存率"`
	AttributionRetention7DCnt             int64   `p:"attributionRetention7DCnt"  dc:"7日留存数"`
	AttributionRetention7DCost            float64 `p:"attributionRetention7DCost"  dc:"7日留存成本"`
	AttributionRetention7DRate            float64 `p:"attributionRetention7DRate"  dc:"7日留存率"`
	AttributionRetention7DSumCnt          int64   `p:"attributionRetention7DSumCnt"  dc:"7日留存总数"`
	AttributionRetention7DTotalCost       float64 `p:"attributionRetention7DTotalCost"  dc:"7日留存总成本"`
	TotalPlay                             int64   `p:"totalPlay"  dc:"播放量"`
	ValidPlay                             int64   `p:"validPlay"  dc:"有效播放数"`
	ValidPlayCost                         float64 `p:"validPlayCost"  dc:"有效播放成本"`
	ValidPlayRate                         float64 `p:"validPlayRate"  dc:"有效播放率"`
	Play25FeedBreak                       int64   `p:"play25FeedBreak"  dc:"25%进度播放数"`
	Play50FeedBreak                       int64   `p:"play50FeedBreak"  dc:"50%进度播放数"`
	Play75FeedBreak                       int64   `p:"play75FeedBreak"  dc:"75%进度播放数"`
	Play99FeedBreak                       int64   `p:"play99FeedBreak"  dc:"99%进度播放数"`
	AveragePlayTimePerPlay                float64 `p:"averagePlayTimePerPlay"  dc:"平均单次播放时长"`
	PlayOverRate                          float64 `p:"playOverRate"  dc:"完播率"`
	WifiPlayRate                          float64 `p:"wifiPlayRate"  dc:"WiFi播放占比"`
	CardShow                              int64   `p:"cardShow"  dc:"3秒卡片展现数"`
	DyLike                                int64   `p:"dyLike"  dc:"点赞数"`
	DyComment                             int64   `p:"dyComment"  dc:"评论量"`
	DyShare                               int64   `p:"dyShare"  dc:"分享量"`
	IesChallengeClick                     int64   `p:"iesChallengeClick"  dc:"挑战赛查看数"`
	IesMusicClick                         int64   `p:"iesMusicClick"  dc:"音乐查看数"`
	LocationClick                         int64   `p:"locationClick"  dc:"POI点击数"`
	CustomerEffective                     int64   `p:"customerEffective"  dc:"有效获客"`
	Wechat                                int64   `p:"wechat"  dc:"微信复制"`
	AttributionMicroGame0DLtv             float64 `p:"attributionMicroGame0DLtv"  dc:"小程序/小游戏当日LTV"`
	AttributionMicroGame3DLtv             float64 `p:"attributionMicroGame3DLtv"  dc:"小程序/小游戏激活后三日LTV"`
	AttributionMicroGame7DLtv             float64 `p:"attributionMicroGame7DLtv"  dc:"小程序/小游戏激活后七日LTV"`
	AttributionMicroGame0DRoi             float64 `p:"attributionMicroGame0DRoi"  dc:"小程序/小游戏当日广告变现ROI"`
	AttributionMicroGame3DRoi             float64 `p:"attributionMicroGame3DRoi"  dc:"小程序/小游戏激活后三日广告变现ROI"`
	AttributionMicroGame7DRoi             float64 `p:"attributionMicroGame7DRoi"  dc:"小程序/小游戏激活后七日广告变现ROI"`
	CreateDate                            string  `p:"createDate"  dc:"统计日期"`
}

// AdAdvertiserAccountMetricsDataEditReq 修改操作请求参数
type AdAdvertiserAccountMetricsDataEditReq struct {
	Id                                    int     `p:"id" v:"required#主键ID不能为空" dc:"自增id主键"`
	AdvertiserId                          string  `p:"advertiserId"  dc:"账户ID"`
	StatCost                              float64 `p:"statCost"  dc:"消耗"`
	ShowCnt                               int64   `p:"showCnt"  dc:"展示数"`
	CpmPlatform                           float64 `p:"cpmPlatform"  dc:"平均千次展现费用(元)"`
	ClickCnt                              int64   `p:"clickCnt"  dc:"点击数"`
	Ctr                                   float64 `p:"ctr"  dc:"点击率"`
	CpcPlatform                           float64 `p:"cpcPlatform"  dc:"平均点击单价(元)"`
	ConvertCnt                            int64   `p:"convertCnt"  dc:"转化数"`
	ConversionCost                        float64 `p:"conversionCost"  dc:"平均转化成本"`
	ConversionRate                        float64 `p:"conversionRate"  dc:"转化率"`
	DeepConvertCnt                        int64   `p:"deepConvertCnt"  dc:"深度转化数"`
	DeepConvertCost                       float64 `p:"deepConvertCost"  dc:"深度转化成本"`
	DeepConvertRate                       float64 `p:"deepConvertRate"  dc:"深度转化率"`
	AttributionConvertCnt                 int64   `p:"attributionConvertCnt"  dc:"转化数(计费时间)"`
	AttributionConvertCost                float64 `p:"attributionConvertCost"  dc:"转化成本(计费时间)"`
	AttributionDeepConvertCnt             int64   `p:"attributionDeepConvertCnt"  dc:"深度转化数(计费时间)"`
	AttributionDeepConvertCost            float64 `p:"attributionDeepConvertCost"  dc:"深度转化成本(计费时间)"`
	PreConvertCount                       int64   `p:"preConvertCount"  dc:"预估转化数(计费时间)"`
	PreConvertCost                        float64 `p:"preConvertCost"  dc:"预估转化成本(计费时间)"`
	PreConvertRate                        float64 `p:"preConvertRate"  dc:"预估转化率(计费时间)"`
	ClickStartCnt                         int64   `p:"clickStartCnt"  dc:"安卓下载开始数"`
	ClickStartCost                        float64 `p:"clickStartCost"  dc:"安卓下载开始成本"`
	ClickStartRate                        float64 `p:"clickStartRate"  dc:"安卓下载开始率"`
	DownloadFinishCnt                     int64   `p:"downloadFinishCnt"  dc:"安卓下载完成数"`
	DownloadFinishCost                    float64 `p:"downloadFinishCost"  dc:"安卓下载完成成本"`
	DownloadFinishRate                    float64 `p:"downloadFinishRate"  dc:"安卓下载完成率"`
	InstallFinishCnt                      int64   `p:"installFinishCnt"  dc:"安卓安装完成数"`
	InstallFinishCost                     float64 `p:"installFinishCost"  dc:"安卓安装完成成本"`
	InstallFinishRate                     float64 `p:"installFinishRate"  dc:"安卓安装完成率"`
	Active                                int64   `p:"active"  dc:"激活数"`
	ActiveCost                            float64 `p:"activeCost"  dc:"激活成本"`
	ActiveRate                            float64 `p:"activeRate"  dc:"激活率"`
	ActiveRegisterCost                    float64 `p:"activeRegisterCost"  dc:"注册成本"`
	ActiveRegisterRate                    float64 `p:"activeRegisterRate"  dc:"注册率"`
	GameAddiction                         int64   `p:"gameAddiction"  dc:"关键行为数"`
	GameAddictionCost                     float64 `p:"gameAddictionCost"  dc:"关键行为成本"`
	GameAddictionRate                     float64 `p:"gameAddictionRate"  dc:"关键行为率"`
	AttributionNextDayOpenCnt             int64   `p:"attributionNextDayOpenCnt"  dc:"次留数"`
	AttributionNextDayOpenCost            float64 `p:"attributionNextDayOpenCost"  dc:"次留成本"`
	AttributionNextDayOpenRate            float64 `p:"attributionNextDayOpenRate"  dc:"次留率"`
	NextDayOpen                           int64   `p:"nextDayOpen"  dc:"次留回传数"`
	ActivePay                             int64   `p:"activePay"  dc:"首次付费数"`
	ActivePayCost                         float64 `p:"activePayCost"  dc:"首次付费成本"`
	ActivePayRate                         float64 `p:"activePayRate"  dc:"首次付费率"`
	GamePayCount                          int64   `p:"gamePayCount"  dc:"付费次数"`
	GamePayCost                           float64 `p:"gamePayCost"  dc:"付费成本"`
	AttributionGamePay7DCount             int64   `p:"attributionGamePay7DCount"  dc:"7日付费次数(激活时间)"`
	AttributionGamePay7DCost              float64 `p:"attributionGamePay7DCost"  dc:"7日付费成本(激活时间)"`
	AttributionActivePay7DPerCount        int64   `p:"attributionActivePay7DPerCount"  dc:"7日人均付费次数(激活时间)"`
	InAppUv                               int64   `p:"inAppUv"  dc:"APP内访问"`
	InAppDetailUv                         int64   `p:"inAppDetailUv"  dc:"APP内访问详情页"`
	InAppCart                             int64   `p:"inAppCart"  dc:"APP内加入购物车"`
	InAppPay                              int64   `p:"inAppPay"  dc:"APP内付费"`
	InAppOrder                            int64   `p:"inAppOrder"  dc:"APP内下单"`
	AttributionGameInAppLtv1Day           float64 `p:"attributionGameInAppLtv1Day"  dc:"当日付费金额"`
	AttributionGameInAppLtv2Days          float64 `p:"attributionGameInAppLtv2Days"  dc:"激活后一日付费金额"`
	AttributionGameInAppLtv3Days          float64 `p:"attributionGameInAppLtv3Days"  dc:"激活后二日付费金额"`
	AttributionGameInAppLtv4Days          float64 `p:"attributionGameInAppLtv4Days"  dc:"激活后三日付费金额"`
	AttributionGameInAppLtv5Days          float64 `p:"attributionGameInAppLtv5Days"  dc:"激活后四日付费金额"`
	AttributionGameInAppLtv6Days          float64 `p:"attributionGameInAppLtv6Days"  dc:"激活后五日付费金额"`
	AttributionGameInAppLtv7Days          float64 `p:"attributionGameInAppLtv7Days"  dc:"激活后六日付费金额"`
	AttributionGameInAppLtv8Days          float64 `p:"attributionGameInAppLtv8Days"  dc:"激活后七日付费金额"`
	AttributionGameInAppRoi1Day           float64 `p:"attributionGameInAppRoi1Day"  dc:"当日付费ROI"`
	AttributionGameInAppRoi2Days          float64 `p:"attributionGameInAppRoi2Days"  dc:"激活后一日付费ROI"`
	AttributionGameInAppRoi3Days          float64 `p:"attributionGameInAppRoi3Days"  dc:"激活后二日付费ROI"`
	AttributionGameInAppRoi4Days          float64 `p:"attributionGameInAppRoi4Days"  dc:"激活后三日付费ROI"`
	AttributionGameInAppRoi5Days          float64 `p:"attributionGameInAppRoi5Days"  dc:"激活后四日付费ROI"`
	AttributionGameInAppRoi6Days          float64 `p:"attributionGameInAppRoi6Days"  dc:"激活后五日付费ROI"`
	AttributionGameInAppRoi7Days          float64 `p:"attributionGameInAppRoi7Days"  dc:"激活后六日付费ROI"`
	AttributionGameInAppRoi8Days          float64 `p:"attributionGameInAppRoi8Days"  dc:"激活后七日付费ROI"`
	AttributionDayActivePayCount          int64   `p:"attributionDayActivePayCount"  dc:"计费当日激活且首次付费数"`
	AttributionActivePayIntraOneDayCount  int64   `p:"attributionActivePayIntraOneDayCount"  dc:"激活后24h首次付费数"`
	AttributionActivePayIntraOneDayCost   float64 `p:"attributionActivePayIntraOneDayCost"  dc:"激活后24h首次付费成本"`
	AttributionActivePayIntraOneDayRate   float64 `p:"attributionActivePayIntraOneDayRate"  dc:"激活后24h首次付费率"`
	AttributionActivePayIntraOneDayAmount float64 `p:"attributionActivePayIntraOneDayAmount"  dc:"激活后24h付费金额"`
	AttributionActivePayIntraOneDayRoi    float64 `p:"attributionActivePayIntraOneDayRoi"  dc:"激活后24h付费ROI"`
	AttributionRetention2DCnt             int64   `p:"attributionRetention2DCnt"  dc:"2日留存数"`
	AttributionRetention2DCost            float64 `p:"attributionRetention2DCost"  dc:"2日留存成本"`
	AttributionRetention2DRate            float64 `p:"attributionRetention2DRate"  dc:"2日留存率"`
	AttributionRetention3DCnt             int64   `p:"attributionRetention3DCnt"  dc:"3日留存数"`
	AttributionRetention3DCost            float64 `p:"attributionRetention3DCost"  dc:"3日留存成本"`
	AttributionRetention3DRate            float64 `p:"attributionRetention3DRate"  dc:"3日留存率"`
	AttributionRetention4DCnt             int64   `p:"attributionRetention4DCnt"  dc:"4日留存数"`
	AttributionRetention4DCost            float64 `p:"attributionRetention4DCost"  dc:"4日留存成本"`
	AttributionRetention4DRate            float64 `p:"attributionRetention4DRate"  dc:"4日留存率"`
	AttributionRetention5DCnt             int64   `p:"attributionRetention5DCnt"  dc:"5日留存数"`
	AttributionRetention5DCost            float64 `p:"attributionRetention5DCost"  dc:"5日留存成本"`
	AttributionRetention5DRate            float64 `p:"attributionRetention5DRate"  dc:"5日留存率"`
	AttributionRetention6DCnt             int64   `p:"attributionRetention6DCnt"  dc:"6日留存数"`
	AttributionRetention6DCost            float64 `p:"attributionRetention6DCost"  dc:"6日留存成本"`
	AttributionRetention6DRate            float64 `p:"attributionRetention6DRate"  dc:"6日留存率"`
	AttributionRetention7DCnt             int64   `p:"attributionRetention7DCnt"  dc:"7日留存数"`
	AttributionRetention7DCost            float64 `p:"attributionRetention7DCost"  dc:"7日留存成本"`
	AttributionRetention7DRate            float64 `p:"attributionRetention7DRate"  dc:"7日留存率"`
	AttributionRetention7DSumCnt          int64   `p:"attributionRetention7DSumCnt"  dc:"7日留存总数"`
	AttributionRetention7DTotalCost       float64 `p:"attributionRetention7DTotalCost"  dc:"7日留存总成本"`
	TotalPlay                             int64   `p:"totalPlay"  dc:"播放量"`
	ValidPlay                             int64   `p:"validPlay"  dc:"有效播放数"`
	ValidPlayCost                         float64 `p:"validPlayCost"  dc:"有效播放成本"`
	ValidPlayRate                         float64 `p:"validPlayRate"  dc:"有效播放率"`
	Play25FeedBreak                       int64   `p:"play25FeedBreak"  dc:"25%进度播放数"`
	Play50FeedBreak                       int64   `p:"play50FeedBreak"  dc:"50%进度播放数"`
	Play75FeedBreak                       int64   `p:"play75FeedBreak"  dc:"75%进度播放数"`
	Play99FeedBreak                       int64   `p:"play99FeedBreak"  dc:"99%进度播放数"`
	AveragePlayTimePerPlay                float64 `p:"averagePlayTimePerPlay"  dc:"平均单次播放时长"`
	PlayOverRate                          float64 `p:"playOverRate"  dc:"完播率"`
	WifiPlayRate                          float64 `p:"wifiPlayRate"  dc:"WiFi播放占比"`
	CardShow                              int64   `p:"cardShow"  dc:"3秒卡片展现数"`
	DyLike                                int64   `p:"dyLike"  dc:"点赞数"`
	DyComment                             int64   `p:"dyComment"  dc:"评论量"`
	DyShare                               int64   `p:"dyShare"  dc:"分享量"`
	IesChallengeClick                     int64   `p:"iesChallengeClick"  dc:"挑战赛查看数"`
	IesMusicClick                         int64   `p:"iesMusicClick"  dc:"音乐查看数"`
	LocationClick                         int64   `p:"locationClick"  dc:"POI点击数"`
	CustomerEffective                     int64   `p:"customerEffective"  dc:"有效获客"`
	Wechat                                int64   `p:"wechat"  dc:"微信复制"`
	AttributionMicroGame0DLtv             float64 `p:"attributionMicroGame0DLtv"  dc:"小程序/小游戏当日LTV"`
	AttributionMicroGame3DLtv             float64 `p:"attributionMicroGame3DLtv"  dc:"小程序/小游戏激活后三日LTV"`
	AttributionMicroGame7DLtv             float64 `p:"attributionMicroGame7DLtv"  dc:"小程序/小游戏激活后七日LTV"`
	AttributionMicroGame0DRoi             float64 `p:"attributionMicroGame0DRoi"  dc:"小程序/小游戏当日广告变现ROI"`
	AttributionMicroGame3DRoi             float64 `p:"attributionMicroGame3DRoi"  dc:"小程序/小游戏激活后三日广告变现ROI"`
	AttributionMicroGame7DRoi             float64 `p:"attributionMicroGame7DRoi"  dc:"小程序/小游戏激活后七日广告变现ROI"`
	CreateDate                            string  `p:"createDate"  dc:"统计日期"`
}
type AdPromotionIdInfo struct {
	PromotionId string `orm:"promotion_id" json:"promotion_id" dc:"项目ID"`
}

type AccountSubjectDataStatisticsReq struct {
	comModel.PageReq
	StartTime           string   `p:"startTime" dc:"开始时间格式 yyyy-MM-dd"`
	EndTime             string   `p:"endTime" dc:"结束时间格式 yyyy-MM-dd"`
	AdvertiserCompanies []string `p:"advertiserCompanies"  dc:"账户主体列表"`
	UserIds             []int    `p:"userIds"  dc:"归属人员ID列表"`
	Merge               int      `p:"merge"  dc:"是否合并 0: 否 1: 是"`
}

type AccountSubjectDataStatisticsRes struct {
	comModel.ListRes
	List    []*AccountSubjectDataStatisticsData `json:"list"`
	Summary *AccountSubjectDataStatisticsData   `json:"summary"`
}

type AccountSubjectDataStatisticsData struct {
	CreateDate                  string  `json:"createDate" dc:"统计日期"`
	AdvertiserCompany           string  `json:"advertiserCompany" dc:"账户主体"`
	UserId                      int     `json:"userId" dc:"归属人员ID"`
	UserName                    string  `json:"userName" dc:"归属人员"`
	TotalAccounts               int     `json:"totalAccounts" dc:"总账户数"`
	ActiveAccounts              int     `json:"activeAccounts" dc:"在投账户数"`
	StatCost                    float64 `json:"statCost" dc:"消耗"`
	StatPayAmount               float64 `json:"statPayAmount" dc:"付费金额（回传时间）"`
	PayAmountRoi                float64 `json:"payAmountRoi" dc:"付费ROI（回传时间）"`
	Active                      int64   `json:"active" dc:"激活数"`
	ActiveRate                  float64 `json:"activeRate" dc:"激活率"`
	ConvertCnt                  int64   `json:"convertCnt" dc:"转化数"`
	ConversionRate              float64 `json:"conversionRate" dc:"转化率"`
	AttributionGameInAppLtv1Day float64 `json:"attributionGameInAppLtv1Day" dc:"付费金额（激活用户当日付费金额）"`
	AttributionGameInAppRoi1Day float64 `json:"attributionGameInAppRoi1Day" dc:"付费ROI（激活用户当日付费RIO）"`
	AttributionMicroGame0DLtv   float64 `json:"attributionMicroGame0DLtv" dc:"小程序/小游戏当日LTV（激活用户当日LTV）"`
	AttributionMicroGame0DRoi   float64 `json:"attributionMicroGame0DRoi" dc:"小程序/小游戏当日广告变现ROI（激活用户当日广告变现ROI）"`
}
