// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-11-15 16:27:19
// 生成路径: internal/app/oceanengine/controller/ad_advertiser_account_metrics_data.go
// 生成人：cyao
// desc:广告账户的指标数据
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"
	"github.com/tiger1103/gfast/v3/api/v1/oceanengine"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type adAdvertiserAccountMetricsDataController struct {
	systemController.BaseController
}

var AdAdvertiserAccountMetricsData = new(adAdvertiserAccountMetricsDataController)

// List 列表
func (c *adAdvertiserAccountMetricsDataController) List(ctx context.Context, req *oceanengine.AdAdvertiserAccountMetricsDataSearchReq) (res *oceanengine.AdAdvertiserAccountMetricsDataSearchRes, err error) {
	res = new(oceanengine.AdAdvertiserAccountMetricsDataSearchRes)
	res.AdAdvertiserAccountMetricsDataSearchRes, err = service.AdAdvertiserAccountMetricsData().List(ctx, &req.AdAdvertiserAccountMetricsDataSearchReq)
	return
}

func (c *adAdvertiserAccountMetricsDataController) AdAdvertiserAccountReportDataSearch(ctx context.Context, req *oceanengine.AdAdvertiserAccountReportDataSearchReq) (res *oceanengine.AdAdvertiserAccountReportDataSearchRes, err error) {
	res = new(oceanengine.AdAdvertiserAccountReportDataSearchRes)
	res.AdAdvertiserAccountReportDataSearchRes, err = service.AdAdvertiserAccountMetricsData().AdAdvertiserAccountReportDataSearch(ctx, &req.AdAdvertiserAccountReportDataSearch)
	return
}

func (c *adAdvertiserAccountMetricsDataController) AdAdvertiserAccountReportStatTask(ctx context.Context, req *oceanengine.AdAdvertiserAccountReportStatTaskReq) (res *oceanengine.AdAdvertiserAccountReportStatTaskRes, err error) {
	err = service.AdAdvertiserAccountMetricsData().AdAdvertiserAccountReportStatTask(ctx, req.StartTime, req.EndTime)
	return
}

func (c *adAdvertiserAccountMetricsDataController) AdAdvertiserAccountReportSubTask(ctx context.Context, req *oceanengine.AdAdvertiserAccountReportSubTaskReq) (res *oceanengine.AdAdvertiserAccountReportSubTaskRes, err error) {
	err = service.AdAdvertiserAccountMetricsData().AdAdvertiserAccountReportSubTask(ctx, req.AdId, req.StatTime)
	return
}

// Get 获取广告账户的指标数据
func (c *adAdvertiserAccountMetricsDataController) Get(ctx context.Context, req *oceanengine.AdAdvertiserAccountMetricsDataGetReq) (res *oceanengine.AdAdvertiserAccountMetricsDataGetRes, err error) {
	res = new(oceanengine.AdAdvertiserAccountMetricsDataGetRes)
	res.AdAdvertiserAccountMetricsDataInfoRes, err = service.AdAdvertiserAccountMetricsData().GetById(ctx, req.Id)
	return
}

// Add 添加广告账户的指标数据
func (c *adAdvertiserAccountMetricsDataController) Add(ctx context.Context, req *oceanengine.AdAdvertiserAccountMetricsDataAddReq) (res *oceanengine.AdAdvertiserAccountMetricsDataAddRes, err error) {
	err = service.AdAdvertiserAccountMetricsData().Add(ctx, req.AdAdvertiserAccountMetricsDataAddReq)
	return
}

// Edit 修改广告账户的指标数据
func (c *adAdvertiserAccountMetricsDataController) Edit(ctx context.Context, req *oceanengine.AdAdvertiserAccountMetricsDataEditReq) (res *oceanengine.AdAdvertiserAccountMetricsDataEditRes, err error) {
	err = service.AdAdvertiserAccountMetricsData().Edit(ctx, req.AdAdvertiserAccountMetricsDataEditReq)
	return
}

// Delete 删除广告账户的指标数据
func (c *adAdvertiserAccountMetricsDataController) Delete(ctx context.Context, req *oceanengine.AdAdvertiserAccountMetricsDataDeleteReq) (res *oceanengine.AdAdvertiserAccountMetricsDataDeleteRes, err error) {
	err = service.AdAdvertiserAccountMetricsData().Delete(ctx, req.Ids)
	return
}

// AccountSubjectDataStatistics 账户主体数据统计
func (c *adAdvertiserAccountMetricsDataController) AccountSubjectDataStatistics(ctx context.Context, req *oceanengine.AccountSubjectDataStatisticsReq) (res *oceanengine.AccountSubjectDataStatisticsRes, err error) {
	res = new(oceanengine.AccountSubjectDataStatisticsRes)
	res.AccountSubjectDataStatisticsRes, err = service.AdAdvertiserAccountMetricsData().AccountSubjectDataStatistics(ctx, &req.AccountSubjectDataStatisticsReq)
	return
}
