// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-07-28 14:33:14
// 生成路径: internal/app/oceanengine/dao/internal/ad_advertiser_account_hour_metrics_data.go
// 生成人：cq
// desc:广告账户小时指标数据
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdAdvertiserAccountHourMetricsDataDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdAdvertiserAccountHourMetricsDataDao struct {
	table   string                                    // Table is the underlying table name of the DAO.
	group   string                                    // Group is the database configuration group name of current DAO.
	columns AdAdvertiserAccountHourMetricsDataColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdAdvertiserAccountHourMetricsDataColumns defines and stores column names for table ad_advertiser_account_hour_metrics_data.
type AdAdvertiserAccountHourMetricsDataColumns struct {
	AdvertiserId  string // 账户ID
	CreateDate    string // 创建日期
	Hour          string // 小时
	StatCost      string // 消耗
	StatPayAmount string // 付费金额
}

var adAdvertiserAccountHourMetricsDataColumns = AdAdvertiserAccountHourMetricsDataColumns{
	AdvertiserId:  "advertiser_id",
	CreateDate:    "create_date",
	Hour:          "hour",
	StatCost:      "stat_cost",
	StatPayAmount: "stat_pay_amount",
}

// NewAdAdvertiserAccountHourMetricsDataDao creates and returns a new DAO object for table data access.
func NewAdAdvertiserAccountHourMetricsDataDao() *AdAdvertiserAccountHourMetricsDataDao {
	return &AdAdvertiserAccountHourMetricsDataDao{
		group:   "default",
		table:   "ad_advertiser_account_hour_metrics_data",
		columns: adAdvertiserAccountHourMetricsDataColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdAdvertiserAccountHourMetricsDataDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdAdvertiserAccountHourMetricsDataDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdAdvertiserAccountHourMetricsDataDao) Columns() AdAdvertiserAccountHourMetricsDataColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdAdvertiserAccountHourMetricsDataDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdAdvertiserAccountHourMetricsDataDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdAdvertiserAccountHourMetricsDataDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
