package logic

import (
	_ "github.com/tiger1103/gfast/v3/internal/app/oceanengine/logic/adAccountSubjectDataStat"
	_ "github.com/tiger1103/gfast/v3/internal/app/oceanengine/logic/adAdvertiserAccount"

	_ "github.com/tiger1103/gfast/v3/internal/app/oceanengine/logic/adAdvertiserAccountHourMetricsData"

	_ "github.com/tiger1103/gfast/v3/internal/app/oceanengine/logic/adAdvertiserAccountMetricsData"

	_ "github.com/tiger1103/gfast/v3/internal/app/oceanengine/logic/adAdvertiserAccountTransactions"

	_ "github.com/tiger1103/gfast/v3/internal/app/oceanengine/logic/adAssetAudiencePackage"

	_ "github.com/tiger1103/gfast/v3/internal/app/oceanengine/logic/adAssetAudiencePackageProject"

	_ "github.com/tiger1103/gfast/v3/internal/app/oceanengine/logic/adAssetByteApplet"

	_ "github.com/tiger1103/gfast/v3/internal/app/oceanengine/logic/adAssetByteAppletLink"

	_ "github.com/tiger1103/gfast/v3/internal/app/oceanengine/logic/adAssetCustomAudience"

	_ "github.com/tiger1103/gfast/v3/internal/app/oceanengine/logic/adAssetDistricts"

	_ "github.com/tiger1103/gfast/v3/internal/app/oceanengine/logic/adAssetEvent"

	_ "github.com/tiger1103/gfast/v3/internal/app/oceanengine/logic/adAssetEventConfig"

	_ "github.com/tiger1103/gfast/v3/internal/app/oceanengine/logic/adAssetWechatApplet"

	_ "github.com/tiger1103/gfast/v3/internal/app/oceanengine/logic/adMajordomoAdvertiserAccount"

	_ "github.com/tiger1103/gfast/v3/internal/app/oceanengine/logic/adMaterialPromotion"

	_ "github.com/tiger1103/gfast/v3/internal/app/oceanengine/logic/adOptimizerDataStat"

	_ "github.com/tiger1103/gfast/v3/internal/app/oceanengine/logic/adProject"

	_ "github.com/tiger1103/gfast/v3/internal/app/oceanengine/logic/adProjectMetricsData"

	_ "github.com/tiger1103/gfast/v3/internal/app/oceanengine/logic/adPromotion"

	_ "github.com/tiger1103/gfast/v3/internal/app/oceanengine/logic/adPromotionMetricsData"

	_ "github.com/tiger1103/gfast/v3/internal/app/oceanengine/logic/adStrategyConfig"

	_ "github.com/tiger1103/gfast/v3/internal/app/oceanengine/logic/adStrategyCreativeMaterial"

	_ "github.com/tiger1103/gfast/v3/internal/app/oceanengine/logic/adStrategyGenerate"

	_ "github.com/tiger1103/gfast/v3/internal/app/oceanengine/logic/adStrategyLandingPage"

	_ "github.com/tiger1103/gfast/v3/internal/app/oceanengine/logic/adStrategyProjectConfig"

	_ "github.com/tiger1103/gfast/v3/internal/app/oceanengine/logic/adStrategyPromotionConfig"

	_ "github.com/tiger1103/gfast/v3/internal/app/oceanengine/logic/adStrategyTask"

	_ "github.com/tiger1103/gfast/v3/internal/app/oceanengine/logic/adStrategyTaskProject"

	_ "github.com/tiger1103/gfast/v3/internal/app/oceanengine/logic/adStrategyTaskPromotion"

	_ "github.com/tiger1103/gfast/v3/internal/app/oceanengine/logic/adStrategyTitlePackage"
)
