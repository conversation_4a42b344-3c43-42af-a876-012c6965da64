// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-11-16 10:33:41
// 生成路径: internal/app/oceanengine/logic/ad_promotion.go
// 生成人：cq
// desc:巨量广告表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/os/gtime"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/advertiser"
	toutiaoApi "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/api"
	toutiaoModel "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/model"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	toutiaoModels "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"strconv"
	"strings"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/dao"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdPromotion(New())
}

func New() service.IAdPromotion {
	return &sAdPromotion{}
}

type sAdPromotion struct{}

func (s *sAdPromotion) List(ctx context.Context, req *model.AdPromotionSearchReq) (listRes *model.AdPromotionSearchRes, err error) {
	listRes = new(model.AdPromotionSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdPromotion.Ctx(ctx).WithAll()
		userInfo := sysService.Context().GetLoginUser(ctx)
		userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		if !admin && len(userIds) > 0 {
			m = m.WhereIn(dao.AdPromotion.Columns().UserId, userIds)
		}
		if req.PromotionId != "" {
			m = m.Where(dao.AdPromotion.Columns().PromotionId+" = ?", req.PromotionId)
		}
		if req.PromotionName != "" {
			m = m.Where(dao.AdPromotion.Columns().PromotionName+" like ?", "%"+req.PromotionName+"%")
		}
		if req.AdvertiserId != "" {
			m = m.Where(dao.AdPromotion.Columns().AdvertiserId+" = ?", req.AdvertiserId)
		}
		if req.ProjectId != "" {
			m = m.Where(dao.AdPromotion.Columns().ProjectId+" = ?", req.ProjectId)
		}
		if req.Ids != nil && len(req.Ids) > 0 {
			m = m.WhereIn(dao.AdPromotion.Columns().PromotionId, req.Ids)
		}
		if req.Keyword != "" {
			m = m.WhereLike(dao.AdPromotion.Columns().PromotionName, "%"+req.Keyword+"%")
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id desc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdPromotionListRes
		err = m.Fields("id as id").
			Fields("project_id as projectId").
			Fields("advertiser_id as advertiserId").
			Fields("promotion_id as PromotionId").
			Fields("promotion_name as promotionName").
			Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdPromotionListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.AdPromotionListRes{
				Id:            v.Id,
				AdvertiserId:  v.AdvertiserId,
				ProjectId:     v.ProjectId,
				PromotionId:   v.PromotionId,
				PromotionName: v.PromotionName,
			}
		}
	})
	return
}

// GetNativeAnchor
func (s *sAdPromotion) GetNativeAnchor(ctx context.Context, req *model.GetNativeAnchorReq) (data *model.GetNativeAnchorRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		data = new(model.GetNativeAnchorRes)
		data.Data = make([]*model.GetNativeAnchor, 0)
		tokenRes, err1 := service.AdAdvertiserAccount().GetAccessTokenByAdvertiserId(ctx, req.AdvertiserId)
		liberr.ErrIsNil(ctx, err1, "获取access_token失败")
		aid := gconv.Int64(req.AdvertiserId)
		defaultType := toutiaoModels.MICRO_GAME_NativeAnchorGetV30FilteringLandingType
		res, err2 := advertiser.GetToutiaoApiClient().NativeAnchorGetV30ApiService.
			AccessToken(tokenRes.AccessToken).AdvertiserId(aid).Page(int32(req.Page)).PageSize(int32(req.Size)).Filtering(toutiaoModels.NativeAnchorGetV30Filtering{
			AnchorName:  &req.Keyword,
			LandingType: &defaultType,
		}).Do()
		liberr.ErrIsNil(ctx, err2)
		if res.Data != nil {
			for _, item := range res.Data.List {
				data.Data = append(data.Data, &model.GetNativeAnchor{
					AnchorID:           *item.AnchorId,
					AnchorName:         *item.AnchorName,
					AnchorShareType:    string(*item.AnchorShareType),
					AnchorType:         string(*item.AnchorType),
					AndroidPackageName: item.AndroidPackageName,
					CreateTime:         *item.CreateTime,
					IosPackageName:     item.IosPackageName,
					Source:             string(*item.Source),
					Status:             string(*item.Status),
				})
			}
		}

	})
	return
}

func (s *sAdPromotion) SelectDouyinAccount(ctx context.Context, m *model.SelectDouyinAccountReq) (data *model.SelectDouyinAccountRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		data = new(model.SelectDouyinAccountRes)
		data.Data = make([]*model.SelectDouyinAccount, 0)
		for _, id := range m.AdvertiserIds {
			tokenRes, err1 := service.AdAdvertiserAccount().GetAccessTokenByAdvertiserId(ctx, id)
			liberr.ErrIsNil(ctx, err1, "获取access_token失败")
			aid := gconv.Int64(id)
			res, err2 := advertiser.GetToutiaoApiClient().ToolsAwemeAuthListV2ApiService.
				AccessToken(tokenRes.AccessToken).
				SetRequest(toutiaoApi.ApiOpenApi2ToolsAwemeAuthListGetRequest{
					AdvertiserId: aid,
					Page:         1,
					PageSize:     50,
				}).Do()
			if err2 != nil {
				continue
			}
			if *res.Code == 0 {
				for _, item := range res.Data.List {
					data.Data = append(data.Data, &model.SelectDouyinAccount{
						//ID:           gconv.Int64(item.AwemeId),
						AdvertiserID: aid,
						AuthType:     string(*item.AuthType),
						AwemeID:      *item.AwemeId,
						AwemeName:    *item.AwemeName,
						AuthStatus:   string(*item.AuthStatus),
						//MainUserID:   item.,
					})
				}
			}
			// 限制接口调用频率
			time.Sleep(time.Millisecond * 500)
		}
	})
	return
}

func (s *sAdPromotion) GetToutiaoCreativeComponent(ctx context.Context, m *model.GetToutiaoCreativeComponentReq) (data *model.GetToutiaoCreativeComponentRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		data = new(model.GetToutiaoCreativeComponentRes)
		data.Data = make([]*model.GetToutiaoCreativeComponent, 0)
		tokenRes, err1 := service.AdAdvertiserAccount().GetAccessTokenByAdvertiserId(ctx, m.AdvertiserId)
		liberr.ErrIsNil(ctx, err1, "获取access_token失败")
		aid := gconv.Int64(m.AdvertiserId)
		if m.Size == 0 {
			m.Size = 40
		}
		if m.Page == 0 {
			m.Page = 1
		}
		before := advertiser.GetToutiaoApiClient().AssetsCreativeComponentGetV2ApiService.
			AccessToken(tokenRes.AccessToken).SetAId(aid).SetPageAndSize(m.Page, m.Size)
		filtering := &toutiaoModels.AssetsCreativeComponentGetV2Filtering{}
		if len(m.Keyword) > 0 {
			filtering.ComponentName = &m.Keyword
		}
		if len(m.ComponentTypes) > 0 {
			for _, componentType := range m.ComponentTypes {
				cType := models.AssetsCreativeComponentGetV2FilteringComponentTypes(componentType)
				filtering.ComponentTypes = append(filtering.ComponentTypes, &cType)
			}
		}
		before.SetFiltering(filtering)
		res, err2 := before.Do()
		if err2 != nil {
			return
		}
		if *res.Code == 0 {
			for _, item := range res.Data.List {
				data.Data = append(data.Data, &model.GetToutiaoCreativeComponent{
					ComponentType: *item.ComponentType,
					ComponentName: *item.ComponentName,
					Status:        *item.Status,
					CreateTime:    *item.CreateTime,
					ComponentID:   *item.ComponentId,
					ComponentData: item.ComponentData,
				})
			}
		}
	})
	return
}

// GetTaskList 给计划任务执行的list
func (s *sAdPromotion) GetTaskList(ctx context.Context, req *model.AdAdvertiserTaskSearchReq) (listRes *model.AdAdvertiserProjectTaskSearchRes, err error) {
	listRes = new(model.AdAdvertiserProjectTaskSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdPromotion.Ctx(ctx)
		if req.UserId > 0 {
			m = m.Where(dao.AdPromotion.Columns().UserId, req.UserId)
		}
		if req.AdStatus != "" {
			m = m.Where(dao.AdPromotion.Columns().Status, gconv.Int(req.AdStatus))
		}
		if len(req.Ids) > 0 {
			m = m.WhereIn(dao.AdPromotion.Columns().PromotionId, req.Ids)
		}
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.MaxPageSize
		}
		var res []*model.AdPromotionListRes
		err = m.Fields("id as id").
			Fields("advertiser_id as advertiserId").
			Fields("promotion_id as promotionId").
			Page(req.PageNum, req.PageSize).Order("id desc").Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdAdvertiserTaskListRes, 0)
		for _, item := range res {
			have := false
			for _, taskListRes := range listRes.List {
				if taskListRes.AdvertiserId == item.AdvertiserId {
					taskListRes.ObjectIds = append(taskListRes.ObjectIds, item.PromotionId)
					have = true
				}
			}
			if !have {
				listRes.List = append(listRes.List, &model.AdAdvertiserTaskListRes{
					AdvertiserId: item.AdvertiserId,
					ObjectIds:    []string{item.PromotionId},
				})
			}
		}
	})
	return
}

func (s *sAdPromotion) GetExportData(ctx context.Context, req *model.AdPromotionSearchReq) (listRes []*model.AdPromotionInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdPromotion.Ctx(ctx).WithAll()
		if req.PromotionId != "" {
			m = m.Where(dao.AdPromotion.Columns().PromotionId+" = ?", req.PromotionId)
		}
		if req.PromotionName != "" {
			m = m.Where(dao.AdPromotion.Columns().PromotionName+" like ?", "%"+req.PromotionName+"%")
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sAdPromotion) GetById(ctx context.Context, id int64) (res *model.AdPromotionInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdPromotion.Ctx(ctx).WithAll().Where(dao.AdPromotion.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdPromotion) GetByPromotionIds(ctx context.Context, promotionIds []string) (res []*model.AdPromotionInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdPromotion.Ctx(ctx).WithAll().Where(dao.AdPromotion.Columns().PromotionId, promotionIds).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdPromotion) GetPromotionNumByAdvertiserId(ctx context.Context, advertiserId string) (count int, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		tokenRes, err1 := service.AdAdvertiserAccount().GetAccessTokenByAdvertiserId(ctx, advertiserId)
		liberr.ErrIsNil(ctx, err1, "获取access_token失败")
		advertiserIdInt64 := gconv.Int64(advertiserId)
		createTime := gtime.Date()
		promotionListGetRes, err2 := advertiser.GetToutiaoApiClient().PromotionListV3ApiService.AccessToken(tokenRes.AccessToken).
			PromotionListGetV3Request(toutiaoModel.PromotionListGetV3Request{
				AdvertiserId: &advertiserIdInt64,
				Filtering: &models.PromotionListV30Filtering{
					PromotionCreateTime: &createTime,
				},
			}).Do()
		liberr.ErrIsNil(ctx, err2, "获取广告列表失败")
		if promotionListGetRes.Data != nil && promotionListGetRes.Data.PageInfo != nil {
			count = gconv.Int(*promotionListGetRes.Data.PageInfo.TotalNumber)
		}
	})
	return
}

func (s *sAdPromotion) GetPromotionNumByProjectId(ctx context.Context, projectId string, advertiserId string) (count int, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		tokenRes, err1 := service.AdAdvertiserAccount().GetAccessTokenByAdvertiserId(ctx, advertiserId)
		liberr.ErrIsNil(ctx, err1, "获取access_token失败")
		advertiserIdInt64 := gconv.Int64(advertiserId)
		projectIdInt64 := gconv.Int64(projectId)
		promotionListGetRes, err2 := advertiser.GetToutiaoApiClient().PromotionListV3ApiService.AccessToken(tokenRes.AccessToken).
			PromotionListGetV3Request(toutiaoModel.PromotionListGetV3Request{
				AdvertiserId: &advertiserIdInt64,
				Filtering: &models.PromotionListV30Filtering{
					ProjectId: &projectIdInt64,
				},
			}).Do()
		liberr.ErrIsNil(ctx, err2, "获取广告列表失败")
		if promotionListGetRes.Data != nil && promotionListGetRes.Data.PageInfo != nil {
			count = gconv.Int(*promotionListGetRes.Data.PageInfo.TotalNumber)
		}
	})
	return
}

func (s *sAdPromotion) Add(ctx context.Context, req *model.AdPromotionAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdPromotion.Ctx(ctx).Insert(do.AdPromotion{
			PromotionId:         req.PromotionId,
			PromotionName:       req.PromotionName,
			ProjectId:           req.ProjectId,
			AdvertiserId:        req.AdvertiserId,
			PromotionCreateTime: req.PromotionCreateTime,
			PromotionModifyTime: req.PromotionModifyTime,
			Status:              req.Status,
			OptStatus:           req.OptStatus,
			Budget:              req.Budget,
			BudgetMode:          req.BudgetMode,
			CpaBid:              req.CpaBid,
			DeepCpaBid:          req.DeepCpaBid,
			UserId:              req.UserId,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdPromotion) BatchAdd(ctx context.Context, batchAddReq []*model.AdPromotionAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		promotionIds := make([]string, 0)
		for _, v := range batchAddReq {
			promotionIds = append(promotionIds, v.PromotionId)
		}
		existAdPromotions, _ := s.GetByPromotionIds(ctx, promotionIds)
		addReq := make([]*do.AdPromotion, 0)
		for _, req := range batchAddReq {
			adPromotion := &do.AdPromotion{
				PromotionId:                  req.PromotionId,
				PromotionName:                req.PromotionName,
				ProjectId:                    req.ProjectId,
				AdvertiserId:                 req.AdvertiserId,
				PromotionCreateTime:          req.PromotionCreateTime,
				PromotionModifyTime:          req.PromotionModifyTime,
				Status:                       req.Status,
				OptStatus:                    req.OptStatus,
				Budget:                       req.Budget,
				BudgetMode:                   req.BudgetMode,
				CpaBid:                       req.CpaBid,
				DeepCpaBid:                   req.DeepCpaBid,
				LearningPhase:                req.LearningPhase,
				UserId:                       req.UserId,
				MajordomoAdvertiserAccountId: req.MajordomoAdvertiserAccountId,
			}
			for _, existAdPromotion := range existAdPromotions {
				if existAdPromotion.PromotionId == adPromotion.PromotionId {
					adPromotion.Id = existAdPromotion.Id
					break
				}
			}
			addReq = append(addReq, adPromotion)
		}
		_, err = dao.AdPromotion.Ctx(ctx).Save(addReq)
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdPromotion) Edit(ctx context.Context, req *model.AdPromotionEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdPromotion.Ctx(ctx).WherePri(req.Id).Update(do.AdPromotion{
			PromotionId:                  req.PromotionId,
			PromotionName:                req.PromotionName,
			ProjectId:                    req.ProjectId,
			AdvertiserId:                 req.AdvertiserId,
			PromotionCreateTime:          req.PromotionCreateTime,
			PromotionModifyTime:          req.PromotionModifyTime,
			Status:                       req.Status,
			OptStatus:                    req.OptStatus,
			Budget:                       req.Budget,
			BudgetMode:                   req.BudgetMode,
			CpaBid:                       req.CpaBid,
			DeepCpaBid:                   req.DeepCpaBid,
			UserId:                       req.UserId,
			MajordomoAdvertiserAccountId: req.MajordomoAdvertiserAccountId,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdPromotion) Delete(ctx context.Context, ids []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdPromotion.Ctx(ctx).Delete(dao.AdPromotion.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sAdPromotion) RunSyncAdPromotion(ctx context.Context, req *model.SyncAdPromotionReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if req.StartTime == "" && req.EndTime == "" {
			_, err = s.SyncAdPromotion(ctx, req.StartTime, req.AdvertiserIds, nil)
			liberr.ErrIsNil(ctx, err, "同步巨量广告计划失败")
		} else {
			startTime := req.StartTime
			endTime := req.EndTime
			for {
				if startTime > endTime {
					break
				}
				_, errors := s.SyncAdPromotion(ctx, req.StartTime, req.AdvertiserIds, nil)
				if errors != nil {
					g.Log().Error(ctx, errors)
				}
				startTime = libUtils.PlusDays(startTime, 1)
			}
		}
	})
	return
}

func (s *sAdPromotion) SyncAdPromotion(ctx context.Context, statDate string, advertiserIds []string, promotionIds []int64) (tokenMap map[string]string, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		tokenMap = make(map[string]string)
		for _, advertiserId := range advertiserIds {
			accessTokenRes, err1 := service.AdAdvertiserAccount().GetAccessTokenByAdvertiserId(ctx, advertiserId)
			if err1 != nil || accessTokenRes == nil || accessTokenRes.AccessToken == "" {
				g.Log().Errorf(ctx, fmt.Sprintf("广告主: %s, 获取access_token失败", advertiserId))
				continue
			}
			tokenMap[advertiserId] = accessTokenRes.AccessToken
			advertiserIdInt64, _ := strconv.ParseInt(advertiserId, 10, 64)
			var pageNo int64 = 1
			var pageSize int64 = 20
			for {
				request := toutiaoModel.PromotionListGetV3Request{
					AdvertiserId: &advertiserIdInt64,
					Page:         &pageNo,
					PageSize:     &pageSize,
				}
				var filtering = &models.PromotionListV30Filtering{}
				if statDate != "" {
					filtering.PromotionCreateTime = &statDate
				}
				if promotionIds != nil && len(promotionIds) > 0 {
					filtering.Ids = promotionIds
				}
				request.Filtering = filtering
				promotionListGetRes, err2 := advertiser.GetToutiaoApiClient().PromotionListV3ApiService.
					AccessToken(accessTokenRes.AccessToken).PromotionListGetV3Request(request).Do()
				if err2 != nil {
					g.Log().Error(ctx, fmt.Sprintf("获取广告项目列表失败: %v", err2))
					break
				}
				if promotionListGetRes.Data == nil || promotionListGetRes.Data.List == nil || len(promotionListGetRes.Data.List) == 0 {
					break
				}
				promotionList := make([]*model.AdPromotionAddReq, 0)
				for _, listInner := range promotionListGetRes.Data.List {
					adPromotionAdd := &model.AdPromotionAddReq{
						PromotionId:                  strconv.FormatInt(*listInner.PromotionId, 10),
						PromotionName:                *listInner.PromotionName,
						ProjectId:                    strconv.FormatInt(*listInner.ProjectId, 10),
						AdvertiserId:                 strconv.FormatInt(*listInner.AdvertiserId, 10),
						PromotionCreateTime:          *listInner.PromotionCreateTime,
						PromotionModifyTime:          *listInner.PromotionModifyTime,
						Status:                       gconv.String(listInner.Status),
						OptStatus:                    gconv.String(listInner.OptStatus),
						Budget:                       gconv.Float64(listInner.Budget),
						BudgetMode:                   gconv.String(listInner.BudgetMode),
						CpaBid:                       gconv.Float64(listInner.CpaBid),
						DeepCpaBid:                   gconv.Float64(listInner.DeepCpabid),
						LearningPhase:                gconv.String(listInner.LearningPhase),
						UserId:                       accessTokenRes.UserId,
						MajordomoAdvertiserAccountId: accessTokenRes.ParentId,
					}
					promotionList = append(promotionList, adPromotionAdd)
				}
				err3 := s.BatchAdd(ctx, promotionList)
				if err3 != nil {
					g.Log().Error(ctx, fmt.Sprintf("批量添加广告失败: %v", err3))
					break
				}
				if *promotionListGetRes.Data.PageInfo.TotalPage <= pageNo {
					break
				}
				pageNo++
			}
			// 更新广告计划同步时间
			_, err4 := dao.AdAdvertiserAccount.Ctx(ctx).Where(dao.AdAdvertiserAccount.Columns().AdvertiserId, advertiserId).
				Update(g.Map{
					"sync_promotion_at": gtime.Now(),
				})
			liberr.ErrIsNil(ctx, err4, "更新广告主广告计划同步时间失败")
		}
	})
	return
}

// GetAdPromotionCpaBid 批量获取广告的 CpaBid
func (s *sAdPromotion) GetAdPromotionCpaBid(ctx context.Context, promotionIds []int64) (cpaBidMap map[int64]float64, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		promotionList := make([]*model.AdPromotionInfoRes, 0)
		dao.AdPromotion.Ctx(ctx).WithAll().Fields(dao.AdPromotion.Columns().PromotionId, dao.AdPromotion.Columns().CpaBid).Where(dao.AdPromotion.Columns().Id+" in (?)", promotionIds).Scan(&promotionList)
		// 将promotionList 转化成 capBidMap
		cpaBidMap = make(map[int64]float64)
		for _, promotion := range promotionList {
			cpaBidMap[promotion.Id] = promotion.CpaBid
		}
	})
	return
}

// GetAdPromotionBudget 批量获取广告的预算Budget
func (s *sAdPromotion) GetAdPromotionBudget(ctx context.Context, promotionIds []int64) (budgetMap map[int64]float64, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		promotionList := make([]*model.AdPromotionInfoRes, 0)
		dao.AdPromotion.Ctx(ctx).WithAll().Fields(dao.AdPromotion.Columns().PromotionId, dao.AdPromotion.Columns().Budget).WhereIn(dao.AdPromotion.Columns().PromotionId, promotionIds).Scan(&promotionList)
		// 将promotionList 转化成 capBidMap
		budgetMap = make(map[int64]float64)
		for _, promotion := range promotionList {
			budgetMap[promotion.Id] = promotion.Budget
		}
	})
	return
}

func (s *sAdPromotion) GetAdPromotionName(ctx context.Context, promotionIds []string) (nameMap map[string]string, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		accounts := make([]*model.AdPromotionInfoRes, 0)
		dao.AdPromotion.Ctx(ctx).WithAll().Fields(dao.AdPromotion.Columns().PromotionId, dao.AdPromotion.Columns().PromotionName).WhereIn(dao.AdPromotion.Columns().PromotionId, promotionIds).Scan(&accounts)

		nameMap = make(map[string]string)
		for _, account := range accounts {
			nameMap[account.AdvertiserId] = account.PromotionName
		}
	})
	return
}

func (s *sAdPromotion) AdPromotionStatusUpdate(ctx context.Context, req *model.AdPromotionStatusUpdateReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var res = make([]*model.AdPromotionStatusUpdateRes, 0)
		err1 := dao.AdPromotion.Ctx(ctx).As("ap").
			LeftJoin("ad_majordomo_advertiser_account", "ama", "ap.majordomo_advertiser_account_id = ama.id").
			WhereIn("ap.promotion_id", req.PromotionIds).
			Fields("ap.advertiser_id as advertiserId").
			Fields("ama.access_token as accessToken").
			Fields("GROUP_CONCAT(ap.promotion_id) as promotionIds").
			Group("ap.advertiser_id, ama.access_token").
			Scan(&res)
		liberr.ErrIsNil(ctx, err1, "查询广告计划失败")
		updatePromotionIds := make([]string, 0)
		for _, v := range res {
			data := make([]*models.PromotionStatusUpdateV30RequestDataInner, 0)
			promotionIds := strings.Split(v.PromotionIds, ",")
			for _, promotionId := range promotionIds {
				data = append(data, &models.PromotionStatusUpdateV30RequestDataInner{
					OptStatus:   req.OptStatus,
					PromotionId: gconv.Int64(promotionId),
				})
			}
			request := models.PromotionStatusUpdateV30Request{
				AdvertiserId: gconv.Int64(v.AdvertiserId),
				Data:         data,
			}
			promotionStatusUpdateRes, err2 := advertiser.GetToutiaoApiClient().PromotionStatusUpdateV3ApiService.
				AccessToken(v.AccessToken).PromotionStatusUpdateV30Request(request).Do()
			liberr.ErrIsNil(ctx, err2, "更新广告计划状态失败")
			for _, promotionId := range promotionStatusUpdateRes.Data.PromotionIds {
				updatePromotionIds = append(updatePromotionIds, gconv.String(promotionId))
			}
			if len(updatePromotionIds) == 0 {
				continue
			}
			g.Log().Info(ctx, "更新广告计划状态成功: %+v", updatePromotionIds)
			var updateSucRes = make([]*model.AdProjectInfoRes, 0)
			err3 := dao.AdPromotion.Ctx(ctx).WhereIn(dao.AdPromotion.Columns().PromotionId, updatePromotionIds).Scan(&updateSucRes)
			liberr.ErrIsNil(ctx, err3, "查询广告计划失败")
			for _, v1 := range updateSucRes {
				v1.OptStatus = gconv.String(req.OptStatus)
			}
			_, err4 := dao.AdPromotion.Ctx(ctx).Save(updateSucRes)
			liberr.ErrIsNil(ctx, err4, "更新广告计划失败")
		}
	})
	return
}
