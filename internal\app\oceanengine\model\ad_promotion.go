// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2024-11-16 10:33:41
// 生成路径: internal/app/oceanengine/model/ad_promotion.go
// 生成人：cq
// desc:巨量广告表
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
)

// AdPromotionInfoRes is the golang structure for table ad_promotion.
type AdPromotionInfoRes struct {
	gmeta.Meta                   `orm:"table:ad_promotion"`
	Id                           int64       `orm:"id,primary" json:"id" dc:"ID"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        // ID
	PromotionId                  string      `orm:"promotion_id" json:"promotionId" dc:"广告ID"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           // 广告ID
	PromotionName                string      `orm:"promotion_name" json:"promotionName" dc:"广告名称"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       // 广告名称
	ProjectId                    string      `orm:"project_id" json:"projectId" dc:"项目ID"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               // 项目ID
	AdvertiserId                 string      `orm:"advertiser_id" json:"advertiserId" dc:"广告账户id"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       // 广告账户id
	PromotionCreateTime          string      `orm:"promotion_create_time" json:"promotionCreateTime" dc:"广告创建时间，格式yyyy-MM-dd HH:mm:ss"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                  // 广告创建时间，格式yyyy-MM-dd HH:mm:ss
	PromotionModifyTime          string      `orm:"promotion_modify_time" json:"promotionModifyTime" dc:"广告更新时间，格式yyyy-MM-dd HH:mm:ss"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                  // 广告更新时间，格式yyyy-MM-dd HH:mm:ss
	Status                       string      `orm:"status" json:"status" dc:"广告状态，OK 投放中 DELETED 已删除PROJECT_OFFLINE_BUDGET 项目超出预PROJECT_PREOFFLINE_BUDGET 项目接近预算TIME_NO_REACH 未到达投放时间 TIME_DONE 已完成 NO_SCHEDULE 不在投放时段 AUDIT 新建审核中 REAUDIT 修改审核中 FROZEN 已终止 AUDIT_DENY 审核不通过 OFFLINE_BUDGET 广告超出预算 OFFLINE_BALANCE 账户余额不足PREOFFLINE_BUDGET 广告接近预算 DISABLED 已暂停 PROJECT_DISABLED 已被项目暂停 LIVE_ROOM_OFF 关联直播间不可投PRODUCT_OFFLINE 关联商品不可投AWEME_ACCOUNT_DISABLED 关联抖音账号不可投AWEME_ANCHOR_DISABLED 锚点不可投DISABLE_BY_QUOTA 已暂停（配额达限）CREATE 新建 ADVERTISER_OFFLINE_BUDGET 账号超出预算ADVERTISER_PREOFFLINE_BUDGET 账号接近预算"` // 广告状态，OK 投放中 DELETED 已删除PROJECT_OFFLINE_BUDGET 项目超出预PROJECT_PREOFFLINE_BUDGET 项目接近预算TIME_NO_REACH 未到达投放时间 TIME_DONE 已完成 NO_SCHEDULE 不在投放时段 AUDIT 新建审核中 REAUDIT 修改审核中 FROZEN 已终止 AUDIT_DENY 审核不通过 OFFLINE_BUDGET 广告超出预算 OFFLINE_BALANCE 账户余额不足PREOFFLINE_BUDGET 广告接近预算 DISABLED 已暂停 PROJECT_DISABLED 已被项目暂停 LIVE_ROOM_OFF 关联直播间不可投PRODUCT_OFFLINE 关联商品不可投AWEME_ACCOUNT_DISABLED 关联抖音账号不可投AWEME_ANCHOR_DISABLED 锚点不可投DISABLE_BY_QUOTA 已暂停（配额达限）CREATE 新建 ADVERTISER_OFFLINE_BUDGET 账号超出预算ADVERTISER_PREOFFLINE_BUDGET 账号接近预算
	OptStatus                    string      `orm:"opt_status" json:"optStatus" dc:"操作状态，ENABLE 启用、DISABLE 暂停、DISABLE_BY_QUOTA暂停（投放额度不足）"`                                                                                                                                                                                                                                                                                                                                                                                                                                                               // 操作状态，ENABLE 启用、DISABLE 暂停、DISABLE_BY_QUOTA暂停（投放额度不足）
	Budget                       float64     `orm:"budget" json:"budget" dc:"预算"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        // 预算
	BudgetMode                   string      `orm:"budget_mode" json:"budgetMode" dc:"预算类型"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             // 预算类型
	CpaBid                       float64     `orm:"cpa_bid" json:"cpaBid" dc:"广告出价：目标转化出价/预期成本"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         // 广告出价：目标转化出价/预期成本
	DeepCpaBid                   float64     `orm:"deep_cpa_bid" json:"deepCpaBid" dc:"深度优化出价"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          // 深度优化出价
	LearningPhase                string      `orm:"learning_phase" json:"learningPhase"  dc:"学习期状态，枚举值：DEFAULT（默认，不在学习期中）、LEARNING（学习中）、LEARNED（学习成功）、LEARN_FAILED（学习失败)"`
	UserId                       int         `orm:"user_id" json:"userId" dc:"所属用户"` // 所属用户
	MajordomoAdvertiserAccountId int64       `orm:"majordomo_advertiser_account_id" json:"majordomoAdvertiserAccountId" dc:"管家账户ID"`
	CreatedAt                    *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"` // 创建时间
	UpdatedAt                    *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"` // 更新时间
	DeletedAt                    *gtime.Time `orm:"deleted_at" json:"deletedAt" dc:"删除时间"` // 删除时间
}

type AdPromotionListRes struct {
	Id            int64  `json:"id" dc:"ID"`
	ProjectId     string `json:"projectId" dc:"项目ID"`
	AdvertiserId  string `json:"advertiserId" dc:"广告账户id"`
	PromotionId   string `json:"promotionId" dc:"广告ID"`
	PromotionName string `json:"promotionName" dc:"广告名称"`
}

// AdPromotionSearchReq 分页请求参数
type AdPromotionSearchReq struct {
	comModel.PageReq
	PromotionId   string   `p:"promotionId" dc:"广告ID"`    //广告ID
	PromotionName string   `p:"promotionName" dc:"广告名称"`  //广告名称
	AdvertiserId  string   `p:"advertiserId" dc:"广告账户id"` //广告账户id
	ProjectId     string   `p:"projectId" dc:"项目ID"`
	Keyword       string   `p:"keyword" dc:"关键字"`
	Ids           []string `p:"ids" dc:"项目ID列表"` // 前端要求用ids
}

// AdPromotionSearchRes 列表返回结果
type AdPromotionSearchRes struct {
	comModel.ListRes
	List []*AdPromotionListRes `json:"list"`
}

type SelectDouyinAccountReq struct {
	// advertiser_ids
	AdvertiserIds []string `p:"advertiserIds" v:"required#广告账户id不能为空" dc:"广告账户id列表"`
}

type SelectDouyinAccountRes struct {
	// data
	Data []*SelectDouyinAccount `json:"data"`
}

type GetToutiaoCreativeComponentReq struct {
	AdvertiserId string `p:"advertiserId" dc:"广告账户id"` //广告账户id
	Keyword      string `p:"keyword" dc:"关键字"`
	//component_types
	ComponentTypes []string `p:"componentTypes" dc:"组件类型列表"`
	Page           int64    `p:"page"   dc:"页数"`
	Size           int64    `p:"size"   dc:"页数"`
}
type GetToutiaoCreativeComponentRes struct {
	Data []*GetToutiaoCreativeComponent `json:"data"`
}

type GetToutiaoCreativeComponent struct {
	ComponentType string                 `json:"component_type"`
	ComponentName string                 `json:"component_name"`
	Status        string                 `json:"status"`
	CreateTime    string                 `json:"create_time"`
	ComponentID   int64                  `json:"component_id"`
	ComponentData map[string]interface{} `json:"component_data,omitempty"`
}

type GetNativeAnchorReq struct {
	AdvertiserId string `p:"advertiserId" v:"required#广告账户id不能为空" dc:"广告账户id"`
	//keyword
	Keyword string `p:"keyword"  dc:"关键词"`
	Page    int    `p:"page"   dc:"页数"`
	Size    int    `p:"size"   dc:"页数"`
	//landing_type MICRO_GAME
	//LandingType string `p:"landingType"  dc:"广告类型"`
}

type GetNativeAnchorRes struct {
	Data []*GetNativeAnchor `json:"data"`
}

type GetNativeAnchor struct {
	AnchorID           string      `json:"anchor_id"`
	AnchorName         string      `json:"anchor_name"`
	AnchorShareType    string      `json:"anchor_share_type"`
	AnchorType         string      `json:"anchor_type"`
	AndroidPackageName interface{} `json:"android_package_name"`
	CreateTime         string      `json:"create_time"`
	IosPackageName     interface{} `json:"ios_package_name"`
	Source             string      `json:"source"`
	Status             string      `json:"status"`
	AnchorTypeText     string      `json:"anchor_type_text"`
}

type SelectDouyinAccount struct {
	ID           int64  `json:"id"`
	AdvertiserID int64  `json:"advertiser_id"`
	AuthType     string `json:"auth_type"`
	AwemeID      string `json:"aweme_id"`
	AwemeName    string `json:"aweme_name"`
	AuthStatus   string `json:"auth_status"`
	MainUserID   int64  `json:"main_user_id"`
}

// AdPromotionAddReq 添加操作请求参数
type AdPromotionAddReq struct {
	PromotionId                  string  `p:"promotionId"  dc:"广告ID"`
	PromotionName                string  `p:"promotionName" v:"required#广告名称不能为空" dc:"广告名称"`
	ProjectId                    string  `p:"projectId"  dc:"项目ID"`
	AdvertiserId                 string  `p:"advertiserId"  dc:"广告账户id"`
	PromotionCreateTime          string  `p:"promotionCreateTime"  dc:"广告创建时间，格式yyyy-MM-dd HH:mm:ss"`
	PromotionModifyTime          string  `p:"promotionModifyTime"  dc:"广告更新时间，格式yyyy-MM-dd HH:mm:ss"`
	Status                       string  `p:"status" v:"required#广告状态，OK 投放中 DELETED 已删除PROJECT_OFFLINE_BUDGET 项目超出预PROJECT_PREOFFLINE_BUDGET 项目接近预算TIME_NO_REACH 未到达投放时间 TIME_DONE 已完成 NO_SCHEDULE 不在投放时段 AUDIT 新建审核中 REAUDIT 修改审核中 FROZEN 已终止 AUDIT_DENY 审核不通过 OFFLINE_BUDGET 广告超出预算 OFFLINE_BALANCE 账户余额不足PREOFFLINE_BUDGET 广告接近预算 DISABLED 已暂停 PROJECT_DISABLED 已被项目暂停 LIVE_ROOM_OFF 关联直播间不可投PRODUCT_OFFLINE 关联商品不可投AWEME_ACCOUNT_DISABLED 关联抖音账号不可投AWEME_ANCHOR_DISABLED 锚点不可投DISABLE_BY_QUOTA 已暂停（配额达限）CREATE 新建 ADVERTISER_OFFLINE_BUDGET 账号超出预算ADVERTISER_PREOFFLINE_BUDGET 账号接近预算不能为空" dc:"广告状态，OK 投放中 DELETED 已删除PROJECT_OFFLINE_BUDGET 项目超出预PROJECT_PREOFFLINE_BUDGET 项目接近预算TIME_NO_REACH 未到达投放时间 TIME_DONE 已完成 NO_SCHEDULE 不在投放时段 AUDIT 新建审核中 REAUDIT 修改审核中 FROZEN 已终止 AUDIT_DENY 审核不通过 OFFLINE_BUDGET 广告超出预算 OFFLINE_BALANCE 账户余额不足PREOFFLINE_BUDGET 广告接近预算 DISABLED 已暂停 PROJECT_DISABLED 已被项目暂停 LIVE_ROOM_OFF 关联直播间不可投PRODUCT_OFFLINE 关联商品不可投AWEME_ACCOUNT_DISABLED 关联抖音账号不可投AWEME_ANCHOR_DISABLED 锚点不可投DISABLE_BY_QUOTA 已暂停（配额达限）CREATE 新建 ADVERTISER_OFFLINE_BUDGET 账号超出预算ADVERTISER_PREOFFLINE_BUDGET 账号接近预算"`
	OptStatus                    string  `p:"optStatus" v:"required#操作状态，ENABLE 启用、DISABLE 暂停、DISABLE_BY_QUOTA暂停（投放额度不足）不能为空" dc:"操作状态，ENABLE 启用、DISABLE 暂停、DISABLE_BY_QUOTA暂停（投放额度不足）"`
	Budget                       float64 `p:"budget"  dc:"预算"`
	BudgetMode                   string  `p:"budgetMode"  dc:"预算类型"`
	CpaBid                       float64 `p:"cpaBid"  dc:"广告出价：目标转化出价/预期成本"`
	DeepCpaBid                   float64 `p:"deepCpaBid"  dc:"深度优化出价"`
	LearningPhase                string  `p:"learningPhase"  dc:"学习期状态，枚举值：DEFAULT（默认，不在学习期中）、LEARNING（学习中）、LEARNED（学习成功）、LEARN_FAILED（学习失败)"`
	UserId                       int     `p:"userId"  dc:"所属用户"`
	MajordomoAdvertiserAccountId int64   `p:"majordomoAdvertiserAccountId" dc:"管家账户ID"`
}

// AdPromotionEditReq 修改操作请求参数
type AdPromotionEditReq struct {
	Id                           int64   `p:"id" v:"required#主键ID不能为空" dc:"ID"`
	PromotionId                  string  `p:"promotionId"  dc:"广告ID"`
	PromotionName                string  `p:"promotionName" v:"required#广告名称不能为空" dc:"广告名称"`
	ProjectId                    string  `p:"projectId"  dc:"项目ID"`
	AdvertiserId                 string  `p:"advertiserId"  dc:"广告账户id"`
	PromotionCreateTime          string  `p:"promotionCreateTime"  dc:"广告创建时间，格式yyyy-MM-dd HH:mm:ss"`
	PromotionModifyTime          string  `p:"promotionModifyTime"  dc:"广告更新时间，格式yyyy-MM-dd HH:mm:ss"`
	Status                       string  `p:"status" v:"required#广告状态，OK 投放中 DELETED 已删除PROJECT_OFFLINE_BUDGET 项目超出预PROJECT_PREOFFLINE_BUDGET 项目接近预算TIME_NO_REACH 未到达投放时间 TIME_DONE 已完成 NO_SCHEDULE 不在投放时段 AUDIT 新建审核中 REAUDIT 修改审核中 FROZEN 已终止 AUDIT_DENY 审核不通过 OFFLINE_BUDGET 广告超出预算 OFFLINE_BALANCE 账户余额不足PREOFFLINE_BUDGET 广告接近预算 DISABLED 已暂停 PROJECT_DISABLED 已被项目暂停 LIVE_ROOM_OFF 关联直播间不可投PRODUCT_OFFLINE 关联商品不可投AWEME_ACCOUNT_DISABLED 关联抖音账号不可投AWEME_ANCHOR_DISABLED 锚点不可投DISABLE_BY_QUOTA 已暂停（配额达限）CREATE 新建 ADVERTISER_OFFLINE_BUDGET 账号超出预算ADVERTISER_PREOFFLINE_BUDGET 账号接近预算不能为空" dc:"广告状态，OK 投放中 DELETED 已删除PROJECT_OFFLINE_BUDGET 项目超出预PROJECT_PREOFFLINE_BUDGET 项目接近预算TIME_NO_REACH 未到达投放时间 TIME_DONE 已完成 NO_SCHEDULE 不在投放时段 AUDIT 新建审核中 REAUDIT 修改审核中 FROZEN 已终止 AUDIT_DENY 审核不通过 OFFLINE_BUDGET 广告超出预算 OFFLINE_BALANCE 账户余额不足PREOFFLINE_BUDGET 广告接近预算 DISABLED 已暂停 PROJECT_DISABLED 已被项目暂停 LIVE_ROOM_OFF 关联直播间不可投PRODUCT_OFFLINE 关联商品不可投AWEME_ACCOUNT_DISABLED 关联抖音账号不可投AWEME_ANCHOR_DISABLED 锚点不可投DISABLE_BY_QUOTA 已暂停（配额达限）CREATE 新建 ADVERTISER_OFFLINE_BUDGET 账号超出预算ADVERTISER_PREOFFLINE_BUDGET 账号接近预算"`
	OptStatus                    string  `p:"optStatus" v:"required#操作状态，ENABLE 启用、DISABLE 暂停、DISABLE_BY_QUOTA暂停（投放额度不足）不能为空" dc:"操作状态，ENABLE 启用、DISABLE 暂停、DISABLE_BY_QUOTA暂停（投放额度不足）"`
	Budget                       float64 `p:"budget"  dc:"预算"`
	BudgetMode                   string  `p:"budgetMode"  dc:"预算类型"`
	CpaBid                       float64 `p:"cpaBid"  dc:"广告出价：目标转化出价/预期成本"`
	DeepCpaBid                   float64 `p:"deepCpaBid"  dc:"深度优化出价"`
	UserId                       int     `p:"userId"  dc:"所属用户"`
	MajordomoAdvertiserAccountId int64   `p:"majordomoAdvertiserAccountId" dc:"管家账户ID"`
}

type SyncAdPromotionReq struct {
	StartTime     string   `p:"startTime" dc:"开始时间 格式yyyy-MM-dd"`
	EndTime       string   `p:"endTime" dc:"结束时间 格式yyyy-MM-dd"`
	AdvertiserIds []string `p:"advertiserIds" dc:"广告主ID列表"`
}

type AdPromotionStatusUpdateReq struct {
	PromotionIds []string                                     `p:"promotionIds" v:"required#广告计划ID不能为空" dc:"广告计划ID列表"`
	OptStatus    models.PromotionStatusUpdateV30DataOptStatus `p:"optStatus" v:"required#目标操作，ENABLE 启用广告计划、DISABLE暂停广告计划不能为空" dc:"目标操作，ENABLE 启用项目、DISABLE暂停项目"`
}

type AdPromotionStatusUpdateRes struct {
	AdvertiserId string `json:"advertiserId" dc:"广告主ID"`
	AccessToken  string `json:"accessToken" dc:"用于验证权限的token"`
	PromotionIds string `json:"promotionIds" dc:"广告计划ID列表"`
}
