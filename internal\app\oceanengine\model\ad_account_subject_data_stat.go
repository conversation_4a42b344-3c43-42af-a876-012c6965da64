// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-07-23 16:32:30
// 生成路径: internal/app/oceanengine/model/ad_account_subject_data_stat.go
// 生成人：cq
// desc:账户主体数据统计
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdAccountSubjectDataStatInfoRes is the golang structure for table ad_account_subject_data_stat.
type AdAccountSubjectDataStatInfoRes struct {
	gmeta.Meta                  `orm:"table:ad_account_subject_data_stat"`
	CreateDate                  string  `orm:"create_date" json:"createDate" dc:"统计日期"`                                          // 统计日期
	AdvertiserCompany           string  `orm:"advertiser_company" json:"advertiserCompany" dc:"账户主体"`                            // 账户主体
	UserId                      int     `orm:"user_id" json:"userId" dc:"用户ID"`                                                  // 用户ID
	TotalAccounts               int     `orm:"total_accounts" json:"totalAccounts" dc:"总账户数"`                                    // 总账户数
	ActiveAccounts              int     `orm:"active_accounts" json:"activeAccounts" dc:"在投账户数"`                                 // 在投账户数
	StatCost                    float64 `orm:"stat_cost" json:"statCost" dc:"消耗"`                                                // 消耗
	StatPayAmount               float64 `orm:"stat_pay_amount" json:"statPayAmount" dc:"付费金额（回传时间）"`                             // 付费金额（回传时间）
	Active                      int     `orm:"active" json:"active" dc:"激活数"`                                                    // 激活数
	ClickCnt                    int     `orm:"click_cnt" json:"clickCnt" dc:"点击数"`                                               // 点击数
	ConvertCnt                  int     `orm:"convert_cnt" json:"convertCnt" dc:"转化数"`                                           // 转化数
	AttributionGameInAppLtv1Day float64 `orm:"attribution_game_in_app_ltv_1day" json:"attributionGameInAppLtv1Day" dc:"付费金额"`    // 付费金额
	AttributionMicroGame0DLtv   float64 `orm:"attribution_micro_game_0d_ltv" json:"attributionMicroGame0DLtv" dc:"小程序/小游戏当日LTV"` // 小程序/小游戏当日LTV
}

type AdAccountSubjectDataStatListRes struct {
	CreateDate                  string  `json:"createDate" dc:"统计日期"`
	AdvertiserCompany           string  `json:"advertiserCompany" dc:"账户主体"`
	UserId                      int     `json:"userId" dc:"归属人员ID"`
	UserName                    string  `json:"userName" dc:"归属人员"`
	TotalAccounts               int     `json:"totalAccounts" dc:"总账户数"`
	ActiveAccounts              int     `json:"activeAccounts" dc:"在投账户数"`
	StatCost                    float64 `json:"statCost" dc:"消耗"`
	StatPayAmount               float64 `json:"statPayAmount" dc:"付费金额（回传时间）"`
	PayAmountRoi                float64 `json:"payAmountRoi" dc:"付费ROI（回传时间）"`
	Active                      int64   `json:"active" dc:"激活数"`
	ActiveRate                  float64 `json:"activeRate" dc:"激活率"`
	ConvertCnt                  int64   `json:"convertCnt" dc:"转化数"`
	ConversionRate              float64 `json:"conversionRate" dc:"转化率"`
	AttributionGameInAppLtv1Day float64 `json:"attributionGameInAppLtv1Day" dc:"付费金额（激活用户当日付费金额）"`
	AttributionGameInAppRoi1Day float64 `json:"attributionGameInAppRoi1Day" dc:"付费ROI（激活用户当日付费RIO）"`
	AttributionMicroGame0DLtv   float64 `json:"attributionMicroGame0DLtv" dc:"小程序/小游戏当日LTV（激活用户当日LTV）"`
	AttributionMicroGame0DRoi   float64 `json:"attributionMicroGame0DRoi" dc:"小程序/小游戏当日广告变现ROI（激活用户当日广告变现ROI）"`
}

// AdAccountSubjectDataStatSearchReq 分页请求参数
type AdAccountSubjectDataStatSearchReq struct {
	comModel.PageReq
	StartTime           string   `p:"startTime" dc:"开始时间格式 yyyy-MM-dd"`
	EndTime             string   `p:"endTime" dc:"结束时间格式 yyyy-MM-dd"`
	AdvertiserCompanies []string `p:"advertiserCompanies"  dc:"账户主体列表"`
	UserIds             []int    `p:"userIds"  dc:"归属人员ID列表"`
	Merge               int      `p:"merge"  dc:"是否合并 0: 否 1: 是"`
}

// AdAccountSubjectDataStatSearchRes 列表返回结果
type AdAccountSubjectDataStatSearchRes struct {
	comModel.ListRes
	List    []*AdAccountSubjectDataStatListRes `json:"list"`
	Summary *AdAccountSubjectDataStatListRes   `json:"summary"`
}

// AdAccountSubjectDataStatAddReq 添加操作请求参数
type AdAccountSubjectDataStatAddReq struct {
	CreateDate                  string  `p:"createDate" v:"required#统计日期不能为空" dc:"统计日期"`
	AdvertiserCompany           string  `p:"advertiserCompany" v:"required#账户主体不能为空" dc:"账户主体"`
	UserId                      int     `p:"userId" v:"required#用户ID不能为空" dc:"用户ID"`
	TotalAccounts               int     `p:"totalAccounts"  dc:"总账户数"`
	ActiveAccounts              int     `p:"activeAccounts"  dc:"在投账户数"`
	StatCost                    float64 `p:"statCost"  dc:"消耗"`
	StatPayAmount               float64 `p:"statPayAmount"  dc:"付费金额（回传时间）"`
	Active                      int     `p:"active"  dc:"激活数"`
	ClickCnt                    int     `p:"clickCnt"  dc:"点击数"`
	ConvertCnt                  int     `p:"convertCnt"  dc:"转化数"`
	AttributionGameInAppLtv1Day float64 `p:"attributionGameInAppLtv1Day"  dc:"付费金额"`
	AttributionMicroGame0DLtv   float64 `p:"attributionMicroGame0DLtv"  dc:"小程序/小游戏当日LTV"`
}

// AdAccountSubjectDataStatEditReq 修改操作请求参数
type AdAccountSubjectDataStatEditReq struct {
	CreateDate                  string  `p:"createDate" v:"required#统计日期不能为空" dc:"统计日期"`
	AdvertiserCompany           string  `p:"advertiserCompany" v:"required#账户主体不能为空" dc:"账户主体"`
	UserId                      int     `p:"userId" v:"required#用户ID不能为空" dc:"用户ID"`
	TotalAccounts               int     `p:"totalAccounts"  dc:"总账户数"`
	ActiveAccounts              int     `p:"activeAccounts"  dc:"在投账户数"`
	StatCost                    float64 `p:"statCost"  dc:"消耗"`
	StatPayAmount               float64 `p:"statPayAmount"  dc:"付费金额（回传时间）"`
	Active                      int     `p:"active"  dc:"激活数"`
	ClickCnt                    int     `p:"clickCnt"  dc:"点击数"`
	ConvertCnt                  int     `p:"convertCnt"  dc:"转化数"`
	AttributionGameInAppLtv1Day float64 `p:"attributionGameInAppLtv1Day"  dc:"付费金额"`
	AttributionMicroGame0DLtv   float64 `p:"attributionMicroGame0DLtv"  dc:"小程序/小游戏当日LTV"`
}
